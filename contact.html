<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Magic Menu - Contact Us</title>

    <!-- Critical CSS -->
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/modern-base.css">
    <link rel="stylesheet" href="assets/css/modern-header.css">
    <link rel="stylesheet" href="assets/css/modern-contact.css">

    <!-- External Dependencies -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous">

    <!-- Favicons -->
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png">
    <link rel="manifest" href="assets/images/site.webmanifest">
    <meta name="theme-color" content="#ff7a00">

    <!-- SEO Meta Tags -->
    <meta name="description" content="Contact Magic Menu - Get in touch with us for food delivery inquiries, support, or feedback. We're here to help with your dining experience.">
    <meta name="keywords" content="contact, Magic Menu, food delivery, customer service, support, feedback, Nigerian cuisine">
    <meta name="author" content="Magic Menu">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Contact Magic Menu - Get in Touch">
    <meta property="og:description" content="Contact Magic Menu for food delivery inquiries, support, or feedback. We're here to help with your dining experience.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://magicmenu.com/contact">
    <meta property="og:image" content="https://magicmenu.com/assets/images/og-contact.jpg">
    <meta property="og:site_name" content="Magic Menu">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Contact Magic Menu - Get in Touch">
    <meta name="twitter:description" content="Contact Magic Menu for food delivery inquiries, support, or feedback. We're here to help with your dining experience.">
    <meta name="twitter:image" content="https://magicmenu.com/assets/images/og-contact.jpg">

    <!-- Configuration -->
    <meta name="ga-measurement-id" content="G-XXXXXXXXXX">
    <meta name="google-maps-key" content="YOUR_ACTUAL_API_KEY_HERE">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ContactPage",
        "name": "Contact Magic Menu",
        "description": "Contact page for Magic Menu food delivery service",
        "url": "https://magicmenu.com/contact",
        "mainEntity": {
            "@type": "FoodEstablishment",
            "name": "Magic Menu",
            "@id": "https://magicmenu.com",
            "address": {
                "@type": "PostalAddress",
                "streetAddress": "123 Foodie Street",
                "addressLocality": "Lagos",
                "addressRegion": "Lagos State",
                "postalCode": "100001",
                "addressCountry": "NG"
            },
            "telephone": "+234-************",
            "email": "<EMAIL>",
            "url": "https://magicmenu.com",
            "servesCuisine": "Nigerian",
            "priceRange": "$$",
            "openingHours": [
                "Mo-Fr 09:00-22:00",
                "Sa-Su 10:00-23:00"
            ],
            "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "+234-************",
                "contactType": "customer service",
                "availableLanguage": ["English", "Yoruba", "Igbo", "Hausa"]
            }
        }
    }
    </script>
</head>
<body>
    <!-- Skip Links for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <a href="#contact-form" class="skip-link">Skip to contact form</a>

    <!-- Modern Header -->
    <header class="header" role="banner">
        <div class="header-content">
            <a href="index.html" class="logo" aria-label="Magic Menu - Go to homepage">
                <img src="assets/images/logo.png" alt="Magic Menu Logo" width="40" height="40">
                <span class="logo-text">Magic Menu</span>
            </a>

            <nav class="main-nav" role="navigation" aria-label="Main navigation">
                <button class="mobile-menu-toggle" aria-label="Toggle navigation menu" aria-expanded="false">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
                <ul class="nav-list">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="menu.html" class="nav-link">Menu</a></li>
                    <li><a href="modern-cart.html" class="nav-link">Order</a></li>
                    <li><a href="account.html" class="nav-link">Account</a></li>
                    <li><a href="contact.html" class="nav-link active" aria-current="page">Contact</a></li>
                </ul>
            </nav>

            <div class="cart-icon-wrapper">
                <button class="cart-icon" aria-label="View cart" aria-describedby="cart-count">
                    <i class="fas fa-shopping-cart" aria-hidden="true"></i>
                    <span id="cart-count" class="cart-count">0</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="contact-hero" aria-labelledby="hero-title">
        <div class="hero-background">
            <div class="hero-pattern"></div>
        </div>
        <div class="container">
            <div class="hero-content">
                <h1 id="hero-title" class="hero-title" data-aos="fade-up">
                    Get in Touch
                </h1>
                <p class="hero-subtitle" data-aos="fade-up" data-aos-delay="100">
                    We'd love to hear from you. Send us a message and we'll respond as soon as possible.
                </p>
                <div class="hero-stats" data-aos="fade-up" data-aos-delay="200">
                    <div class="stat-item">
                        <span class="stat-number">2h</span>
                        <span class="stat-label">Avg Response</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label">Support</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">1000+</span>
                        <span class="stat-label">Happy Customers</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main id="main-content" class="main-content" role="main">
        <div class="container">
            <!-- Contact Methods Section -->
            <section class="contact-methods" aria-labelledby="contact-methods-title">
                <h2 id="contact-methods-title" class="section-title" data-aos="fade-up">
                    How to Reach Us
                </h2>
                <div class="contact-grid">
                    <div class="contact-card" data-aos="fade-up" data-aos-delay="100">
                        <div class="card-icon">
                            <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Our Location</h3>
                            <address class="card-address">
                                123 Foodie Street<br>
                                Victoria Island, Lagos<br>
                                Lagos State, Nigeria
                            </address>
                            <a href="https://maps.google.com/?q=123+Foodie+Street,+Victoria+Island,+Lagos,+Nigeria"
                               class="card-action"
                               target="_blank"
                               rel="noopener noreferrer"
                               aria-label="Open location in Google Maps">
                                <i class="fas fa-external-link-alt" aria-hidden="true"></i>
                                Get Directions
                            </a>
                        </div>
                    </div>

                    <div class="contact-card" data-aos="fade-up" data-aos-delay="200">
                        <div class="card-icon">
                            <i class="fas fa-phone" aria-hidden="true"></i>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Call Us</h3>
                            <p class="card-description">
                                Speak directly with our team for orders, inquiries, or immediate assistance.
                            </p>
                            <a href="tel:+2348012345678"
                               class="card-action"
                               aria-label="Call Magic Menu at +234 ************">
                                <i class="fas fa-phone" aria-hidden="true"></i>
                                +234 ************
                            </a>
                        </div>
                    </div>

                    <div class="contact-card" data-aos="fade-up" data-aos-delay="300">
                        <div class="card-icon">
                            <i class="fas fa-envelope" aria-hidden="true"></i>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Email Us</h3>
                            <p class="card-description">
                                Send us an email for feedback, partnerships, or detailed inquiries.
                            </p>
                            <a href="mailto:<EMAIL>"
                               class="card-action"
                               aria-label="Send <NAME_EMAIL>">
                                <i class="fas fa-envelope" aria-hidden="true"></i>
                                <EMAIL>
                            </a>
                        </div>
                    </div>

                    <div class="contact-card" data-aos="fade-up" data-aos-delay="400">
                        <div class="card-icon">
                            <i class="fas fa-clock" aria-hidden="true"></i>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Delivery Hours</h3>
                            <div class="hours-list">
                                <div class="hours-item">
                                    <span class="hours-day">Monday - Friday</span>
                                    <span class="hours-time">9:00 AM - 10:00 PM</span>
                                </div>
                                <div class="hours-item">
                                    <span class="hours-day">Saturday - Sunday</span>
                                    <span class="hours-time">10:00 AM - 11:00 PM</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Contact Form Section -->
            <section class="contact-form-section" aria-labelledby="contact-form-title">
                <div class="form-container" data-aos="fade-up" data-aos-delay="500">
                    <div class="form-header">
                        <h2 id="contact-form-title" class="form-title">Send Us a Message</h2>
                        <p class="form-description">
                            Have a question, feedback, or special request? We're here to help and would love to hear from you.
                        </p>
                    </div>

                    <form id="contact-form" class="contact-form" novalidate aria-labelledby="contact-form-title">
                        <!-- Form Progress Indicator -->
                        <div class="form-progress" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" aria-label="Form completion progress">
                            <div class="progress-bar"></div>
                        </div>

                        <!-- Personal Information -->
                        <fieldset class="form-fieldset">
                            <legend class="fieldset-legend">Personal Information</legend>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="customer-name" class="form-label required">
                                        <span class="label-text">Full Name</span>
                                        <span class="label-required" aria-label="required">*</span>
                                    </label>
                                    <div class="input-wrapper">
                                        <i class="input-icon fas fa-user" aria-hidden="true"></i>
                                        <input
                                            type="text"
                                            id="customer-name"
                                            name="customerName"
                                            class="form-input"
                                            required
                                            autocomplete="name"
                                            placeholder="Enter your full name"
                                            aria-describedby="customer-name-error customer-name-help"
                                            data-validation="required,minLength:2"
                                        >
                                    </div>
                                    <div class="input-help" id="customer-name-help">
                                        Please enter your first and last name
                                    </div>
                                    <div class="error-message" id="customer-name-error" role="alert" aria-live="polite"></div>
                                </div>

                                <div class="form-group">
                                    <label for="customer-email" class="form-label required">
                                        <span class="label-text">Email Address</span>
                                        <span class="label-required" aria-label="required">*</span>
                                    </label>
                                    <div class="input-wrapper">
                                        <i class="input-icon fas fa-envelope" aria-hidden="true"></i>
                                        <input
                                            type="email"
                                            id="customer-email"
                                            name="customerEmail"
                                            class="form-input"
                                            required
                                            autocomplete="email"
                                            placeholder="Enter your email address"
                                            aria-describedby="customer-email-error customer-email-help"
                                            data-validation="required,email"
                                        >
                                    </div>
                                    <div class="input-help" id="customer-email-help">
                                        We'll use this to respond to your message
                                    </div>
                                    <div class="error-message" id="customer-email-error" role="alert" aria-live="polite"></div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="customer-phone" class="form-label">
                                    <span class="label-text">Phone Number</span>
                                    <span class="label-optional">(optional)</span>
                                </label>
                                <div class="input-wrapper">
                                    <i class="input-icon fas fa-phone" aria-hidden="true"></i>
                                    <input
                                        type="tel"
                                        id="customer-phone"
                                        name="customerPhone"
                                        class="form-input"
                                        autocomplete="tel"
                                        placeholder="Enter your phone number"
                                        aria-describedby="customer-phone-error customer-phone-help"
                                        data-validation="phone"
                                    >
                                </div>
                                <div class="input-help" id="customer-phone-help">
                                    Optional - for urgent matters or follow-up calls
                                </div>
                                <div class="error-message" id="customer-phone-error" role="alert" aria-live="polite"></div>
                            </div>
                        </fieldset>

                        <!-- Message Details -->
                        <fieldset class="form-fieldset">
                            <legend class="fieldset-legend">Message Details</legend>

                            <div class="form-group">
                                <label for="inquiry-type" class="form-label">
                                    <span class="label-text">Inquiry Type</span>
                                </label>
                                <div class="input-wrapper">
                                    <i class="input-icon fas fa-list" aria-hidden="true"></i>
                                    <select
                                        id="inquiry-type"
                                        name="inquiryType"
                                        class="form-input form-select"
                                        aria-describedby="inquiry-type-help"
                                    >
                                        <option value="">Select inquiry type</option>
                                        <option value="order-support">Order Support</option>
                                        <option value="delivery-issue">Delivery Issue</option>
                                        <option value="feedback">Feedback</option>
                                        <option value="complaint">Complaint</option>
                                        <option value="partnership">Partnership</option>
                                        <option value="menu-inquiry">Menu Inquiry</option>
                                        <option value="technical-support">Technical Support</option>
                                        <option value="general">General Inquiry</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="input-help" id="inquiry-type-help">
                                    Help us route your message to the right team
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="customer-message" class="form-label required">
                                    <span class="label-text">Your Message</span>
                                    <span class="label-required" aria-label="required">*</span>
                                </label>
                                <div class="input-wrapper">
                                    <textarea
                                        id="customer-message"
                                        name="customerMessage"
                                        class="form-input form-textarea"
                                        required
                                        rows="6"
                                        placeholder="Tell us how we can help you..."
                                        aria-describedby="customer-message-error customer-message-help customer-message-count"
                                        data-validation="required,minLength:10,maxLength:1000"
                                        maxlength="1000"
                                    ></textarea>
                                </div>
                                <div class="input-help" id="customer-message-help">
                                    Please provide as much detail as possible to help us assist you better
                                </div>
                                <div class="character-count" id="customer-message-count" aria-live="polite">
                                    <span class="count-current">0</span>/<span class="count-max">1000</span> characters
                                </div>
                                <div class="error-message" id="customer-message-error" role="alert" aria-live="polite"></div>
                            </div>
                        </fieldset>

                        <!-- Form Actions -->
                        <div class="form-actions">
                            <button type="reset" class="btn btn-secondary" id="reset-form">
                                <i class="fas fa-undo" aria-hidden="true"></i>
                                <span class="btn-text">Reset Form</span>
                            </button>

                            <button type="submit" class="btn btn-primary btn-large" id="submit-form" disabled>
                                <span class="btn-text">Send Message</span>
                                <i class="btn-icon fas fa-paper-plane" aria-hidden="true"></i>
                                <div class="btn-loading">
                                    <div class="loading-spinner"></div>
                                </div>
                            </button>
                        </div>

                        <!-- Form Status -->
                        <div class="form-status" id="form-status" role="status" aria-live="polite" aria-atomic="true"></div>
                    </form>
                </div>
            </section>
            </div>

            <!-- Location Map Section -->
            <section class="map-section" aria-labelledby="map-title">
                <div class="map-container" data-aos="fade-up" data-aos-delay="600">
                    <div class="map-header">
                        <h2 id="map-title" class="map-title">Find Us</h2>
                        <p class="map-description">
                            Located in the heart of Victoria Island, Lagos. We deliver across Lagos and surrounding areas.
                        </p>
                    </div>

                    <div class="map-wrapper" id="map-container" role="img" aria-label="Interactive map showing Magic Menu location at 123 Foodie Street, Victoria Island, Lagos">
                        <div id="map-placeholder" class="map-placeholder">
                            <div class="placeholder-content">
                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                <h3>Magic Menu</h3>
                                <address>
                                    123 Foodie Street<br>
                                    Victoria Island, Lagos<br>
                                    Lagos State, Nigeria
                                </address>
                                <div class="map-loading" aria-live="polite">
                                    <div class="loading-spinner"></div>
                                    <span>Loading interactive map...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="map-actions">
                        <a href="https://maps.google.com/?q=123+Foodie+Street,+Victoria+Island,+Lagos,+Nigeria"
                           class="btn btn-outline"
                           target="_blank"
                           rel="noopener noreferrer"
                           aria-label="Open location in Google Maps (opens in new tab)">
                            <i class="fas fa-external-link-alt" aria-hidden="true"></i>
                            <span class="btn-text">Open in Google Maps</span>
                        </a>

                        <button class="btn btn-outline" id="get-directions" aria-label="Get directions to Magic Menu">
                            <i class="fas fa-route" aria-hidden="true"></i>
                            <span class="btn-text">Get Directions</span>
                        </button>
                    </div>
                </div>
            </section>
        </div>
    </main>


    <!-- Footer -->
    <footer class="footer">
        <div class="container footer-content">
            <div class="footer-section">
                <h4 class="footer-heading">Contact</h4>
                <address>
                    <p>123 Foodie Street<br>Victoria Island, Lagos</p>
                    <p>Phone: <a href="tel:+2348012345678">+234 ************</a></p>
                    <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </address>
            </div>
            <div class="footer-section">
                <h4 class="footer-heading">Delivery Hours</h4>
                <p>Mon-Fri: 9am - 10pm</p>
                <p>Sat-Sun: 10am - 11pm</p>
            </div>
            <div class="footer-section">
                <h4 class="footer-heading">Quick Links</h4>
                <ul class="footer-links">
                    <li><a href="terms.html" class="footer-link">Terms & Conditions</a></li>
                    <li><a href="privacy.html" class="footer-link">Privacy Policy</a></li>
                    <li><a href="faq.html" class="footer-link">FAQ</a></li>
                    <li><a href="about.html" class="footer-link">About Us</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h4 class="footer-heading">Follow Us</h4>
                <div class="social-links">
                    <a href="#" aria-label="Follow us on Facebook"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" aria-label="Follow us on Twitter"><i class="fab fa-twitter"></i></a>
                    <a href="#" aria-label="Follow us on Instagram"><i class="fab fa-instagram"></i></a>
                    <a href="#" aria-label="Follow us on WhatsApp"><i class="fab fa-whatsapp"></i></a>
                </div>
            </div>
        </div>
        <div class="copyright">
            © 2025 Magic Menu. All rights reserved.
        </div>
    </footer>

    <!-- Toast Container for Notifications -->
    <div id="toast-container" class="toast-container" aria-live="polite" aria-atomic="true"></div>

    <!-- Scripts -->
    <script type="module" src="assets/scripts/modern-contact.js"></script>
    <script>
        // Initialize animations if AOS is available
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 600,
                easing: 'ease-out-cubic',
                once: true,
                offset: 50
            });
        }
    </script>

    <!-- Modern Header Component -->
    <script type="module" src="assets/scripts/components/ModernHeader.js"></script>

    <!-- Enhanced Map Loading Script -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const mapContainer = document.getElementById('map-container');
            const mapPlaceholder = document.getElementById('map-placeholder');
            const apiKey = document.querySelector('meta[name="google-maps-key"]')?.content;

            // Enhanced map loading with accessibility
            function loadMap() {
                if (!apiKey || apiKey === 'YOUR_ACTUAL_API_KEY_HERE') {
                    // Remove loading indicator if no API key
                    const loadingElement = mapPlaceholder?.querySelector('.map-loading');
                    if (loadingElement) {
                        loadingElement.remove();
                    }
                    return;
                }

                try {
                    const restaurantAddress = '123 Foodie Street, Victoria Island, Lagos, Nigeria';
                    const iframe = document.createElement('iframe');

                    iframe.width = '100%';
                    iframe.height = '450';
                    iframe.style.border = '0';
                    iframe.loading = 'lazy';
                    iframe.allowFullscreen = true;
                    iframe.referrerPolicy = 'no-referrer-when-downgrade';
                    iframe.title = 'Interactive map showing Magic Menu location';
                    iframe.setAttribute('aria-label', 'Interactive map showing Magic Menu at 123 Foodie Street, Victoria Island, Lagos, Nigeria');

                    iframe.onload = () => {
                        console.log('Map loaded successfully');
                        // Announce to screen readers
                        const announcement = document.createElement('div');
                        announcement.setAttribute('aria-live', 'polite');
                        announcement.className = 'sr-only';
                        announcement.textContent = 'Interactive map has loaded successfully';
                        document.body.appendChild(announcement);
                        setTimeout(() => document.body.removeChild(announcement), 1000);
                    };

                    iframe.onerror = () => {
                        showMapError('Failed to load interactive map. Please try refreshing the page.');
                    };

                    iframe.src = `https://www.google.com/maps/embed/v1/place?key=${apiKey}&q=${encodeURIComponent(restaurantAddress)}&zoom=16`;

                    // Replace placeholder with iframe
                    mapContainer.innerHTML = '';
                    mapContainer.appendChild(iframe);

                } catch (error) {
                    console.error('Map loading error:', error);
                    showMapError('Error loading map. Please try refreshing the page.');
                }
            }

            function showMapError(message) {
                if (mapContainer) {
                    mapContainer.innerHTML = `
                        <div class="map-error" role="alert">
                            <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                            <h3>Map Unavailable</h3>
                            <p>${message}</p>
                            <button onclick="location.reload()" class="btn btn-outline btn-small" aria-label="Reload page to try loading map again">
                                <i class="fas fa-redo" aria-hidden="true"></i>
                                <span>Try Again</span>
                            </button>
                        </div>
                    `;
                }
            }

            // Get Directions button functionality
            const getDirectionsBtn = document.getElementById('get-directions');
            if (getDirectionsBtn) {
                getDirectionsBtn.addEventListener('click', () => {
                    if (navigator.geolocation) {
                        navigator.geolocation.getCurrentPosition(
                            (position) => {
                                const { latitude, longitude } = position.coords;
                                const destination = '123 Foodie Street, Victoria Island, Lagos, Nigeria';
                                const url = `https://www.google.com/maps/dir/${latitude},${longitude}/${encodeURIComponent(destination)}`;
                                window.open(url, '_blank', 'noopener,noreferrer');
                            },
                            () => {
                                // Fallback if geolocation fails
                                const destination = '123 Foodie Street, Victoria Island, Lagos, Nigeria';
                                const url = `https://www.google.com/maps/dir//${encodeURIComponent(destination)}`;
                                window.open(url, '_blank', 'noopener,noreferrer');
                            }
                        );
                    } else {
                        // Fallback for browsers without geolocation
                        const destination = '123 Foodie Street, Victoria Island, Lagos, Nigeria';
                        const url = `https://www.google.com/maps/dir//${encodeURIComponent(destination)}`;
                        window.open(url, '_blank', 'noopener,noreferrer');
                    }
                });
            }

            // Load map with delay for better performance
            setTimeout(loadMap, 1500);
        });

        // Global error handler for iframe errors
        window.addEventListener('error', function(e) {
            if (e.target && e.target.tagName === 'IFRAME') {
                const mapContainer = document.getElementById('map-container');
                if (mapContainer && mapContainer.contains(e.target)) {
                    mapContainer.innerHTML = `
                        <div class="map-error" role="alert">
                            <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                            <h3>Map Error</h3>
                            <p>Unable to load the interactive map. You can still view our location details above.</p>
                        </div>
                    `;
                }
            }
        }, true);
    </script>
</body>
</html>
