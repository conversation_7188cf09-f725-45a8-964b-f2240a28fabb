const express = require('express');
const Joi = require('joi');
const User = require('../models/User');
const JwtUtils = require('../utils/JwtUtils');
const AuthMiddleware = require('../middleware/auth');
const Cart = require('../models/Cart');

const router = express.Router();

// Validation schemas
const registerSchema = Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(12).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/).required(),
    firstName: Joi.string().min(2).max(50).required(),
    lastName: Joi.string().min(2).max(50).required(),
    phone: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).optional()
});

const loginSchema = Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
});

const refreshSchema = Joi.object({
    refreshToken: Joi.string().required()
});

const forgotPasswordSchema = Joi.object({
    email: Joi.string().email().required()
});

const resetPasswordSchema = Joi.object({
    token: Joi.string().required(),
    password: Joi.string().min(12).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/).required()
});

// Register new user
router.post('/register', AuthMiddleware.authRateLimit(3, 15 * 60 * 1000), async (req, res) => {
    try {
        // Validate input
        const { error, value } = registerSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                error: 'Validation failed',
                message: error.details[0].message
            });
        }

        const { email, password, firstName, lastName, phone } = value;

        // Check if user already exists
        const existingUser = await User.findByEmail(email);
        if (existingUser) {
            return res.status(409).json({
                error: 'Registration failed',
                message: 'User with this email already exists'
            });
        }

        // Create user
        const userData = {
            email: email.toLowerCase(),
            password,
            first_name: firstName,
            last_name: lastName,
            phone: phone || null,
            role: 'customer'
        };

        const user = await User.createUser(userData);

        // Generate tokens
        const tokens = JwtUtils.generateTokenPair(user);

        // Merge guest cart if exists
        if (req.session.guestId) {
            try {
                await Cart.mergeGuestCartWithUserCart(req.session.guestId, user.id);
                delete req.session.guestId;
            } catch (error) {
                console.error('Error merging guest cart:', error);
                // Don't fail registration for cart merge error
            }
        }

        res.status(201).json({
            message: 'Registration successful',
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                role: user.role,
                emailVerified: user.email_verified
            },
            tokens
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            error: 'Registration failed',
            message: 'Internal server error'
        });
    }
});

// Login user
router.post('/login', AuthMiddleware.authRateLimit(5, 15 * 60 * 1000), async (req, res) => {
    try {
        // Validate input
        const { error, value } = loginSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                error: 'Validation failed',
                message: error.details[0].message
            });
        }

        const { email, password } = value;

        // Find user
        const user = await User.findByEmail(email);
        if (!user) {
            return res.status(401).json({
                error: 'Authentication failed',
                message: 'Invalid email or password'
            });
        }

        // Check if user is active
        if (!user.is_active) {
            return res.status(401).json({
                error: 'Authentication failed',
                message: 'Account is inactive'
            });
        }

        // Verify password
        const isValidPassword = await User.verifyPassword(user, password);
        if (!isValidPassword) {
            return res.status(401).json({
                error: 'Authentication failed',
                message: 'Invalid email or password'
            });
        }

        // Update last login
        await User.updateLastLogin(user.id);

        // Generate tokens
        const tokens = JwtUtils.generateTokenPair(user);

        // Merge guest cart if exists
        if (req.session.guestId) {
            try {
                await Cart.mergeGuestCartWithUserCart(req.session.guestId, user.id);
                delete req.session.guestId;
            } catch (error) {
                console.error('Error merging guest cart:', error);
                // Don't fail login for cart merge error
            }
        }

        res.json({
            message: 'Login successful',
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                role: user.role,
                emailVerified: user.email_verified,
                lastLogin: user.last_login
            },
            tokens
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            error: 'Login failed',
            message: 'Internal server error'
        });
    }
});

// Refresh access token
router.post('/refresh', async (req, res) => {
    try {
        // Validate input
        const { error, value } = refreshSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                error: 'Validation failed',
                message: error.details[0].message
            });
        }

        const { refreshToken } = value;

        // Refresh token
        const newTokens = await JwtUtils.refreshAccessToken(refreshToken, User);

        res.json({
            message: 'Token refreshed successfully',
            tokens: newTokens
        });

    } catch (error) {
        console.error('Token refresh error:', error);
        res.status(401).json({
            error: 'Token refresh failed',
            message: error.message
        });
    }
});

// Logout user
router.post('/logout', AuthMiddleware.authenticate, async (req, res) => {
    try {
        // In a more sophisticated implementation, you would:
        // 1. Add token to blacklist
        // 2. Clear refresh token from database
        // 3. Clear session data

        // Clear session
        req.session.destroy((err) => {
            if (err) {
                console.error('Session destruction error:', err);
            }
        });

        res.json({
            message: 'Logout successful'
        });

    } catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({
            error: 'Logout failed',
            message: 'Internal server error'
        });
    }
});

// Get current user profile
router.get('/profile', AuthMiddleware.authenticate, async (req, res) => {
    try {
        const user = await User.getProfile(req.user.id);
        if (!user) {
            return res.status(404).json({
                error: 'User not found',
                message: 'User profile not found'
            });
        }

        res.json({
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                phone: user.phone,
                role: user.role,
                emailVerified: user.email_verified,
                lastLogin: user.last_login,
                createdAt: user.created_at
            }
        });

    } catch (error) {
        console.error('Profile fetch error:', error);
        res.status(500).json({
            error: 'Profile fetch failed',
            message: 'Internal server error'
        });
    }
});

// Update user profile
router.put('/profile', AuthMiddleware.authenticate, async (req, res) => {
    try {
        const updateSchema = Joi.object({
            firstName: Joi.string().min(2).max(50).optional(),
            lastName: Joi.string().min(2).max(50).optional(),
            phone: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).optional().allow('')
        });

        const { error, value } = updateSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                error: 'Validation failed',
                message: error.details[0].message
            });
        }

        const updateData = {};
        if (value.firstName) updateData.first_name = value.firstName;
        if (value.lastName) updateData.last_name = value.lastName;
        if (value.phone !== undefined) updateData.phone = value.phone || null;

        const updatedUser = await User.update(req.user.id, updateData);
        if (!updatedUser) {
            return res.status(404).json({
                error: 'User not found',
                message: 'User not found'
            });
        }

        res.json({
            message: 'Profile updated successfully',
            user: {
                id: updatedUser.id,
                email: updatedUser.email,
                firstName: updatedUser.first_name,
                lastName: updatedUser.last_name,
                phone: updatedUser.phone,
                role: updatedUser.role,
                emailVerified: updatedUser.email_verified
            }
        });

    } catch (error) {
        console.error('Profile update error:', error);
        res.status(500).json({
            error: 'Profile update failed',
            message: 'Internal server error'
        });
    }
});

// Verify email
router.post('/verify-email', async (req, res) => {
    try {
        const { token } = req.body;
        
        if (!token) {
            return res.status(400).json({
                error: 'Validation failed',
                message: 'Verification token is required'
            });
        }

        const user = await User.verifyEmail(token);
        if (!user) {
            return res.status(400).json({
                error: 'Verification failed',
                message: 'Invalid or expired verification token'
            });
        }

        res.json({
            message: 'Email verified successfully'
        });

    } catch (error) {
        console.error('Email verification error:', error);
        res.status(500).json({
            error: 'Email verification failed',
            message: 'Internal server error'
        });
    }
});

module.exports = router;
