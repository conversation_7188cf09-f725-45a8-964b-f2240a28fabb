import { api } from '../api.js';
import { Alert } from './Alert.js';
import { formatPrice } from '../config/currency.js';
import { cartManager } from './CartManager.js';

export class OrderManager {
    constructor() {
        this.currentOrder = null;
        this.isLoading = false;
        this.callbacks = [];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Listen for checkout form submissions
        document.addEventListener('submit', async (e) => {
            if (e.target.matches('.checkout-form')) {
                e.preventDefault();
                await this.handleCheckoutSubmit(e.target);
            }
        });

        // Listen for order tracking form submissions
        document.addEventListener('submit', async (e) => {
            if (e.target.matches('.order-tracking-form')) {
                e.preventDefault();
                await this.handleOrderTracking(e.target);
            }
        });
    }

    async createOrder(orderData) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        
        try {
            const response = await api.createOrder(orderData);
            this.currentOrder = response.order;
            
            this.notifyCallbacks('orderCreated', this.currentOrder);
            return response;
        } catch (error) {
            console.error('Failed to create order:', error);
            throw error;
        } finally {
            this.isLoading = false;
        }
    }

    async getOrder(orderId) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        
        try {
            const response = await api.getOrder(orderId);
            return response.order;
        } catch (error) {
            console.error('Failed to get order:', error);
            throw error;
        } finally {
            this.isLoading = false;
        }
    }

    async trackOrder(orderNumber) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        
        try {
            const response = await api.trackOrder(orderNumber);
            return response.order;
        } catch (error) {
            console.error('Failed to track order:', error);
            throw error;
        } finally {
            this.isLoading = false;
        }
    }

    async getOrderHistory(page = 1, limit = 10) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        
        try {
            const response = await api.getOrderHistory(page, limit);
            return response;
        } catch (error) {
            console.error('Failed to get order history:', error);
            throw error;
        } finally {
            this.isLoading = false;
        }
    }

    async handleCheckoutSubmit(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        
        // Show loading state
        submitBtn.textContent = 'Processing...';
        submitBtn.disabled = true;

        try {
            // Validate cart is not empty
            if (cartManager.isEmpty()) {
                Alert.show('Your cart is empty. Please add items before checkout.', 'error');
                return;
            }

            // Get form data
            const formData = new FormData(form);
            const orderData = this.buildOrderData(formData);

            // Create order
            const response = await this.createOrder(orderData);

            // Show success message
            Alert.show('Order placed successfully!', 'success');

            // Redirect to confirmation page
            setTimeout(() => {
                window.location.href = `/confirmation.html?orderId=${response.order.id}&orderNumber=${response.order.orderNumber}`;
            }, 1500);

        } catch (error) {
            console.error('Checkout failed:', error);
            Alert.show(error.message || 'Failed to place order. Please try again.', 'error');
        } finally {
            // Reset button state
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }

    async handleOrderTracking(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        const resultsContainer = document.querySelector('.tracking-results');
        
        // Show loading state
        submitBtn.textContent = 'Tracking...';
        submitBtn.disabled = true;

        try {
            const formData = new FormData(form);
            const orderNumber = formData.get('orderNumber');

            if (!orderNumber) {
                Alert.show('Please enter an order number', 'error');
                return;
            }

            const order = await this.trackOrder(orderNumber);
            this.displayTrackingResults(order, resultsContainer);

        } catch (error) {
            console.error('Order tracking failed:', error);
            Alert.show(error.message || 'Order not found. Please check your order number.', 'error');
            
            if (resultsContainer) {
                resultsContainer.innerHTML = `
                    <div class="tracking-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Order not found. Please check your order number and try again.</p>
                    </div>
                `;
            }
        } finally {
            // Reset button state
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }

    buildOrderData(formData) {
        const cart = cartManager.getCart();
        
        return {
            customerInfo: {
                name: formData.get('name') || `${formData.get('firstName')} ${formData.get('lastName')}`,
                email: formData.get('email'),
                phone: formData.get('phone'),
                address: {
                    street: formData.get('address') || formData.get('street'),
                    city: formData.get('city'),
                    state: formData.get('state'),
                    postalCode: formData.get('postalCode') || formData.get('zipCode'),
                    country: formData.get('country') || 'Nigeria'
                },
                specialInstructions: formData.get('specialInstructions') || formData.get('notes')
            },
            paymentInfo: {
                method: formData.get('paymentMethod') || 'cash',
                reference: formData.get('paymentReference') || null
            }
        };
    }

    displayTrackingResults(order, container) {
        if (!container) return;

        const statusSteps = [
            { key: 'pending', label: 'Order Received', icon: 'fas fa-receipt' },
            { key: 'confirmed', label: 'Order Confirmed', icon: 'fas fa-check-circle' },
            { key: 'preparing', label: 'Preparing', icon: 'fas fa-utensils' },
            { key: 'ready', label: 'Ready for Pickup/Delivery', icon: 'fas fa-box' },
            { key: 'delivered', label: 'Delivered', icon: 'fas fa-truck' }
        ];

        const currentStatusIndex = statusSteps.findIndex(step => step.key === order.status);
        
        container.innerHTML = `
            <div class="tracking-result">
                <div class="order-header">
                    <h3>Order #${order.orderNumber}</h3>
                    <div class="order-status status-${order.status}">${this.formatStatus(order.status)}</div>
                </div>
                
                <div class="order-timeline">
                    ${statusSteps.map((step, index) => `
                        <div class="timeline-step ${index <= currentStatusIndex ? 'completed' : ''} ${index === currentStatusIndex ? 'current' : ''}">
                            <div class="step-icon">
                                <i class="${step.icon}"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-title">${step.label}</div>
                                ${index === currentStatusIndex ? '<div class="step-time">Current Status</div>' : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
                
                <div class="order-details">
                    <div class="detail-row">
                        <span>Order Date:</span>
                        <span>${new Date(order.createdAt).toLocaleDateString()}</span>
                    </div>
                    <div class="detail-row">
                        <span>Total Amount:</span>
                        <span>${formatPrice(order.totalAmount)}</span>
                    </div>
                    ${order.estimatedDeliveryTime ? `
                        <div class="detail-row">
                            <span>Estimated Delivery:</span>
                            <span>${new Date(order.estimatedDeliveryTime).toLocaleString()}</span>
                        </div>
                    ` : ''}
                    ${order.deliveredAt ? `
                        <div class="detail-row">
                            <span>Delivered At:</span>
                            <span>${new Date(order.deliveredAt).toLocaleString()}</span>
                        </div>
                    ` : ''}
                </div>
                
                ${order.items && order.items.length > 0 ? `
                    <div class="order-items">
                        <h4>Items Ordered:</h4>
                        <div class="items-list">
                            ${order.items.map(item => `
                                <div class="order-item">
                                    <span class="item-name">${item.name}</span>
                                    <span class="item-quantity">x${item.quantity}</span>
                                    <span class="item-price">${formatPrice(item.totalPrice)}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    formatStatus(status) {
        const statusMap = {
            'pending': 'Order Received',
            'confirmed': 'Confirmed',
            'preparing': 'Being Prepared',
            'ready': 'Ready',
            'delivered': 'Delivered',
            'cancelled': 'Cancelled'
        };
        
        return statusMap[status] || status;
    }

    // Load order details for confirmation page
    async loadOrderConfirmation() {
        const urlParams = new URLSearchParams(window.location.search);
        const orderId = urlParams.get('orderId');
        const orderNumber = urlParams.get('orderNumber');

        if (!orderId && !orderNumber) {
            Alert.show('Invalid order reference', 'error');
            return;
        }

        try {
            let order;
            if (orderId) {
                order = await this.getOrder(orderId);
            } else {
                order = await this.trackOrder(orderNumber);
            }

            this.displayOrderConfirmation(order);
        } catch (error) {
            console.error('Failed to load order confirmation:', error);
            Alert.show('Failed to load order details', 'error');
        }
    }

    displayOrderConfirmation(order) {
        // Update order number
        const orderNumberElements = document.querySelectorAll('.order-number, .confirmation-number .value');
        orderNumberElements.forEach(el => {
            el.textContent = `#${order.orderNumber}`;
        });

        // Update order status
        const statusElements = document.querySelectorAll('.order-status');
        statusElements.forEach(el => {
            el.textContent = this.formatStatus(order.status);
            el.className = `order-status status-${order.status}`;
        });

        // Update totals
        const subtotalElements = document.querySelectorAll('.order-subtotal');
        const taxElements = document.querySelectorAll('.order-tax');
        const deliveryElements = document.querySelectorAll('.order-delivery');
        const totalElements = document.querySelectorAll('.order-total');

        subtotalElements.forEach(el => el.textContent = formatPrice(order.subtotal || 0));
        taxElements.forEach(el => el.textContent = formatPrice(order.tax || 0));
        deliveryElements.forEach(el => el.textContent = formatPrice(order.deliveryFee || 0));
        totalElements.forEach(el => el.textContent = formatPrice(order.totalAmount));

        // Update estimated delivery time
        if (order.estimatedDeliveryTime) {
            const deliveryElements = document.querySelectorAll('.estimated-delivery');
            const deliveryTime = new Date(order.estimatedDeliveryTime).toLocaleString();
            deliveryElements.forEach(el => el.textContent = deliveryTime);
        }

        // Update items list
        if (order.items && order.items.length > 0) {
            const itemsContainer = document.querySelector('.items-list');
            if (itemsContainer) {
                itemsContainer.innerHTML = order.items.map(item => `
                    <div class="confirmation-item">
                        <div class="item-details">
                            <span class="item-name">${item.name}</span>
                            <span class="item-quantity">Quantity: ${item.quantity}</span>
                        </div>
                        <span class="item-total">${formatPrice(item.totalPrice)}</span>
                    </div>
                `).join('');
            }
        }
    }

    // Callback management
    onOrderChange(callback) {
        this.callbacks.push(callback);
        
        // Return unsubscribe function
        return () => {
            const index = this.callbacks.indexOf(callback);
            if (index > -1) {
                this.callbacks.splice(index, 1);
            }
        };
    }

    notifyCallbacks(event, data = null) {
        this.callbacks.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Order callback error:', error);
            }
        });
    }

    // Public getters
    getCurrentOrder() {
        return this.currentOrder;
    }

    isProcessing() {
        return this.isLoading;
    }
}

// Create global order manager instance
export const orderManager = new OrderManager();

// Initialize order confirmation if on confirmation page
if (window.location.pathname.includes('confirmation')) {
    document.addEventListener('DOMContentLoaded', () => {
        orderManager.loadOrderConfirmation();
    });
}
