const express = require('express');
const Joi = require('joi');
const Contact = require('../models/Contact');
const AuthMiddleware = require('../middleware/auth');

const router = express.Router();

// Validation schemas
const contactSubmissionSchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    email: Joi.string().email().required(),
    phone: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).optional().allow(''),
    subject: Joi.string().min(5).max(200).required(),
    message: Joi.string().min(10).max(2000).required(),
    inquiryType: Joi.string().valid('general', 'complaint', 'suggestion', 'support', 'catering', 'partnership').optional()
});

const adminNotesSchema = Joi.object({
    notes: Joi.string().min(1).max(1000).required()
});

// Rate limiting for contact form submissions
const contactRateLimit = (req, res, next) => {
    const submissions = req.session.contactSubmissions || [];
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    
    // Clean old submissions
    const recentSubmissions = submissions.filter(time => now - time < oneHour);
    
    if (recentSubmissions.length >= 3) {
        return res.status(429).json({
            error: 'Too many submissions',
            message: 'You can only submit 3 contact forms per hour. Please try again later.'
        });
    }
    
    // Add current submission time
    recentSubmissions.push(now);
    req.session.contactSubmissions = recentSubmissions;
    
    next();
};

// PUBLIC ROUTES

// Submit contact form
router.post('/submit', contactRateLimit, async (req, res) => {
    try {
        const { error, value } = contactSubmissionSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                error: 'Validation failed',
                message: error.details[0].message
            });
        }

        const submission = await Contact.createSubmission(value);

        // In a real application, you might want to:
        // 1. Send email notification to admin
        // 2. Send confirmation email to user
        // 3. Log the submission for analytics

        res.status(201).json({
            message: 'Thank you for your message! We will get back to you soon.',
            submissionId: submission.id,
            submittedAt: submission.created_at
        });

    } catch (error) {
        console.error('Contact submission error:', error);
        res.status(500).json({
            error: 'Submission failed',
            message: 'We apologize, but there was an error processing your message. Please try again later.'
        });
    }
});

// Get contact form statistics (public - for display purposes)
router.get('/stats', async (req, res) => {
    try {
        const stats = await Contact.getSubmissionStats();
        
        // Return only non-sensitive statistics
        res.json({
            stats: {
                totalSubmissions: parseInt(stats.total_submissions),
                submissionsLast30Days: parseInt(stats.submissions_last_30_days),
                responseRate: stats.total_submissions > 0 
                    ? ((parseInt(stats.submissions_with_notes) / parseInt(stats.total_submissions)) * 100).toFixed(1)
                    : 0
            }
        });

    } catch (error) {
        console.error('Contact stats error:', error);
        res.status(500).json({
            error: 'Failed to fetch statistics',
            message: 'Internal server error'
        });
    }
});

// ADMIN ROUTES (require authentication and admin role)

// Get all contact submissions (admin)
router.get('/admin/submissions', 
    AuthMiddleware.authenticate, 
    AuthMiddleware.requireAdmin, 
    async (req, res) => {
        try {
            const { 
                page = 1, 
                limit = 20, 
                isRead, 
                inquiryType, 
                search,
                startDate,
                endDate 
            } = req.query;

            let result;
            
            if (search) {
                result = await Contact.searchSubmissions(search, parseInt(page), parseInt(limit));
            } else {
                const filters = {};
                if (isRead !== undefined) filters.isRead = isRead === 'true';
                if (inquiryType) filters.inquiryType = inquiryType;
                if (startDate && endDate) {
                    filters.startDate = new Date(startDate);
                    filters.endDate = new Date(endDate);
                }
                
                result = await Contact.getSubmissions(parseInt(page), parseInt(limit), filters);
            }

            const submissions = result.data.map(submission => ({
                id: submission.id,
                name: submission.name,
                email: submission.email,
                phone: submission.phone,
                subject: submission.subject,
                message: submission.message,
                inquiryType: submission.inquiry_type,
                isRead: submission.is_read,
                adminNotes: submission.admin_notes,
                createdAt: submission.created_at,
                updatedAt: submission.updated_at
            }));

            res.json({
                submissions,
                pagination: result.pagination
            });

        } catch (error) {
            console.error('Get submissions error:', error);
            res.status(500).json({
                error: 'Failed to fetch submissions',
                message: 'Internal server error'
            });
        }
    }
);

// Get single submission (admin)
router.get('/admin/submissions/:id', 
    AuthMiddleware.authenticate, 
    AuthMiddleware.requireAdmin, 
    async (req, res) => {
        try {
            const { id } = req.params;
            
            if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
                return res.status(400).json({
                    error: 'Invalid ID format',
                    message: 'Submission ID must be a valid UUID'
                });
            }

            const submission = await Contact.findById(id);
            if (!submission) {
                return res.status(404).json({
                    error: 'Submission not found',
                    message: 'The requested submission does not exist'
                });
            }

            // Mark as read when admin views it
            if (!submission.is_read) {
                await Contact.markAsRead(id, true);
                submission.is_read = true;
            }

            res.json({
                submission: {
                    id: submission.id,
                    name: submission.name,
                    email: submission.email,
                    phone: submission.phone,
                    subject: submission.subject,
                    message: submission.message,
                    inquiryType: submission.inquiry_type,
                    isRead: submission.is_read,
                    adminNotes: submission.admin_notes,
                    createdAt: submission.created_at,
                    updatedAt: submission.updated_at
                }
            });

        } catch (error) {
            console.error('Get submission error:', error);
            res.status(500).json({
                error: 'Failed to fetch submission',
                message: 'Internal server error'
            });
        }
    }
);

// Mark submission as read/unread (admin)
router.patch('/admin/submissions/:id/read', 
    AuthMiddleware.authenticate, 
    AuthMiddleware.requireAdmin, 
    async (req, res) => {
        try {
            const { id } = req.params;
            const { isRead } = req.body;
            
            if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
                return res.status(400).json({
                    error: 'Invalid ID format',
                    message: 'Submission ID must be a valid UUID'
                });
            }

            if (typeof isRead !== 'boolean') {
                return res.status(400).json({
                    error: 'Validation failed',
                    message: 'isRead must be a boolean value'
                });
            }

            const updatedSubmission = await Contact.markAsRead(id, isRead);

            res.json({
                message: `Submission marked as ${isRead ? 'read' : 'unread'}`,
                submission: {
                    id: updatedSubmission.id,
                    isRead: updatedSubmission.is_read,
                    updatedAt: updatedSubmission.updated_at
                }
            });

        } catch (error) {
            console.error('Mark as read error:', error);
            
            if (error.message.includes('not found')) {
                return res.status(404).json({
                    error: 'Submission not found',
                    message: 'The requested submission does not exist'
                });
            }
            
            res.status(500).json({
                error: 'Failed to update submission',
                message: 'Internal server error'
            });
        }
    }
);

// Add admin notes to submission (admin)
router.patch('/admin/submissions/:id/notes', 
    AuthMiddleware.authenticate, 
    AuthMiddleware.requireAdmin, 
    async (req, res) => {
        try {
            const { id } = req.params;
            
            if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
                return res.status(400).json({
                    error: 'Invalid ID format',
                    message: 'Submission ID must be a valid UUID'
                });
            }

            const { error, value } = adminNotesSchema.validate(req.body);
            if (error) {
                return res.status(400).json({
                    error: 'Validation failed',
                    message: error.details[0].message
                });
            }

            const { notes } = value;
            const updatedSubmission = await Contact.addAdminNotes(id, notes, req.user.id);

            res.json({
                message: 'Admin notes added successfully',
                submission: {
                    id: updatedSubmission.id,
                    adminNotes: updatedSubmission.admin_notes,
                    isRead: updatedSubmission.is_read,
                    updatedAt: updatedSubmission.updated_at
                }
            });

        } catch (error) {
            console.error('Add admin notes error:', error);
            
            if (error.message.includes('not found')) {
                return res.status(404).json({
                    error: 'Submission not found',
                    message: 'The requested submission does not exist'
                });
            }
            
            res.status(500).json({
                error: 'Failed to add admin notes',
                message: 'Internal server error'
            });
        }
    }
);

// Get contact submission statistics (admin)
router.get('/admin/stats', 
    AuthMiddleware.authenticate, 
    AuthMiddleware.requireAdmin, 
    async (req, res) => {
        try {
            const { startDate, endDate } = req.query;
            
            const stats = await Contact.getSubmissionStats(
                startDate ? new Date(startDate) : null,
                endDate ? new Date(endDate) : null
            );

            res.json({
                stats: {
                    totalSubmissions: parseInt(stats.total_submissions),
                    readSubmissions: parseInt(stats.read_submissions),
                    unreadSubmissions: parseInt(stats.unread_submissions),
                    generalInquiries: parseInt(stats.general_inquiries),
                    complaints: parseInt(stats.complaints),
                    suggestions: parseInt(stats.suggestions),
                    supportRequests: parseInt(stats.support_requests),
                    submissionsWithNotes: parseInt(stats.submissions_with_notes),
                    submissionsLast7Days: parseInt(stats.submissions_last_7_days),
                    submissionsLast30Days: parseInt(stats.submissions_last_30_days),
                    responseRate: stats.total_submissions > 0 
                        ? ((parseInt(stats.submissions_with_notes) / parseInt(stats.total_submissions)) * 100).toFixed(1)
                        : 0
                }
            });

        } catch (error) {
            console.error('Get contact stats error:', error);
            res.status(500).json({
                error: 'Failed to fetch contact statistics',
                message: 'Internal server error'
            });
        }
    }
);

// Bulk mark as read (admin)
router.patch('/admin/submissions/bulk/read', 
    AuthMiddleware.authenticate, 
    AuthMiddleware.requireAdmin, 
    async (req, res) => {
        try {
            const { submissionIds } = req.body;
            
            if (!Array.isArray(submissionIds) || submissionIds.length === 0) {
                return res.status(400).json({
                    error: 'Validation failed',
                    message: 'submissionIds must be a non-empty array'
                });
            }

            // Validate all IDs are UUIDs
            for (const id of submissionIds) {
                if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
                    return res.status(400).json({
                        error: 'Invalid ID format',
                        message: 'All submission IDs must be valid UUIDs'
                    });
                }
            }

            await Contact.bulkMarkAsRead(submissionIds);

            res.json({
                message: `${submissionIds.length} submissions marked as read`,
                processedCount: submissionIds.length
            });

        } catch (error) {
            console.error('Bulk mark as read error:', error);
            res.status(500).json({
                error: 'Failed to bulk update submissions',
                message: 'Internal server error'
            });
        }
    }
);

module.exports = router;
