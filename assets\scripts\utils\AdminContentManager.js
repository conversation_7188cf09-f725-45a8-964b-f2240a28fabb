import { api } from '../api.js';
import { Alert } from './Alert.js';

export class AdminContentManager {
    constructor() {
        this.content = {
            pages: [],
            promotions: [],
            announcements: [],
            settings: {}
        };
        this.currentView = 'pages';
        this.isLoading = false;
        this.editor = null;
        
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadContent();
    }

    setupEventListeners() {
        // View switching
        document.addEventListener('click', (e) => {
            if (e.target.matches('.content-tab')) {
                this.switchView(e.target.dataset.view);
            }
        });

        // Content actions
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.add-content-btn')) {
                e.preventDefault();
                this.showContentEditor(e.target.dataset.type);
            }
            
            if (e.target.matches('.edit-content-btn')) {
                e.preventDefault();
                this.editContent(e.target.dataset.type, e.target.dataset.id);
            }
            
            if (e.target.matches('.delete-content-btn')) {
                e.preventDefault();
                await this.deleteContent(e.target.dataset.type, e.target.dataset.id);
            }
            
            if (e.target.matches('.toggle-status-btn')) {
                e.preventDefault();
                await this.toggleContentStatus(e.target.dataset.type, e.target.dataset.id);
            }
        });

        // Form submissions
        document.addEventListener('submit', async (e) => {
            if (e.target.matches('#content-form')) {
                e.preventDefault();
                await this.handleContentSubmit(e.target);
            }
            
            if (e.target.matches('#settings-form')) {
                e.preventDefault();
                await this.handleSettingsSubmit(e.target);
            }
        });

        // Rich text editor initialization
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeEditor();
        });
    }

    async loadContent() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading(true);

        try {
            // Load all content types
            const [pages, promotions, announcements, settings] = await Promise.all([
                this.loadPages(),
                this.loadPromotions(),
                this.loadAnnouncements(),
                this.loadSettings()
            ]);

            this.content = {
                pages: pages || [],
                promotions: promotions || [],
                announcements: announcements || [],
                settings: settings || {}
            };

            this.renderCurrentView();

        } catch (error) {
            console.error('Failed to load content:', error);
            Alert.show('Failed to load content', 'error');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    async loadPages() {
        try {
            const response = await api.getContentPages();
            return response.pages;
        } catch (error) {
            console.error('Failed to load pages:', error);
            return [];
        }
    }

    async loadPromotions() {
        try {
            const response = await api.getPromotions();
            return response.promotions;
        } catch (error) {
            console.error('Failed to load promotions:', error);
            return [];
        }
    }

    async loadAnnouncements() {
        try {
            const response = await api.getAnnouncements();
            return response.announcements;
        } catch (error) {
            console.error('Failed to load announcements:', error);
            return [];
        }
    }

    async loadSettings() {
        try {
            const response = await api.getSystemSettings();
            return response.settings;
        } catch (error) {
            console.error('Failed to load settings:', error);
            return {};
        }
    }

    switchView(view) {
        this.currentView = view;
        
        // Update active tab
        document.querySelectorAll('.content-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');
        
        this.renderCurrentView();
    }

    renderCurrentView() {
        const container = document.querySelector('#content-management-area');
        if (!container) return;

        switch (this.currentView) {
            case 'pages':
                this.renderPagesView(container);
                break;
            case 'promotions':
                this.renderPromotionsView(container);
                break;
            case 'announcements':
                this.renderAnnouncementsView(container);
                break;
            case 'settings':
                this.renderSettingsView(container);
                break;
            default:
                this.renderPagesView(container);
        }
    }

    renderPagesView(container) {
        container.innerHTML = `
            <div class="content-header">
                <h3>Page Content Management</h3>
                <button class="btn btn-primary add-content-btn" data-type="page">
                    <i class="fas fa-plus"></i> Add Page
                </button>
            </div>
            
            <div class="content-grid">
                ${this.content.pages.map(page => this.renderPageCard(page)).join('')}
            </div>
            
            ${this.content.pages.length === 0 ? `
                <div class="empty-state">
                    <i class="fas fa-file-alt"></i>
                    <h3>No pages found</h3>
                    <p>Create your first page to get started.</p>
                </div>
            ` : ''}
        `;
    }

    renderPageCard(page) {
        return `
            <div class="content-card">
                <div class="card-header">
                    <h4>${page.title}</h4>
                    <div class="card-actions">
                        <button class="btn btn-sm edit-content-btn" data-type="page" data-id="${page.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm toggle-status-btn" 
                                data-type="page" data-id="${page.id}"
                                title="${page.isActive ? 'Deactivate' : 'Activate'}">
                            <i class="fas fa-${page.isActive ? 'eye-slash' : 'eye'}"></i>
                        </button>
                        <button class="btn btn-sm btn-danger delete-content-btn" data-type="page" data-id="${page.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <p class="page-slug">/${page.slug}</p>
                    <p class="page-description">${page.description || ''}</p>
                    <div class="page-meta">
                        <span class="status-badge ${page.isActive ? 'active' : 'inactive'}">
                            ${page.isActive ? 'Published' : 'Draft'}
                        </span>
                        <span class="update-date">Updated: ${new Date(page.updatedAt).toLocaleDateString()}</span>
                    </div>
                </div>
            </div>
        `;
    }

    renderPromotionsView(container) {
        container.innerHTML = `
            <div class="content-header">
                <h3>Promotions Management</h3>
                <button class="btn btn-primary add-content-btn" data-type="promotion">
                    <i class="fas fa-plus"></i> Add Promotion
                </button>
            </div>
            
            <div class="promotions-table">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Discount</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.content.promotions.map(promo => this.renderPromotionRow(promo)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    renderPromotionRow(promotion) {
        return `
            <tr>
                <td>
                    <div class="promotion-info">
                        <strong>${promotion.title}</strong>
                        <p class="promotion-description">${promotion.description || ''}</p>
                    </div>
                </td>
                <td>
                    <span class="type-badge type-${promotion.type}">${promotion.type}</span>
                </td>
                <td>
                    <span class="discount-value">
                        ${promotion.discountType === 'percentage' ? promotion.discountValue + '%' : '₦' + promotion.discountValue}
                    </span>
                </td>
                <td>${new Date(promotion.startDate).toLocaleDateString()}</td>
                <td>${new Date(promotion.endDate).toLocaleDateString()}</td>
                <td>
                    <span class="status-badge ${promotion.isActive ? 'active' : 'inactive'}">
                        ${promotion.isActive ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm edit-content-btn" data-type="promotion" data-id="${promotion.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm toggle-status-btn" data-type="promotion" data-id="${promotion.id}">
                            <i class="fas fa-${promotion.isActive ? 'pause' : 'play'}"></i>
                        </button>
                        <button class="btn btn-sm btn-danger delete-content-btn" data-type="promotion" data-id="${promotion.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    renderAnnouncementsView(container) {
        container.innerHTML = `
            <div class="content-header">
                <h3>Announcements Management</h3>
                <button class="btn btn-primary add-content-btn" data-type="announcement">
                    <i class="fas fa-plus"></i> Add Announcement
                </button>
            </div>
            
            <div class="announcements-list">
                ${this.content.announcements.map(announcement => this.renderAnnouncementCard(announcement)).join('')}
            </div>
        `;
    }

    renderAnnouncementCard(announcement) {
        return `
            <div class="announcement-card ${announcement.priority}">
                <div class="announcement-header">
                    <h4>${announcement.title}</h4>
                    <div class="announcement-meta">
                        <span class="priority-badge priority-${announcement.priority}">${announcement.priority}</span>
                        <span class="status-badge ${announcement.isActive ? 'active' : 'inactive'}">
                            ${announcement.isActive ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                </div>
                <div class="announcement-content">
                    <p>${announcement.content}</p>
                    <div class="announcement-details">
                        <span>Start: ${new Date(announcement.startDate).toLocaleDateString()}</span>
                        <span>End: ${new Date(announcement.endDate).toLocaleDateString()}</span>
                    </div>
                </div>
                <div class="announcement-actions">
                    <button class="btn btn-sm edit-content-btn" data-type="announcement" data-id="${announcement.id}">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-sm toggle-status-btn" data-type="announcement" data-id="${announcement.id}">
                        <i class="fas fa-${announcement.isActive ? 'pause' : 'play'}"></i>
                        ${announcement.isActive ? 'Deactivate' : 'Activate'}
                    </button>
                    <button class="btn btn-sm btn-danger delete-content-btn" data-type="announcement" data-id="${announcement.id}">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `;
    }

    renderSettingsView(container) {
        const settings = this.content.settings;
        
        container.innerHTML = `
            <div class="settings-header">
                <h3>System Settings</h3>
            </div>
            
            <form id="settings-form" class="settings-form">
                <div class="settings-section">
                    <h4>Restaurant Information</h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="restaurant-name">Restaurant Name</label>
                            <input type="text" id="restaurant-name" name="restaurantName" 
                                   value="${settings.restaurantName || 'Magic Menu'}" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="restaurant-phone">Phone Number</label>
                            <input type="tel" id="restaurant-phone" name="restaurantPhone" 
                                   value="${settings.restaurantPhone || ''}">
                        </div>
                        
                        <div class="form-group">
                            <label for="restaurant-email">Email Address</label>
                            <input type="email" id="restaurant-email" name="restaurantEmail" 
                                   value="${settings.restaurantEmail || ''}">
                        </div>
                        
                        <div class="form-group full-width">
                            <label for="restaurant-address">Address</label>
                            <textarea id="restaurant-address" name="restaurantAddress" rows="3">${settings.restaurantAddress || ''}</textarea>
                        </div>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h4>Operating Hours</h4>
                    <div class="hours-grid">
                        ${this.renderOperatingHours(settings.operatingHours || {})}
                    </div>
                </div>
                
                <div class="settings-section">
                    <h4>Order Settings</h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="min-order-amount">Minimum Order Amount (₦)</label>
                            <input type="number" id="min-order-amount" name="minOrderAmount" 
                                   value="${settings.minOrderAmount || 0}" min="0" step="100">
                        </div>
                        
                        <div class="form-group">
                            <label for="delivery-fee">Delivery Fee (₦)</label>
                            <input type="number" id="delivery-fee" name="deliveryFee" 
                                   value="${settings.deliveryFee || 500}" min="0" step="50">
                        </div>
                        
                        <div class="form-group">
                            <label for="tax-rate">Tax Rate (%)</label>
                            <input type="number" id="tax-rate" name="taxRate" 
                                   value="${settings.taxRate || 7.5}" min="0" max="100" step="0.1">
                        </div>
                        
                        <div class="form-group">
                            <label for="estimated-prep-time">Estimated Prep Time (minutes)</label>
                            <input type="number" id="estimated-prep-time" name="estimatedPrepTime" 
                                   value="${settings.estimatedPrepTime || 30}" min="5" step="5">
                        </div>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h4>Website Settings</h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="site-title">Site Title</label>
                            <input type="text" id="site-title" name="siteTitle" 
                                   value="${settings.siteTitle || 'Magic Menu - Delicious Nigerian Cuisine'}">
                        </div>
                        
                        <div class="form-group full-width">
                            <label for="site-description">Site Description</label>
                            <textarea id="site-description" name="siteDescription" rows="3">${settings.siteDescription || ''}</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="allowGuestOrders" 
                                       ${settings.allowGuestOrders !== false ? 'checked' : ''}>
                                Allow Guest Orders
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="enableNotifications" 
                                       ${settings.enableNotifications !== false ? 'checked' : ''}>
                                Enable Email Notifications
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="settings-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="location.reload()">
                        <i class="fas fa-undo"></i> Reset
                    </button>
                </div>
            </form>
        `;
    }

    renderOperatingHours(hours) {
        const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        
        return days.map((day, index) => {
            const dayHours = hours[day] || { open: '09:00', close: '22:00', closed: false };
            
            return `
                <div class="hours-day">
                    <label class="day-label">${dayNames[index]}</label>
                    <div class="hours-controls">
                        <label>
                            <input type="checkbox" name="${day}.closed" ${dayHours.closed ? 'checked' : ''}>
                            Closed
                        </label>
                        <input type="time" name="${day}.open" value="${dayHours.open}" 
                               ${dayHours.closed ? 'disabled' : ''}>
                        <span>to</span>
                        <input type="time" name="${day}.close" value="${dayHours.close}" 
                               ${dayHours.closed ? 'disabled' : ''}>
                    </div>
                </div>
            `;
        }).join('');
    }

    showContentEditor(type, content = null) {
        const modal = document.createElement('div');
        modal.className = 'modal content-editor-modal';
        modal.innerHTML = this.getEditorHTML(type, content);
        
        document.body.appendChild(modal);
        modal.classList.add('active');
        
        // Initialize rich text editor if needed
        if (type === 'page') {
            this.initializeRichTextEditor();
        }
        
        // Close modal handlers
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => {
                modal.remove();
            });
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    getEditorHTML(type, content) {
        switch (type) {
            case 'page':
                return this.getPageEditorHTML(content);
            case 'promotion':
                return this.getPromotionEditorHTML(content);
            case 'announcement':
                return this.getAnnouncementEditorHTML(content);
            default:
                return '<div>Unknown content type</div>';
        }
    }

    getPageEditorHTML(page) {
        return `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${page ? 'Edit Page' : 'Add New Page'}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="content-form" data-type="page" ${page ? `data-id="${page.id}"` : ''}>
                        <div class="form-group">
                            <label for="page-title">Page Title</label>
                            <input type="text" id="page-title" name="title" 
                                   value="${page?.title || ''}" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="page-slug">URL Slug</label>
                            <input type="text" id="page-slug" name="slug" 
                                   value="${page?.slug || ''}" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="page-description">Description</label>
                            <textarea id="page-description" name="description" rows="3">${page?.description || ''}</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="page-content">Content</label>
                            <textarea id="page-content" name="content" rows="10">${page?.content || ''}</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="isActive" ${page?.isActive !== false ? 'checked' : ''}>
                                Published
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary modal-close">Cancel</button>
                    <button class="btn btn-primary" onclick="document.getElementById('content-form').dispatchEvent(new Event('submit'))">
                        ${page ? 'Update' : 'Create'} Page
                    </button>
                </div>
            </div>
        `;
    }

    async handleContentSubmit(form) {
        const formData = new FormData(form);
        const type = form.dataset.type;
        const id = form.dataset.id;
        
        try {
            const data = Object.fromEntries(formData.entries());
            
            // Handle checkboxes
            data.isActive = formData.has('isActive');
            
            let response;
            if (id) {
                response = await this.updateContent(type, id, data);
            } else {
                response = await this.createContent(type, data);
            }
            
            Alert.show(`${type} ${id ? 'updated' : 'created'} successfully`, 'success');
            
            // Close modal and refresh
            document.querySelector('.content-editor-modal').remove();
            await this.loadContent();
            
        } catch (error) {
            console.error('Content submission failed:', error);
            Alert.show(`Failed to ${id ? 'update' : 'create'} ${type}`, 'error');
        }
    }

    async createContent(type, data) {
        switch (type) {
            case 'page':
                return await api.createContentPage(data);
            case 'promotion':
                return await api.createPromotion(data);
            case 'announcement':
                return await api.createAnnouncement(data);
            default:
                throw new Error('Unknown content type');
        }
    }

    async updateContent(type, id, data) {
        switch (type) {
            case 'page':
                return await api.updateContentPage(id, data);
            case 'promotion':
                return await api.updatePromotion(id, data);
            case 'announcement':
                return await api.updateAnnouncement(id, data);
            default:
                throw new Error('Unknown content type');
        }
    }

    async deleteContent(type, id) {
        if (!confirm(`Are you sure you want to delete this ${type}?`)) {
            return;
        }
        
        try {
            switch (type) {
                case 'page':
                    await api.deleteContentPage(id);
                    break;
                case 'promotion':
                    await api.deletePromotion(id);
                    break;
                case 'announcement':
                    await api.deleteAnnouncement(id);
                    break;
            }
            
            Alert.show(`${type} deleted successfully`, 'success');
            await this.loadContent();
            
        } catch (error) {
            console.error('Delete failed:', error);
            Alert.show(`Failed to delete ${type}`, 'error');
        }
    }

    async toggleContentStatus(type, id) {
        try {
            let content;
            switch (type) {
                case 'page':
                    content = this.content.pages.find(p => p.id === id);
                    break;
                case 'promotion':
                    content = this.content.promotions.find(p => p.id === id);
                    break;
                case 'announcement':
                    content = this.content.announcements.find(a => a.id === id);
                    break;
            }
            
            if (!content) return;
            
            await this.updateContent(type, id, { isActive: !content.isActive });
            Alert.show(`${type} ${content.isActive ? 'deactivated' : 'activated'}`, 'success');
            await this.loadContent();
            
        } catch (error) {
            console.error('Status toggle failed:', error);
            Alert.show('Failed to update status', 'error');
        }
    }

    initializeRichTextEditor() {
        // Initialize rich text editor (could use TinyMCE, CKEditor, etc.)
        const textarea = document.querySelector('#page-content');
        if (textarea && window.tinymce) {
            tinymce.init({
                target: textarea,
                height: 400,
                menubar: false,
                plugins: 'advlist autolink lists link image charmap print preview anchor',
                toolbar: 'undo redo | formatselect | bold italic backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help'
            });
        }
    }

    showLoading(show) {
        const loader = document.querySelector('.content-loader');
        if (loader) {
            loader.style.display = show ? 'block' : 'none';
        }
    }
}

// Create global admin content manager instance
export const adminContentManager = new AdminContentManager();
