const BaseModel = require('./BaseModel');

class MenuCategory extends BaseModel {
    constructor() {
        super('menu_categories');
    }

    // Get all active categories with item counts
    async getAllWithItemCounts() {
        try {
            const query = `
                SELECT 
                    mc.*,
                    COUNT(mi.id) as item_count,
                    COUNT(mi.id) FILTER (WHERE mi.is_available = true) as available_item_count
                FROM menu_categories mc
                LEFT JOIN menu_items mi ON mc.id = mi.category_id
                WHERE mc.is_active = true
                GROUP BY mc.id
                ORDER BY mc.display_order, mc.name
            `;
            return await this.db.query(query);
        } catch (error) {
            console.error('Error getting categories with item counts:', error);
            throw error;
        }
    }

    // Get category with its menu items
    async getCategoryWithItems(categoryId) {
        try {
            const query = `
                SELECT 
                    mc.*,
                    json_agg(
                        json_build_object(
                            'id', mi.id,
                            'name', mi.name,
                            'description', mi.description,
                            'price', mi.price,
                            'image_url', mi.image_url,
                            'is_available', mi.is_available,
                            'is_featured', mi.is_featured,
                            'preparation_time', mi.preparation_time,
                            'calories', mi.calories,
                            'allergens', mi.allergens,
                            'ingredients', mi.ingredients,
                            'display_order', mi.display_order
                        ) ORDER BY mi.display_order, mi.name
                    ) FILTER (WHERE mi.id IS NOT NULL) as items
                FROM menu_categories mc
                LEFT JOIN menu_items mi ON mc.id = mi.category_id AND mi.is_available = true
                WHERE mc.id = $1 AND mc.is_active = true
                GROUP BY mc.id
            `;
            const result = await this.db.query(query, [categoryId]);
            return result[0] || null;
        } catch (error) {
            console.error('Error getting category with items:', error);
            throw error;
        }
    }

    // Create category with validation
    async createCategory(categoryData) {
        try {
            // Validate required fields
            if (!categoryData.name) {
                throw new Error('Category name is required');
            }

            // Check if category name already exists
            const existingCategory = await this.findOne({ name: categoryData.name });
            if (existingCategory) {
                throw new Error('Category name already exists');
            }

            // Set display order if not provided
            if (!categoryData.display_order) {
                const maxOrderResult = await this.db.query(
                    'SELECT COALESCE(MAX(display_order), 0) + 1 as next_order FROM menu_categories'
                );
                categoryData.display_order = maxOrderResult[0].next_order;
            }

            return await this.create(categoryData);
        } catch (error) {
            console.error('Error creating category:', error);
            throw error;
        }
    }

    // Update category with validation
    async updateCategory(categoryId, categoryData) {
        try {
            // Check if category exists
            const existingCategory = await this.findById(categoryId);
            if (!existingCategory) {
                throw new Error('Category not found');
            }

            // Check if new name conflicts with existing categories
            if (categoryData.name && categoryData.name !== existingCategory.name) {
                const nameConflict = await this.findOne({ name: categoryData.name });
                if (nameConflict && nameConflict.id !== categoryId) {
                    throw new Error('Category name already exists');
                }
            }

            return await this.update(categoryId, categoryData);
        } catch (error) {
            console.error('Error updating category:', error);
            throw error;
        }
    }

    // Delete category (soft delete by setting is_active to false)
    async deleteCategory(categoryId) {
        try {
            // Check if category has menu items
            const itemCount = await this.db.query(
                'SELECT COUNT(*) as count FROM menu_items WHERE category_id = $1',
                [categoryId]
            );

            if (itemCount[0].count > 0) {
                // Soft delete - just deactivate
                return await this.update(categoryId, { is_active: false });
            } else {
                // Hard delete if no items
                return await this.delete(categoryId);
            }
        } catch (error) {
            console.error('Error deleting category:', error);
            throw error;
        }
    }

    // Reorder categories
    async reorderCategories(categoryOrders) {
        try {
            const client = await this.db.pool.connect();
            
            try {
                await client.query('BEGIN');
                
                for (const { id, display_order } of categoryOrders) {
                    await client.query(
                        'UPDATE menu_categories SET display_order = $1 WHERE id = $2',
                        [display_order, id]
                    );
                }
                
                await client.query('COMMIT');
                return true;
            } catch (error) {
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            console.error('Error reordering categories:', error);
            throw error;
        }
    }

    // Get category statistics
    async getCategoryStats() {
        try {
            const query = `
                SELECT 
                    mc.id,
                    mc.name,
                    COUNT(mi.id) as total_items,
                    COUNT(mi.id) FILTER (WHERE mi.is_available = true) as available_items,
                    COUNT(mi.id) FILTER (WHERE mi.is_featured = true) as featured_items,
                    AVG(mi.price) as average_price,
                    MIN(mi.price) as min_price,
                    MAX(mi.price) as max_price
                FROM menu_categories mc
                LEFT JOIN menu_items mi ON mc.id = mi.category_id
                WHERE mc.is_active = true
                GROUP BY mc.id, mc.name
                ORDER BY mc.display_order, mc.name
            `;
            return await this.db.query(query);
        } catch (error) {
            console.error('Error getting category stats:', error);
            throw error;
        }
    }

    // Get categories for public menu (only active categories with available items)
    async getPublicCategories() {
        try {
            const query = `
                SELECT DISTINCT
                    mc.id,
                    mc.name,
                    mc.description,
                    mc.display_order
                FROM menu_categories mc
                JOIN menu_items mi ON mc.id = mi.category_id
                WHERE mc.is_active = true AND mi.is_available = true
                ORDER BY mc.display_order, mc.name
            `;
            return await this.db.query(query);
        } catch (error) {
            console.error('Error getting public categories:', error);
            throw error;
        }
    }
}

module.exports = new MenuCategory();
