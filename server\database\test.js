const db = require('../utils/DatabaseSecurity');
const User = require('../models/User');
const MenuCategory = require('../models/MenuCategory');
const MenuItem = require('../models/MenuItem');
const Cart = require('../models/Cart');
require('dotenv').config();

class DatabaseTester {
    constructor() {
        this.testResults = {
            passed: 0,
            failed: 0,
            errors: []
        };
    }

    async runTest(testName, testFunction) {
        try {
            console.log(`🧪 Testing: ${testName}`);
            await testFunction();
            console.log(`✅ ${testName} - PASSED`);
            this.testResults.passed++;
        } catch (error) {
            console.error(`❌ ${testName} - FAILED: ${error.message}`);
            this.testResults.failed++;
            this.testResults.errors.push({ test: testName, error: error.message });
        }
    }

    async testDatabaseConnection() {
        const isConnected = await db.testConnection();
        if (!isConnected) {
            throw new Error('Database connection failed');
        }
    }

    async testSchemaIntegrity() {
        // Check if all required tables exist
        const requiredTables = [
            'users', 'user_addresses', 'menu_categories', 'menu_items',
            'shopping_carts', 'cart_items', 'orders', 'order_items',
            'contact_submissions', 'user_sessions', 'migrations'
        ];

        const tablesQuery = `
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        `;
        const existingTables = await db.query(tablesQuery);
        const existingTableNames = existingTables.map(t => t.table_name);

        for (const table of requiredTables) {
            if (!existingTableNames.includes(table)) {
                throw new Error(`Required table '${table}' not found`);
            }
        }

        // Check if required columns exist in key tables
        const userColumns = await db.query(`
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'users'
        `);
        const userColumnNames = userColumns.map(c => c.column_name);
        const requiredUserColumns = ['id', 'email', 'password_hash', 'role', 'created_at'];
        
        for (const column of requiredUserColumns) {
            if (!userColumnNames.includes(column)) {
                throw new Error(`Required column '${column}' not found in users table`);
            }
        }
    }

    async testUserModel() {
        // Test user creation
        const testUser = {
            email: '<EMAIL>',
            password: 'TestPassword123!',
            first_name: 'Test',
            last_name: 'User',
            phone: '+234-************'
        };

        const createdUser = await User.createUser(testUser);
        if (!createdUser || !createdUser.id) {
            throw new Error('User creation failed');
        }

        // Test password verification
        const isValidPassword = await User.verifyPassword(createdUser, 'TestPassword123!');
        if (!isValidPassword) {
            throw new Error('Password verification failed');
        }

        // Test finding user by email
        const foundUser = await User.findByEmail('<EMAIL>');
        if (!foundUser || foundUser.id !== createdUser.id) {
            throw new Error('Find user by email failed');
        }

        // Test user profile retrieval
        const profile = await User.getProfile(createdUser.id);
        if (!profile || profile.email !== '<EMAIL>') {
            throw new Error('Get user profile failed');
        }

        // Clean up
        await User.delete(createdUser.id);
    }

    async testMenuModels() {
        // Test category creation
        const testCategory = {
            name: 'Test Category',
            description: 'A test category',
            display_order: 999
        };

        const createdCategory = await MenuCategory.createCategory(testCategory);
        if (!createdCategory || !createdCategory.id) {
            throw new Error('Category creation failed');
        }

        // Test menu item creation
        const testMenuItem = {
            category_id: createdCategory.id,
            name: 'Test Item',
            description: 'A test menu item',
            price: 1000.00,
            is_available: true,
            preparation_time: 15,
            calories: 300,
            allergens: ['gluten'],
            ingredients: ['test ingredient']
        };

        const createdItem = await MenuItem.createMenuItem(testMenuItem);
        if (!createdItem || !createdItem.id) {
            throw new Error('Menu item creation failed');
        }

        // Test getting category with items
        const categoryWithItems = await MenuCategory.getCategoryWithItems(createdCategory.id);
        if (!categoryWithItems || !categoryWithItems.items || categoryWithItems.items.length === 0) {
            throw new Error('Get category with items failed');
        }

        // Test menu item search
        const searchResults = await MenuItem.search('Test');
        if (!searchResults || searchResults.length === 0) {
            throw new Error('Menu item search failed');
        }

        // Clean up
        await MenuItem.delete(createdItem.id);
        await MenuCategory.delete(createdCategory.id);
    }

    async testCartModel() {
        // Create test user and menu item first
        const testUser = await User.createUser({
            email: '<EMAIL>',
            password: 'TestPassword123!',
            first_name: 'Cart',
            last_name: 'Test'
        });

        const testCategory = await MenuCategory.createCategory({
            name: 'Cart Test Category',
            description: 'For cart testing'
        });

        const testMenuItem = await MenuItem.createMenuItem({
            category_id: testCategory.id,
            name: 'Cart Test Item',
            description: 'For cart testing',
            price: 1500.00
        });

        // Test cart creation
        const cart = await Cart.getOrCreateUserCart(testUser.id);
        if (!cart || !cart.id) {
            throw new Error('Cart creation failed');
        }

        // Test adding item to cart
        const updatedCart = await Cart.addItem(cart.id, testMenuItem.id, 2);
        if (!updatedCart || !updatedCart.items || updatedCart.items.length === 0) {
            throw new Error('Add item to cart failed');
        }

        // Test cart summary with totals
        const cartSummary = await Cart.getCartSummary(cart.id);
        if (!cartSummary || !cartSummary.totals || cartSummary.totals.subtotal !== 3000.00) {
            throw new Error('Cart summary calculation failed');
        }

        // Test updating item quantity
        const cartAfterUpdate = await Cart.updateItemQuantity(cart.id, testMenuItem.id, 1);
        const newSummary = await Cart.getCartSummary(cartAfterUpdate.id);
        if (newSummary.totals.subtotal !== 1500.00) {
            throw new Error('Update item quantity failed');
        }

        // Test removing item
        await Cart.removeItem(cart.id, testMenuItem.id);
        const emptyCart = await Cart.getCartSummary(cart.id);
        if (emptyCart.items && emptyCart.items.length > 0) {
            throw new Error('Remove item from cart failed');
        }

        // Clean up
        await Cart.delete(cart.id);
        await MenuItem.delete(testMenuItem.id);
        await MenuCategory.delete(testCategory.id);
        await User.delete(testUser.id);
    }

    async testDataIntegrity() {
        // Test foreign key constraints
        try {
            // Try to create menu item with invalid category_id
            await MenuItem.create({
                category_id: '00000000-0000-0000-0000-000000000000',
                name: 'Invalid Item',
                price: 100.00
            });
            throw new Error('Foreign key constraint not working - should have failed');
        } catch (error) {
            if (!error.message.includes('violates foreign key constraint')) {
                throw new Error('Unexpected error in foreign key test: ' + error.message);
            }
            // This is expected - foreign key constraint is working
        }

        // Test unique constraints
        try {
            const category1 = await MenuCategory.createCategory({
                name: 'Unique Test Category',
                description: 'First category'
            });

            // Try to create another category with same name
            await MenuCategory.createCategory({
                name: 'Unique Test Category',
                description: 'Second category'
            });

            // Clean up first category
            await MenuCategory.delete(category1.id);
            throw new Error('Unique constraint not working - should have failed');
        } catch (error) {
            if (!error.message.includes('already exists')) {
                throw new Error('Unexpected error in unique constraint test: ' + error.message);
            }
            // This is expected - unique constraint is working
        }
    }

    async testPerformance() {
        // Test bulk operations performance
        const startTime = Date.now();
        
        // Create multiple categories and items
        const categories = [];
        for (let i = 0; i < 5; i++) {
            const category = await MenuCategory.createCategory({
                name: `Performance Test Category ${i}`,
                description: `Category ${i} for performance testing`
            });
            categories.push(category);
        }

        const items = [];
        for (const category of categories) {
            for (let j = 0; j < 10; j++) {
                const item = await MenuItem.createMenuItem({
                    category_id: category.id,
                    name: `Performance Test Item ${j}`,
                    description: `Item ${j} for performance testing`,
                    price: 1000.00 + (j * 100)
                });
                items.push(item);
            }
        }

        // Test query performance
        const allItems = await MenuItem.getAllWithCategories();
        if (allItems.length < 50) {
            throw new Error('Performance test setup failed');
        }

        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`📊 Performance test completed in ${duration}ms`);
        
        if (duration > 10000) { // 10 seconds
            console.warn('⚠️  Performance test took longer than expected');
        }

        // Clean up
        for (const item of items) {
            await MenuItem.delete(item.id);
        }
        for (const category of categories) {
            await MenuCategory.delete(category.id);
        }
    }

    async runAllTests() {
        try {
            console.log('🚀 Starting comprehensive database tests...\n');

            await this.runTest('Database Connection', () => this.testDatabaseConnection());
            await this.runTest('Schema Integrity', () => this.testSchemaIntegrity());
            await this.runTest('User Model Operations', () => this.testUserModel());
            await this.runTest('Menu Model Operations', () => this.testMenuModels());
            await this.runTest('Cart Model Operations', () => this.testCartModel());
            await this.runTest('Data Integrity Constraints', () => this.testDataIntegrity());
            await this.runTest('Performance Tests', () => this.testPerformance());

            console.log('\n📊 Test Results Summary:');
            console.log(`✅ Passed: ${this.testResults.passed}`);
            console.log(`❌ Failed: ${this.testResults.failed}`);
            console.log(`📈 Success Rate: ${((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100).toFixed(1)}%`);

            if (this.testResults.failed > 0) {
                console.log('\n❌ Failed Tests:');
                this.testResults.errors.forEach(error => {
                    console.log(`  - ${error.test}: ${error.error}`);
                });
                throw new Error('Some tests failed');
            }

            console.log('\n✅ All database tests passed successfully!');

        } catch (error) {
            console.error('\n❌ Database testing failed:', error.message);
            throw error;
        }
    }
}

// CLI interface
async function main() {
    const tester = new DatabaseTester();

    try {
        await tester.runAllTests();
    } catch (error) {
        console.error('Database testing failed:', error.message);
        process.exit(1);
    } finally {
        await db.close();
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = DatabaseTester;
