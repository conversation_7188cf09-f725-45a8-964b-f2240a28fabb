import { api } from '../api.js';
import { Alert } from './Alert.js';
import { FormValidator } from './FormValidator.js';

export class ContactManager {
    constructor() {
        this.isLoading = false;
        this.callbacks = [];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Listen for contact form submissions
        document.addEventListener('submit', async (e) => {
            if (e.target.matches('.contact-form') || e.target.matches('#contact-form')) {
                e.preventDefault();
                await this.handleContactSubmit(e.target);
            }
        });

        // Real-time validation
        document.addEventListener('input', (e) => {
            if (e.target.closest('.contact-form')) {
                this.validateField(e.target);
            }
        });

        // Character count for message fields
        document.addEventListener('input', (e) => {
            if (e.target.matches('textarea[name*="message"], textarea[name*="Message"]')) {
                this.updateCharacterCount(e.target);
            }
        });
    }

    async handleContactSubmit(form) {
        if (this.isLoading) return;

        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        
        // Show loading state
        this.setLoadingState(submitBtn, true);

        try {
            // Validate form
            if (!this.validateForm(form)) {
                Alert.show('Please fix the errors in the form', 'error');
                return;
            }

            // Get form data
            const formData = new FormData(form);
            const contactData = this.buildContactData(formData);

            // Submit to backend
            const response = await api.submitContactForm(contactData);

            // Show success message
            Alert.show('Thank you for your message! We will get back to you soon.', 'success');

            // Reset form
            form.reset();
            this.resetFormValidation(form);

            // Track success
            this.notifyCallbacks('contactSubmitted', response);

        } catch (error) {
            console.error('Contact form submission failed:', error);
            
            // Handle rate limiting
            if (error.message.includes('Too many submissions')) {
                Alert.show('You can only submit 3 contact forms per hour. Please try again later.', 'warning');
            } else {
                Alert.show(error.message || 'Failed to send message. Please try again.', 'error');
            }
        } finally {
            this.setLoadingState(submitBtn, false, originalText);
        }
    }

    buildContactData(formData) {
        // Handle different form field naming conventions
        const name = formData.get('name') || formData.get('customerName') || 
                    `${formData.get('firstName') || ''} ${formData.get('lastName') || ''}`.trim();
        const email = formData.get('email') || formData.get('customerEmail');
        const phone = formData.get('phone') || formData.get('customerPhone');
        const subject = formData.get('subject') || formData.get('customerSubject') || 'General Inquiry';
        const message = formData.get('message') || formData.get('customerMessage');
        const inquiryType = formData.get('inquiryType') || formData.get('inquiry-type') || 'general';

        return {
            name: name,
            email: email,
            phone: phone || '',
            subject: subject,
            message: message,
            inquiryType: inquiryType
        };
    }

    validateForm(form) {
        const requiredFields = form.querySelectorAll('input[required], textarea[required], select[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    validateField(field) {
        const value = field.value.trim();
        const fieldName = field.name;
        let isValid = true;
        let errorMessage = '';

        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = `${this.getFieldLabel(field)} is required`;
        }

        // Specific field validations
        if (value && isValid) {
            switch (fieldName) {
                case 'email':
                case 'customerEmail':
                    if (!FormValidator.validateEmail(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid email address';
                    }
                    break;

                case 'name':
                case 'customerName':
                case 'firstName':
                case 'lastName':
                    if (value.length < 2) {
                        isValid = false;
                        errorMessage = 'Name must be at least 2 characters long';
                    }
                    break;

                case 'phone':
                case 'customerPhone':
                    if (value && !FormValidator.validatePhone(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid phone number';
                    }
                    break;

                case 'subject':
                case 'customerSubject':
                    if (value.length < 5) {
                        isValid = false;
                        errorMessage = 'Subject must be at least 5 characters long';
                    }
                    break;

                case 'message':
                case 'customerMessage':
                    if (value.length < 10) {
                        isValid = false;
                        errorMessage = 'Message must be at least 10 characters long';
                    } else if (value.length > 2000) {
                        isValid = false;
                        errorMessage = 'Message must be less than 2000 characters';
                    }
                    break;
            }
        }

        // Update field UI
        this.updateFieldState(field, isValid, errorMessage);

        return isValid;
    }

    updateFieldState(field, isValid, errorMessage = '') {
        const fieldGroup = field.closest('.form-group') || field.closest('.input-group');
        if (!fieldGroup) return;

        // Remove existing error states
        fieldGroup.classList.remove('error', 'valid');
        const existingError = fieldGroup.querySelector('.error-message');
        if (existingError) {
            existingError.textContent = '';
            existingError.style.display = 'none';
        }

        // Add appropriate state
        if (field.value.trim()) {
            fieldGroup.classList.add(isValid ? 'valid' : 'error');
            
            if (!isValid && errorMessage) {
                let errorElement = fieldGroup.querySelector('.error-message');
                if (!errorElement) {
                    errorElement = document.createElement('div');
                    errorElement.className = 'error-message';
                    errorElement.setAttribute('role', 'alert');
                    fieldGroup.appendChild(errorElement);
                }
                errorElement.textContent = errorMessage;
                errorElement.style.display = 'block';
            }
        }
    }

    updateCharacterCount(textarea) {
        const maxLength = parseInt(textarea.getAttribute('maxlength')) || 2000;
        const currentLength = textarea.value.length;
        
        // Find character count display
        const countDisplay = textarea.parentElement.querySelector('.character-count') ||
                           textarea.closest('.form-group').querySelector('.character-count');
        
        if (countDisplay) {
            const currentSpan = countDisplay.querySelector('.count-current');
            const maxSpan = countDisplay.querySelector('.count-max');
            
            if (currentSpan) currentSpan.textContent = currentLength;
            if (maxSpan) maxSpan.textContent = maxLength;
            
            // Update color based on usage
            countDisplay.classList.remove('warning', 'danger');
            if (currentLength > maxLength * 0.9) {
                countDisplay.classList.add('danger');
            } else if (currentLength > maxLength * 0.8) {
                countDisplay.classList.add('warning');
            }
        }
    }

    getFieldLabel(field) {
        const label = field.closest('.form-group')?.querySelector('label') ||
                     document.querySelector(`label[for="${field.id}"]`);
        
        if (label) {
            return label.textContent.replace('*', '').trim();
        }
        
        // Fallback to field name
        return field.name.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    }

    setLoadingState(button, isLoading, originalText = 'Send Message') {
        if (isLoading) {
            button.disabled = true;
            button.innerHTML = `
                <div class="btn-loading">
                    <div class="loading-spinner"></div>
                </div>
                <span class="btn-text">Sending...</span>
            `;
        } else {
            button.disabled = false;
            button.innerHTML = `
                <span class="btn-text">${originalText}</span>
                <i class="btn-icon fas fa-paper-plane" aria-hidden="true"></i>
            `;
        }
    }

    resetFormValidation(form) {
        // Remove all validation states
        const fieldGroups = form.querySelectorAll('.form-group, .input-group');
        fieldGroups.forEach(group => {
            group.classList.remove('error', 'valid');
            const errorMessage = group.querySelector('.error-message');
            if (errorMessage) {
                errorMessage.textContent = '';
                errorMessage.style.display = 'none';
            }
        });

        // Reset character counts
        const textareas = form.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            this.updateCharacterCount(textarea);
        });

        // Reset submit button state
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
        }
    }

    // Admin functionality for viewing contact submissions
    async getContactSubmissions(page = 1, filters = {}) {
        try {
            const params = { page, limit: 20, ...filters };
            const response = await api.getAdminContactSubmissions(params);
            return response;
        } catch (error) {
            console.error('Failed to get contact submissions:', error);
            throw error;
        }
    }

    async markSubmissionAsRead(submissionId, isRead = true) {
        try {
            const response = await api.markContactSubmissionAsRead(submissionId, isRead);
            this.notifyCallbacks('submissionUpdated', response);
            return response;
        } catch (error) {
            console.error('Failed to mark submission as read:', error);
            throw error;
        }
    }

    async addAdminNotes(submissionId, notes) {
        try {
            const response = await api.addContactAdminNotes(submissionId, notes);
            this.notifyCallbacks('notesAdded', response);
            return response;
        } catch (error) {
            console.error('Failed to add admin notes:', error);
            throw error;
        }
    }

    async getContactStats() {
        try {
            const response = await api.getContactStats();
            return response.stats;
        } catch (error) {
            console.error('Failed to get contact stats:', error);
            throw error;
        }
    }

    // Render contact submissions for admin
    renderContactSubmissions(submissions, container) {
        if (!container) return;

        if (submissions.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <h3>No contact submissions</h3>
                    <p>Contact submissions will appear here when customers send messages.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = submissions.map(submission => `
            <div class="contact-submission ${submission.isRead ? 'read' : 'unread'}" data-id="${submission.id}">
                <div class="submission-header">
                    <div class="submission-info">
                        <h4 class="submission-name">${submission.name}</h4>
                        <span class="submission-email">${submission.email}</span>
                        <span class="submission-type type-${submission.inquiryType}">${submission.inquiryType}</span>
                    </div>
                    <div class="submission-meta">
                        <span class="submission-date">${new Date(submission.createdAt).toLocaleDateString()}</span>
                        <button class="btn btn-sm mark-read-btn" 
                                data-id="${submission.id}" 
                                data-read="${submission.isRead}">
                            ${submission.isRead ? 'Mark Unread' : 'Mark Read'}
                        </button>
                    </div>
                </div>
                <div class="submission-content">
                    <h5 class="submission-subject">${submission.subject}</h5>
                    <p class="submission-message">${submission.message}</p>
                    ${submission.phone ? `<p class="submission-phone">Phone: ${submission.phone}</p>` : ''}
                </div>
                ${submission.adminNotes ? `
                    <div class="submission-notes">
                        <h6>Admin Notes:</h6>
                        <p>${submission.adminNotes}</p>
                    </div>
                ` : ''}
                <div class="submission-actions">
                    <button class="btn btn-sm add-notes-btn" data-id="${submission.id}">
                        ${submission.adminNotes ? 'Edit Notes' : 'Add Notes'}
                    </button>
                    <a href="mailto:${submission.email}?subject=Re: ${encodeURIComponent(submission.subject)}" 
                       class="btn btn-sm btn-primary">
                        Reply via Email
                    </a>
                </div>
            </div>
        `).join('');

        // Add event listeners for admin actions
        container.querySelectorAll('.mark-read-btn').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const id = e.target.dataset.id;
                const isRead = e.target.dataset.read === 'true';
                await this.markSubmissionAsRead(id, !isRead);
                // Refresh the display
                window.location.reload();
            });
        });
    }

    // Callback management
    onContactChange(callback) {
        this.callbacks.push(callback);
        
        return () => {
            const index = this.callbacks.indexOf(callback);
            if (index > -1) {
                this.callbacks.splice(index, 1);
            }
        };
    }

    notifyCallbacks(event, data = null) {
        this.callbacks.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Contact callback error:', error);
            }
        });
    }
}

// Create global contact manager instance
export const contactManager = new ContactManager();

// Make it available globally for debugging
window.contactManager = contactManager;
