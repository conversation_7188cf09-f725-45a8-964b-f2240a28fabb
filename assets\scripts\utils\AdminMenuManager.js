import { api } from '../api.js';
import { Alert } from './Alert.js';
import { formatPrice } from '../config/currency.js';

export class AdminMenuManager {
    constructor() {
        this.categories = [];
        this.menuItems = [];
        this.selectedItems = new Set();
        this.currentView = 'items'; // 'items' or 'categories'
        this.filters = {
            category: '',
            availability: '',
            featured: '',
            search: ''
        };
        this.isLoading = false;
        
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadData();
    }

    setupEventListeners() {
        // View switching
        document.addEventListener('click', (e) => {
            if (e.target.matches('.view-switch-btn')) {
                this.switchView(e.target.dataset.view);
            }
        });

        // Item selection
        document.addEventListener('change', (e) => {
            if (e.target.matches('.item-checkbox')) {
                this.handleItemSelection(e.target);
            }
            
            if (e.target.matches('#select-all-items')) {
                this.handleSelectAll(e.target.checked);
            }
        });

        // Bulk operations
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.bulk-action-btn')) {
                e.preventDefault();
                await this.handleBulkAction(e.target.dataset.action);
            }
        });

        // Item actions
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.edit-item-btn')) {
                e.preventDefault();
                this.editItem(e.target.dataset.itemId);
            }
            
            if (e.target.matches('.delete-item-btn')) {
                e.preventDefault();
                await this.deleteItem(e.target.dataset.itemId);
            }
            
            if (e.target.matches('.toggle-availability-btn')) {
                e.preventDefault();
                await this.toggleAvailability(e.target.dataset.itemId);
            }
        });

        // Category actions
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.edit-category-btn')) {
                e.preventDefault();
                this.editCategory(e.target.dataset.categoryId);
            }
            
            if (e.target.matches('.delete-category-btn')) {
                e.preventDefault();
                await this.deleteCategory(e.target.dataset.categoryId);
            }
        });

        // Form submissions
        document.addEventListener('submit', async (e) => {
            if (e.target.matches('#menu-item-form')) {
                e.preventDefault();
                await this.handleItemFormSubmit(e.target);
            }
            
            if (e.target.matches('#category-form')) {
                e.preventDefault();
                await this.handleCategoryFormSubmit(e.target);
            }
        });

        // Filters
        document.addEventListener('change', (e) => {
            if (e.target.matches('.filter-select')) {
                this.updateFilter(e.target.name, e.target.value);
            }
        });

        // Search
        document.addEventListener('input', (e) => {
            if (e.target.matches('#menu-search')) {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.updateFilter('search', e.target.value);
                }, 300);
            }
        });

        // Add new buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('#add-item-btn')) {
                this.showItemForm();
            }
            
            if (e.target.matches('#add-category-btn')) {
                this.showCategoryForm();
            }
        });
    }

    async loadData() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading(true);

        try {
            const [categoriesResponse, itemsResponse] = await Promise.all([
                api.getMenuCategories(),
                api.getMenuItems()
            ]);

            this.categories = categoriesResponse.categories || [];
            this.menuItems = itemsResponse.items || [];

            this.renderCurrentView();
        } catch (error) {
            console.error('Failed to load menu data:', error);
            Alert.show('Failed to load menu data', 'error');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    switchView(view) {
        this.currentView = view;
        
        // Update active tab
        document.querySelectorAll('.view-switch-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');
        
        this.renderCurrentView();
    }

    renderCurrentView() {
        const container = document.querySelector('#menu-management-content');
        if (!container) return;

        if (this.currentView === 'items') {
            this.renderItemsView(container);
        } else {
            this.renderCategoriesView(container);
        }
    }

    renderItemsView(container) {
        const filteredItems = this.getFilteredItems();
        
        container.innerHTML = `
            <div class="menu-items-header">
                <div class="items-controls">
                    <div class="search-box">
                        <input type="text" id="menu-search" placeholder="Search items..." 
                               value="${this.filters.search}">
                        <i class="fas fa-search"></i>
                    </div>
                    
                    <div class="filters">
                        <select name="category" class="filter-select">
                            <option value="">All Categories</option>
                            ${this.categories.map(cat => 
                                `<option value="${cat.id}" ${this.filters.category === cat.id ? 'selected' : ''}>
                                    ${cat.name}
                                </option>`
                            ).join('')}
                        </select>
                        
                        <select name="availability" class="filter-select">
                            <option value="">All Items</option>
                            <option value="true" ${this.filters.availability === 'true' ? 'selected' : ''}>Available</option>
                            <option value="false" ${this.filters.availability === 'false' ? 'selected' : ''}>Unavailable</option>
                        </select>
                        
                        <select name="featured" class="filter-select">
                            <option value="">All Items</option>
                            <option value="true" ${this.filters.featured === 'true' ? 'selected' : ''}>Featured</option>
                            <option value="false" ${this.filters.featured === 'false' ? 'selected' : ''}>Not Featured</option>
                        </select>
                    </div>
                    
                    <button id="add-item-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Item
                    </button>
                </div>
                
                <div class="bulk-actions" ${this.selectedItems.size === 0 ? 'style="display: none;"' : ''}>
                    <span class="selected-count">${this.selectedItems.size} items selected</span>
                    <div class="bulk-buttons">
                        <button class="bulk-action-btn btn btn-sm" data-action="enable">Enable</button>
                        <button class="bulk-action-btn btn btn-sm" data-action="disable">Disable</button>
                        <button class="bulk-action-btn btn btn-sm" data-action="feature">Feature</button>
                        <button class="bulk-action-btn btn btn-sm" data-action="unfeature">Unfeature</button>
                        <button class="bulk-action-btn btn btn-sm btn-danger" data-action="delete">Delete</button>
                    </div>
                </div>
            </div>
            
            <div class="menu-items-table">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="select-all-items">
                            </th>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Price</th>
                            <th>Status</th>
                            <th>Featured</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredItems.map(item => this.renderItemRow(item)).join('')}
                    </tbody>
                </table>
                
                ${filteredItems.length === 0 ? `
                    <div class="empty-state">
                        <i class="fas fa-utensils"></i>
                        <h3>No items found</h3>
                        <p>Try adjusting your filters or add a new menu item.</p>
                    </div>
                ` : ''}
            </div>
        `;
    }

    renderItemRow(item) {
        const isSelected = this.selectedItems.has(item.id);
        const category = this.categories.find(cat => cat.id === item.category_id);
        
        return `
            <tr class="item-row ${isSelected ? 'selected' : ''}">
                <td>
                    <input type="checkbox" class="item-checkbox" 
                           value="${item.id}" ${isSelected ? 'checked' : ''}>
                </td>
                <td>
                    <img src="${item.image_url || '/assets/images/placeholder-food.jpg'}" 
                         alt="${item.name}" class="item-thumbnail">
                </td>
                <td>
                    <div class="item-info">
                        <strong>${item.name}</strong>
                        <p class="item-description">${item.description || ''}</p>
                    </div>
                </td>
                <td>
                    <span class="category-badge">${category?.name || 'Unknown'}</span>
                </td>
                <td>
                    <span class="price">${formatPrice(item.price)}</span>
                </td>
                <td>
                    <span class="status-badge ${item.is_available ? 'available' : 'unavailable'}">
                        ${item.is_available ? 'Available' : 'Unavailable'}
                    </span>
                </td>
                <td>
                    <span class="featured-badge ${item.is_featured ? 'featured' : ''}">
                        ${item.is_featured ? 'Featured' : ''}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm edit-item-btn" data-item-id="${item.id}" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm toggle-availability-btn" 
                                data-item-id="${item.id}" 
                                title="${item.is_available ? 'Disable' : 'Enable'}">
                            <i class="fas fa-${item.is_available ? 'eye-slash' : 'eye'}"></i>
                        </button>
                        <button class="btn btn-sm btn-danger delete-item-btn" 
                                data-item-id="${item.id}" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    renderCategoriesView(container) {
        container.innerHTML = `
            <div class="categories-header">
                <h3>Menu Categories</h3>
                <button id="add-category-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add Category
                </button>
            </div>
            
            <div class="categories-grid">
                ${this.categories.map(category => this.renderCategoryCard(category)).join('')}
            </div>
        `;
    }

    renderCategoryCard(category) {
        const itemCount = this.menuItems.filter(item => item.category_id === category.id).length;
        
        return `
            <div class="category-card">
                <div class="category-header">
                    <h4>${category.name}</h4>
                    <div class="category-actions">
                        <button class="btn btn-sm edit-category-btn" data-category-id="${category.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger delete-category-btn" data-category-id="${category.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <p class="category-description">${category.description || ''}</p>
                <div class="category-stats">
                    <span class="item-count">${itemCount} items</span>
                    <span class="status ${category.is_active ? 'active' : 'inactive'}">
                        ${category.is_active ? 'Active' : 'Inactive'}
                    </span>
                </div>
            </div>
        `;
    }

    getFilteredItems() {
        return this.menuItems.filter(item => {
            if (this.filters.category && item.category_id !== this.filters.category) return false;
            if (this.filters.availability !== '' && item.is_available.toString() !== this.filters.availability) return false;
            if (this.filters.featured !== '' && item.is_featured.toString() !== this.filters.featured) return false;
            if (this.filters.search && !item.name.toLowerCase().includes(this.filters.search.toLowerCase())) return false;
            return true;
        });
    }

    updateFilter(filterName, value) {
        this.filters[filterName] = value;
        this.renderCurrentView();
    }

    handleItemSelection(checkbox) {
        const itemId = checkbox.value;
        
        if (checkbox.checked) {
            this.selectedItems.add(itemId);
        } else {
            this.selectedItems.delete(itemId);
        }
        
        this.updateBulkActionsVisibility();
        this.updateSelectAllState();
    }

    handleSelectAll(checked) {
        const filteredItems = this.getFilteredItems();
        
        if (checked) {
            filteredItems.forEach(item => this.selectedItems.add(item.id));
        } else {
            filteredItems.forEach(item => this.selectedItems.delete(item.id));
        }
        
        // Update checkboxes
        document.querySelectorAll('.item-checkbox').forEach(checkbox => {
            checkbox.checked = checked;
        });
        
        this.updateBulkActionsVisibility();
    }

    updateBulkActionsVisibility() {
        const bulkActions = document.querySelector('.bulk-actions');
        const selectedCount = document.querySelector('.selected-count');
        
        if (bulkActions) {
            bulkActions.style.display = this.selectedItems.size > 0 ? 'flex' : 'none';
        }
        
        if (selectedCount) {
            selectedCount.textContent = `${this.selectedItems.size} items selected`;
        }
    }

    updateSelectAllState() {
        const selectAllCheckbox = document.querySelector('#select-all-items');
        const filteredItems = this.getFilteredItems();
        const selectedFilteredItems = filteredItems.filter(item => this.selectedItems.has(item.id));
        
        if (selectAllCheckbox) {
            if (selectedFilteredItems.length === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (selectedFilteredItems.length === filteredItems.length) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }
    }

    async handleBulkAction(action) {
        if (this.selectedItems.size === 0) {
            Alert.show('No items selected', 'warning');
            return;
        }

        const itemIds = Array.from(this.selectedItems);
        
        try {
            switch (action) {
                case 'enable':
                    await api.bulkUpdateItemAvailability(itemIds, true);
                    Alert.show(`${itemIds.length} items enabled`, 'success');
                    break;
                    
                case 'disable':
                    await api.bulkUpdateItemAvailability(itemIds, false);
                    Alert.show(`${itemIds.length} items disabled`, 'success');
                    break;
                    
                case 'feature':
                    await api.bulkUpdateItemFeatured(itemIds, true);
                    Alert.show(`${itemIds.length} items featured`, 'success');
                    break;
                    
                case 'unfeature':
                    await api.bulkUpdateItemFeatured(itemIds, false);
                    Alert.show(`${itemIds.length} items unfeatured`, 'success');
                    break;
                    
                case 'delete':
                    if (confirm(`Are you sure you want to delete ${itemIds.length} items?`)) {
                        await api.bulkDeleteItems(itemIds);
                        Alert.show(`${itemIds.length} items deleted`, 'success');
                    } else {
                        return;
                    }
                    break;
            }
            
            // Clear selection and reload data
            this.selectedItems.clear();
            await this.loadData();
            
        } catch (error) {
            console.error('Bulk action failed:', error);
            Alert.show(`Failed to ${action} items`, 'error');
        }
    }

    showLoading(show) {
        const loader = document.querySelector('.menu-management-loader');
        if (loader) {
            loader.style.display = show ? 'block' : 'none';
        }
    }

    // Additional methods for forms, editing, etc. would go here...
}

// Create global admin menu manager instance
export const adminMenuManager = new AdminMenuManager();
