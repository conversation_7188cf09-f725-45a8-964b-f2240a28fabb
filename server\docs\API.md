# Magic Menu API Documentation

## Overview

The Magic Menu API is a RESTful API built with Node.js and Express.js that provides comprehensive backend functionality for a restaurant ordering system. It includes user authentication, menu management, cart operations, order processing, and administrative features.

## Base URL

```
Development: http://localhost:3000/api
Production: https://your-domain.com/api
```

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Token Refresh

Access tokens expire after 24 hours. Use the refresh token to get a new access token without requiring the user to log in again.

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "message": "Operation successful",
  "data": { ... },
  "pagination": { ... } // For paginated responses
}
```

### Error Response
```json
{
  "error": "Error type",
  "message": "Detailed error message"
}
```

## Status Codes

- `200` - OK
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `429` - Too Many Requests
- `500` - Internal Server Error

## API Endpoints

### Authentication (`/api/auth`)

#### Register User
```http
POST /api/auth/register
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+234-************"
}
```

**Response:**
```json
{
  "message": "Registration successful",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "customer",
    "emailVerified": false
  },
  "tokens": {
    "accessToken": "jwt-token",
    "refreshToken": "refresh-token",
    "expiresIn": "24h",
    "tokenType": "Bearer"
  }
}
```

#### Login User
```http
POST /api/auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

#### Refresh Token
```http
POST /api/auth/refresh
```

**Request Body:**
```json
{
  "refreshToken": "your-refresh-token"
}
```

#### Get User Profile
```http
GET /api/auth/profile
Authorization: Bearer <token>
```

#### Update User Profile
```http
PUT /api/auth/profile
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+234-************"
}
```

#### Logout
```http
POST /api/auth/logout
Authorization: Bearer <token>
```

### Menu (`/api/menu`)

#### Get Menu Categories
```http
GET /api/menu/categories
```

**Response:**
```json
{
  "categories": [
    {
      "id": "uuid",
      "name": "Local Delights",
      "description": "Traditional Nigerian dishes",
      "displayOrder": 1
    }
  ],
  "count": 4
}
```

#### Get Menu Items
```http
GET /api/menu/items?category=uuid&featured=true&search=jollof&page=1&limit=20
```

**Query Parameters:**
- `category` - Filter by category ID
- `featured` - Show only featured items (true/false)
- `search` - Search term
- `minPrice` - Minimum price filter
- `maxPrice` - Maximum price filter
- `allergens` - Exclude allergens (array)
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 50)

#### Get Single Menu Item
```http
GET /api/menu/items/:id
```

#### Get Popular Items
```http
GET /api/menu/popular?limit=10
```

### Admin Menu Management (`/api/menu/admin`)

*Requires admin authentication*

#### Create Category
```http
POST /api/menu/admin/categories
Authorization: Bearer <admin-token>
```

**Request Body:**
```json
{
  "name": "New Category",
  "description": "Category description",
  "displayOrder": 5
}
```

#### Update Category
```http
PUT /api/menu/admin/categories/:id
Authorization: Bearer <admin-token>
```

#### Delete Category
```http
DELETE /api/menu/admin/categories/:id
Authorization: Bearer <admin-token>
```

#### Create Menu Item
```http
POST /api/menu/admin/items
Authorization: Bearer <admin-token>
```

**Request Body:**
```json
{
  "categoryId": "uuid",
  "name": "New Dish",
  "description": "Delicious new dish",
  "price": 2500.00,
  "imageUrl": "https://example.com/image.jpg",
  "isAvailable": true,
  "isFeatured": false,
  "preparationTime": 20,
  "calories": 450,
  "allergens": ["gluten"],
  "ingredients": ["rice", "chicken", "spices"],
  "nutritionalInfo": {},
  "displayOrder": 1
}
```

### Cart (`/api/cart`)

#### Get Cart
```http
GET /api/cart
Authorization: Bearer <token> (optional for guest carts)
```

#### Add Item to Cart
```http
POST /api/cart/add
Authorization: Bearer <token> (optional for guest carts)
```

**Request Body:**
```json
{
  "menuItemId": "uuid",
  "quantity": 2,
  "specialInstructions": "Extra spicy"
}
```

#### Update Item Quantity
```http
PUT /api/cart/items/:menuItemId
Authorization: Bearer <token> (optional for guest carts)
```

**Request Body:**
```json
{
  "quantity": 3
}
```

#### Remove Item from Cart
```http
DELETE /api/cart/items/:menuItemId
Authorization: Bearer <token> (optional for guest carts)
```

#### Clear Cart
```http
DELETE /api/cart
Authorization: Bearer <token> (optional for guest carts)
```

#### Get Cart Count
```http
GET /api/cart/count
Authorization: Bearer <token> (optional for guest carts)
```

#### Validate Cart
```http
POST /api/cart/validate
Authorization: Bearer <token> (optional for guest carts)
```

### Orders (`/api/orders`)

#### Create Order
```http
POST /api/orders
Authorization: Bearer <token> (optional for guest orders)
```

**Request Body:**
```json
{
  "customerInfo": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+234-************",
    "address": {
      "street": "123 Main Street",
      "city": "Lagos",
      "state": "Lagos",
      "postalCode": "100001",
      "country": "Nigeria"
    },
    "specialInstructions": "Ring doorbell twice"
  },
  "paymentInfo": {
    "method": "card",
    "reference": "payment-ref-123"
  }
}
```

#### Get Order by ID
```http
GET /api/orders/:id
Authorization: Bearer <token> (optional)
```

#### Track Order by Order Number
```http
GET /api/orders/track/:orderNumber
```

#### Get User Order History
```http
GET /api/orders/user/history?page=1&limit=10
Authorization: Bearer <token>
```

### Admin Order Management (`/api/orders/admin`)

*Requires admin authentication*

#### Get All Orders
```http
GET /api/orders/admin/all?page=1&limit=20&status=pending&search=MM123
Authorization: Bearer <admin-token>
```

#### Update Order Status
```http
PATCH /api/orders/:id/status
Authorization: Bearer <admin-token>
```

**Request Body:**
```json
{
  "status": "confirmed"
}
```

**Valid statuses:** `pending`, `confirmed`, `preparing`, `ready`, `delivered`, `cancelled`

#### Get Order Statistics
```http
GET /api/orders/admin/stats?startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer <admin-token>
```

### Admin Panel (`/api/admin`)

*All admin routes require admin authentication*

#### Dashboard Overview
```http
GET /api/admin/dashboard
Authorization: Bearer <admin-token>
```

#### User Management
```http
GET /api/admin/users?page=1&limit=20&search=john&role=customer
Authorization: Bearer <admin-token>
```

#### Get User Details
```http
GET /api/admin/users/:id
Authorization: Bearer <admin-token>
```

#### Update User Status
```http
PATCH /api/admin/users/:id/status
Authorization: Bearer <admin-token>
```

**Request Body:**
```json
{
  "isActive": false
}
```

#### Sales Analytics
```http
GET /api/admin/analytics/sales?period=30d&startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer <admin-token>
```

#### System Status
```http
GET /api/admin/system/status
Authorization: Bearer <admin-token>
```

#### Export Data
```http
GET /api/admin/export/:type?format=json&startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer <admin-token>
```

**Export types:** `orders`, `users`, `menu`

### Contact (`/api/contact`)

#### Submit Contact Form
```http
POST /api/contact/submit
```

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+234-************",
  "subject": "Question about catering",
  "message": "I would like to inquire about catering services...",
  "inquiryType": "catering"
}
```

**Inquiry types:** `general`, `complaint`, `suggestion`, `support`, `catering`, `partnership`

#### Get Contact Statistics
```http
GET /api/contact/stats
```

### Admin Contact Management (`/api/contact/admin`)

*Requires admin authentication*

#### Get All Submissions
```http
GET /api/contact/admin/submissions?page=1&limit=20&isRead=false&inquiryType=general
Authorization: Bearer <admin-token>
```

#### Get Single Submission
```http
GET /api/contact/admin/submissions/:id
Authorization: Bearer <admin-token>
```

#### Mark as Read/Unread
```http
PATCH /api/contact/admin/submissions/:id/read
Authorization: Bearer <admin-token>
```

**Request Body:**
```json
{
  "isRead": true
}
```

#### Add Admin Notes
```http
PATCH /api/contact/admin/submissions/:id/notes
Authorization: Bearer <admin-token>
```

**Request Body:**
```json
{
  "notes": "Responded via email on 2024-01-15"
}
```

#### Get Contact Statistics
```http
GET /api/contact/admin/stats?startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer <admin-token>
```

#### Bulk Mark as Read
```http
PATCH /api/contact/admin/submissions/bulk/read
Authorization: Bearer <admin-token>
```

**Request Body:**
```json
{
  "submissionIds": ["uuid1", "uuid2", "uuid3"]
}
```

## Rate Limiting

- Authentication endpoints: 5 requests per 15 minutes per IP
- Contact form: 3 submissions per hour per session
- General API: 100 requests per 15 minutes per IP

## Error Handling

The API includes comprehensive error handling with detailed error messages and appropriate HTTP status codes. All errors are logged for debugging purposes.

## Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Rate limiting
- Input validation and sanitization
- CORS protection
- Helmet security headers
- SQL injection prevention with parameterized queries

## Pagination

Paginated endpoints return the following structure:

```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "pages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Testing

Use the provided test script to verify all endpoints:

```bash
# Run API tests
node server/tests/api-test.js

# Run specific test suite
node server/tests/api-test.js auth
node server/tests/api-test.js menu
node server/tests/api-test.js cart
node server/tests/api-test.js orders
```

## Health Check

Check API health status:

```http
GET /health
```

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "database": "connected",
  "environment": "development"
}
```
