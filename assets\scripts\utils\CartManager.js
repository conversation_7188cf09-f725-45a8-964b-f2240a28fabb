import { api } from '../api.js';
import { Alert } from './Alert.js';
import { formatPrice } from '../config/currency.js';

export class CartManager {
    constructor() {
        this.cart = {
            items: [],
            totals: {
                subtotal: 0,
                tax: 0,
                deliveryFee: 0,
                total: 0
            },
            itemCount: 0,
            totalQuantity: 0
        };
        
        this.isLoading = false;
        this.callbacks = [];
        
        this.init();
    }

    async init() {
        // Load cart from backend
        await this.loadCart();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Update UI
        this.updateCartUI();
    }

    setupEventListeners() {
        // Listen for add to cart buttons
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.add-to-cart-btn') || e.target.closest('.add-to-cart-btn')) {
                e.preventDefault();
                const button = e.target.matches('.add-to-cart-btn') ? e.target : e.target.closest('.add-to-cart-btn');
                await this.handleAddToCart(button);
            }
        });

        // Listen for cart item updates
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.cart-item-remove') || e.target.closest('.cart-item-remove')) {
                e.preventDefault();
                const button = e.target.matches('.cart-item-remove') ? e.target : e.target.closest('.cart-item-remove');
                const itemId = button.dataset.itemId;
                await this.removeItem(itemId);
            }
        });

        // Listen for quantity changes
        document.addEventListener('change', async (e) => {
            if (e.target.matches('.cart-item-quantity')) {
                const itemId = e.target.dataset.itemId;
                const quantity = parseInt(e.target.value);
                await this.updateQuantity(itemId, quantity);
            }
        });

        // Listen for quantity increment/decrement buttons
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.quantity-btn')) {
                e.preventDefault();
                const button = e.target;
                const itemId = button.dataset.itemId;
                const action = button.dataset.action;
                const currentQuantity = parseInt(button.parentElement.querySelector('.cart-item-quantity').value);
                
                if (action === 'increment') {
                    await this.updateQuantity(itemId, currentQuantity + 1);
                } else if (action === 'decrement' && currentQuantity > 1) {
                    await this.updateQuantity(itemId, currentQuantity - 1);
                }
            }
        });
    }

    async loadCart() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        
        try {
            const response = await api.getCart();
            this.cart = response.cart || this.cart;
            this.notifyCallbacks('cartLoaded', this.cart);
        } catch (error) {
            console.error('Failed to load cart:', error);
            // Try to load from localStorage as fallback
            this.loadFromLocalStorage();
        } finally {
            this.isLoading = false;
        }
    }

    loadFromLocalStorage() {
        try {
            const savedCart = localStorage.getItem('cartItems');
            if (savedCart) {
                const items = JSON.parse(savedCart);
                this.cart.items = items;
                this.calculateTotals();
            }
        } catch (error) {
            console.error('Failed to load cart from localStorage:', error);
        }
    }

    async addItem(menuItemId, quantity = 1, specialInstructions = '') {
        if (this.isLoading) return;
        
        this.isLoading = true;
        
        try {
            const response = await api.addToCart(menuItemId, quantity, specialInstructions);
            this.cart = response.cart;
            
            Alert.show('Item added to cart!', 'success');
            this.updateCartUI();
            this.notifyCallbacks('itemAdded', { menuItemId, quantity });
            
            return response;
        } catch (error) {
            console.error('Failed to add item to cart:', error);
            Alert.show(error.message || 'Failed to add item to cart', 'error');
            throw error;
        } finally {
            this.isLoading = false;
        }
    }

    async updateQuantity(menuItemId, quantity) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        
        try {
            const response = await api.updateCartItem(menuItemId, quantity);
            this.cart = response.cart;
            
            this.updateCartUI();
            this.notifyCallbacks('quantityUpdated', { menuItemId, quantity });
            
            return response;
        } catch (error) {
            console.error('Failed to update cart item:', error);
            Alert.show(error.message || 'Failed to update cart', 'error');
            throw error;
        } finally {
            this.isLoading = false;
        }
    }

    async removeItem(menuItemId) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        
        try {
            const response = await api.removeFromCart(menuItemId);
            this.cart = response.cart;
            
            Alert.show('Item removed from cart', 'info');
            this.updateCartUI();
            this.notifyCallbacks('itemRemoved', { menuItemId });
            
            return response;
        } catch (error) {
            console.error('Failed to remove item from cart:', error);
            Alert.show(error.message || 'Failed to remove item', 'error');
            throw error;
        } finally {
            this.isLoading = false;
        }
    }

    async clearCart() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        
        try {
            const response = await api.clearCart();
            this.cart = response.cart || {
                items: [],
                totals: { subtotal: 0, tax: 0, deliveryFee: 0, total: 0 },
                itemCount: 0,
                totalQuantity: 0
            };
            
            Alert.show('Cart cleared', 'info');
            this.updateCartUI();
            this.notifyCallbacks('cartCleared');
            
            return response;
        } catch (error) {
            console.error('Failed to clear cart:', error);
            Alert.show(error.message || 'Failed to clear cart', 'error');
            throw error;
        } finally {
            this.isLoading = false;
        }
    }

    async handleAddToCart(button) {
        const itemId = button.dataset.itemId;
        const card = button.closest('.menu-item-card');
        
        if (!itemId || !card) {
            Alert.show('Invalid item', 'error');
            return;
        }

        // Show loading state
        const originalText = button.textContent;
        button.textContent = 'Adding...';
        button.disabled = true;

        try {
            await this.addItem(itemId, 1);
        } catch (error) {
            // Error already handled in addItem
        } finally {
            // Reset button state
            button.textContent = originalText;
            button.disabled = false;
        }
    }

    calculateTotals() {
        const subtotal = this.cart.items.reduce((sum, item) => {
            const price = typeof item.price === 'string' ? parseFloat(item.price.replace(/[^\d.-]/g, '')) : item.price;
            return sum + (price * item.quantity);
        }, 0);
        
        const tax = subtotal * 0.075; // 7.5% VAT
        const deliveryFee = subtotal > 0 ? 500 : 0; // ₦500 delivery fee
        const total = subtotal + tax + deliveryFee;
        
        this.cart.totals = {
            subtotal: parseFloat(subtotal.toFixed(2)),
            tax: parseFloat(tax.toFixed(2)),
            deliveryFee: deliveryFee,
            total: parseFloat(total.toFixed(2))
        };
        
        this.cart.itemCount = this.cart.items.length;
        this.cart.totalQuantity = this.cart.items.reduce((sum, item) => sum + item.quantity, 0);
    }

    updateCartUI() {
        this.updateCartCount();
        this.updateCartSidebar();
        this.updateCartPage();
    }

    updateCartCount() {
        const cartCountElements = document.querySelectorAll('.cart-count, .cart-badge');
        const count = this.cart.totalQuantity || 0;
        
        cartCountElements.forEach(element => {
            element.textContent = count;
            element.style.display = count > 0 ? 'inline' : 'none';
        });
    }

    updateCartSidebar() {
        const sidebar = document.querySelector('.cart-sidebar');
        if (!sidebar) return;

        const itemsContainer = sidebar.querySelector('.cart-items');
        const totalsContainer = sidebar.querySelector('.cart-totals');
        
        if (itemsContainer) {
            if (this.cart.items.length === 0) {
                itemsContainer.innerHTML = '<p class="empty-cart">Your cart is empty</p>';
            } else {
                itemsContainer.innerHTML = this.cart.items.map(item => this.renderCartItem(item)).join('');
            }
        }
        
        if (totalsContainer) {
            totalsContainer.innerHTML = this.renderCartTotals();
        }
    }

    updateCartPage() {
        const cartPage = document.querySelector('.cart-page');
        if (!cartPage) return;

        const itemsContainer = cartPage.querySelector('.cart-items-list');
        const totalsContainer = cartPage.querySelector('.cart-summary');
        
        if (itemsContainer) {
            if (this.cart.items.length === 0) {
                itemsContainer.innerHTML = `
                    <div class="empty-cart-message">
                        <i class="fas fa-shopping-cart"></i>
                        <h3>Your cart is empty</h3>
                        <p>Add some delicious items to get started!</p>
                        <a href="/menu.html" class="btn btn-primary">Browse Menu</a>
                    </div>
                `;
            } else {
                itemsContainer.innerHTML = this.cart.items.map(item => this.renderCartPageItem(item)).join('');
            }
        }
        
        if (totalsContainer) {
            totalsContainer.innerHTML = this.renderCartSummary();
        }
    }

    renderCartItem(item) {
        const price = typeof item.price === 'string' ? item.price : formatPrice(item.price);
        const total = formatPrice((parseFloat(item.price) || 0) * item.quantity);
        
        return `
            <div class="cart-item" data-item-id="${item.menu_item_id || item.id}">
                <img src="${item.image_url || item.image || 'assets/images/placeholder-food.jpg'}" 
                     alt="${item.name || item.title}" class="cart-item-image">
                <div class="cart-item-details">
                    <h4 class="cart-item-name">${item.name || item.title}</h4>
                    <p class="cart-item-price">${price}</p>
                    <div class="cart-item-controls">
                        <button class="quantity-btn" data-action="decrement" data-item-id="${item.menu_item_id || item.id}">-</button>
                        <input type="number" class="cart-item-quantity" value="${item.quantity}" 
                               min="1" data-item-id="${item.menu_item_id || item.id}">
                        <button class="quantity-btn" data-action="increment" data-item-id="${item.menu_item_id || item.id}">+</button>
                    </div>
                </div>
                <div class="cart-item-total">
                    <span class="item-total">${total}</span>
                    <button class="cart-item-remove" data-item-id="${item.menu_item_id || item.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    renderCartPageItem(item) {
        // Similar to renderCartItem but with more details for the cart page
        return this.renderCartItem(item);
    }

    renderCartTotals() {
        return `
            <div class="cart-totals">
                <div class="total-line">
                    <span>Subtotal:</span>
                    <span>${formatPrice(this.cart.totals.subtotal)}</span>
                </div>
                <div class="total-line">
                    <span>Tax (7.5%):</span>
                    <span>${formatPrice(this.cart.totals.tax)}</span>
                </div>
                <div class="total-line">
                    <span>Delivery:</span>
                    <span>${formatPrice(this.cart.totals.deliveryFee)}</span>
                </div>
                <div class="total-line total">
                    <span>Total:</span>
                    <span>${formatPrice(this.cart.totals.total)}</span>
                </div>
            </div>
        `;
    }

    renderCartSummary() {
        return `
            <div class="cart-summary">
                ${this.renderCartTotals()}
                <div class="cart-actions">
                    <button class="btn btn-secondary" onclick="cartManager.clearCart()">Clear Cart</button>
                    <button class="btn btn-primary checkout-btn">Proceed to Checkout</button>
                </div>
            </div>
        `;
    }

    // Callback management
    onCartChange(callback) {
        this.callbacks.push(callback);
        
        // Return unsubscribe function
        return () => {
            const index = this.callbacks.indexOf(callback);
            if (index > -1) {
                this.callbacks.splice(index, 1);
            }
        };
    }

    notifyCallbacks(event, data = null) {
        this.callbacks.forEach(callback => {
            try {
                callback(event, data, this.cart);
            } catch (error) {
                console.error('Cart callback error:', error);
            }
        });
    }

    // Public getters
    getCart() {
        return this.cart;
    }

    getItemCount() {
        return this.cart.totalQuantity || 0;
    }

    getTotals() {
        return this.cart.totals;
    }

    isEmpty() {
        return this.cart.items.length === 0;
    }
}

// Create global cart manager instance
export const cartManager = new CartManager();

// Make it available globally for debugging
window.cartManager = cartManager;
