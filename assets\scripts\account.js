import { api } from './api.js';
import { Alert } from './utils/Alert.js';
import { FormValidator } from './utils/FormValidator.js';
import { Security } from './utils/Security.js';

document.addEventListener('DOMContentLoaded', () => {
    const switchBtns = document.querySelectorAll('.switch-btn');
    const forms = document.querySelectorAll('.auth-form');
    const switchIndicator = document.querySelector('.switch-indicator');
    const loginForm = document.getElementById('loginForm');
    const signupForm = document.getElementById('signupForm');

    // Check if user is already authenticated
    checkAuthenticationStatus();

    // Authentication status check function
    async function checkAuthenticationStatus() {
        if (api.isAuthenticated()) {
            try {
                const profile = await api.getProfile();
                if (profile.user) {
                    // User is authenticated, redirect to appropriate dashboard
                    if (profile.user.role === 'admin') {
                        window.location.href = '/admin.html';
                    } else {
                        window.location.href = '/dashboard.html';
                    }
                }
            } catch (error) {
                // Token might be invalid, clear it
                console.log('Authentication check failed, clearing tokens');
                api.client.clearTokens();
            }
        }
    }

    switchBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // Remove active class from all buttons and forms
            switchBtns.forEach(b => b.classList.remove('active'));
            forms.forEach(f => f.classList.remove('active'));

            // Add active class to clicked button and corresponding form
            btn.classList.add('active');
            const formId = btn.dataset.form;
            const targetForm = document.getElementById(`${formId}Form`);
            if (targetForm) {
                targetForm.classList.add('active');
            }

            // Move the switch indicator
            if (formId === 'signup') {
                switchIndicator.style.transform = 'translateX(100%)';
            } else {
                switchIndicator.style.transform = 'translateX(0)';
            }
        });
    });

    if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            // Show loading state
            const submitBtn = loginForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Signing in...';
            submitBtn.disabled = true;

            try {
                // Get form data
                const formData = new FormData(loginForm);
                const email = formData.get('email');
                const password = formData.get('password');

                // Validate form
                if (!FormValidator.validateRequiredFields(loginForm)) {
                    Alert.show('Please fill out all required fields correctly.', 'error');
                    return;
                }

                // Validate email format
                if (!FormValidator.validateEmail(email)) {
                    Alert.show('Please enter a valid email address.', 'error');
                    return;
                }

                // Call login API (password hashing is handled by backend)
                const response = await api.login({
                    email: email.toLowerCase().trim(),
                    password: password
                });

                // Handle successful login
                if (response.user && response.tokens) {
                    Alert.show('Login successful! Redirecting...', 'success');

                    // Redirect based on user role
                    setTimeout(() => {
                        if (response.user.role === 'admin') {
                            window.location.href = '/admin.html';
                        } else {
                            window.location.href = '/dashboard.html';
                        }
                    }, 1000);
                }
            } catch (error) {
                console.error('Login error:', error);
                Alert.show(error.message || 'Login failed. Please check your credentials.', 'error');
            } finally {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
    }

    // Signup form handler
    if (signupForm) {
        signupForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            // Show loading state
            const submitBtn = signupForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Creating account...';
            submitBtn.disabled = true;

            try {
                // Get form data
                const formData = new FormData(signupForm);
                const email = formData.get('email');
                const password = formData.get('password');
                const confirmPassword = formData.get('confirmPassword');
                const firstName = formData.get('firstName');
                const lastName = formData.get('lastName');
                const phone = formData.get('phone');

                // Validate form
                if (!FormValidator.validateRequiredFields(signupForm)) {
                    Alert.show('Please fill out all required fields correctly.', 'error');
                    return;
                }

                // Validate email format
                if (!FormValidator.validateEmail(email)) {
                    Alert.show('Please enter a valid email address.', 'error');
                    return;
                }

                // Validate password match
                if (password !== confirmPassword) {
                    Alert.show('Passwords do not match', 'error');
                    return;
                }

                // Validate password strength
                const passwordValidation = Security.validatePassword(password);
                if (!passwordValidation.isValid) {
                    Alert.show(passwordValidation.message, 'error');
                    return;
                }

                // Call registration API
                const response = await api.register({
                    email: email.toLowerCase().trim(),
                    password: password,
                    firstName: firstName.trim(),
                    lastName: lastName.trim(),
                    phone: phone ? phone.trim() : undefined
                });

                // Handle successful registration
                if (response.user && response.tokens) {
                    Alert.show('Account created successfully! Welcome to Magic Menu!', 'success');

                    // Redirect to dashboard
                    setTimeout(() => {
                        window.location.href = '/dashboard.html';
                    }, 1500);
                }
            } catch (error) {
                console.error('Registration error:', error);
                Alert.show(error.message || 'Registration failed. Please try again.', 'error');
            } finally {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
    }

    // Implement rate limiting for login attempts
    let loginAttempts = 0;
    const MAX_LOGIN_ATTEMPTS = 5;
    const LOCKOUT_TIME = 15 * 60 * 1000; // 15 minutes
    
    function checkLoginAttempts() {
        if (loginAttempts >= MAX_LOGIN_ATTEMPTS) {
            const lockoutEnd = parseInt(localStorage.getItem('loginLockoutEnd') || '0');
            if (Date.now() < lockoutEnd) {
                Alert.show('Too many login attempts. Please try again later.', 'error');
                return false;
            }
            // Reset after lockout period
            loginAttempts = 0;
            localStorage.removeItem('loginLockoutEnd');
        }
        return true;
    }

    function handleFailedLogin() {
        loginAttempts++;
        if (loginAttempts >= MAX_LOGIN_ATTEMPTS) {
            localStorage.setItem('loginLockoutEnd', String(Date.now() + LOCKOUT_TIME));
        }
    }
});




// Inside account.js
document.querySelectorAll('.toggle-password').forEach(button => {
    button.addEventListener('click', function() {
        const passwordInput = this.previousElementSibling;
        const icon = this.querySelector('i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
});
