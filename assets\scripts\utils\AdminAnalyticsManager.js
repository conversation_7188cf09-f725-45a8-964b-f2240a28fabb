import { api } from '../api.js';
import { Alert } from './Alert.js';
import { formatPrice } from '../config/currency.js';

export class AdminAnalyticsManager {
    constructor() {
        this.analyticsData = {};
        this.currentPeriod = '30d';
        this.currentView = 'overview';
        this.charts = {};
        this.isLoading = false;
        this.autoRefreshInterval = null;
        
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadAnalyticsData();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // Period selection
        document.addEventListener('change', (e) => {
            if (e.target.matches('#analytics-period')) {
                this.updatePeriod(e.target.value);
            }
        });

        // View switching
        document.addEventListener('click', (e) => {
            if (e.target.matches('.analytics-tab')) {
                this.switchView(e.target.dataset.view);
            }
        });

        // Export buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.export-report-btn')) {
                e.preventDefault();
                this.exportReport(e.target.dataset.type);
            }
        });

        // Refresh button
        document.addEventListener('click', async (e) => {
            if (e.target.matches('#refresh-analytics-btn')) {
                e.preventDefault();
                await this.loadAnalyticsData();
            }
        });

        // Date range picker
        document.addEventListener('change', (e) => {
            if (e.target.matches('#custom-date-start') || e.target.matches('#custom-date-end')) {
                this.updateCustomDateRange();
            }
        });

        // Auto-refresh toggle
        document.addEventListener('change', (e) => {
            if (e.target.matches('#auto-refresh-analytics')) {
                if (e.target.checked) {
                    this.startAutoRefresh();
                } else {
                    this.stopAutoRefresh();
                }
            }
        });
    }

    async loadAnalyticsData() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading(true);

        try {
            const params = { period: this.currentPeriod };
            
            // Add custom date range if applicable
            if (this.currentPeriod === 'custom') {
                const startDate = document.querySelector('#custom-date-start')?.value;
                const endDate = document.querySelector('#custom-date-end')?.value;
                if (startDate && endDate) {
                    params.startDate = startDate;
                    params.endDate = endDate;
                }
            }

            // Load all analytics data in parallel
            const [salesData, userStats, orderStats, menuStats] = await Promise.all([
                api.getSalesAnalytics(params),
                this.getUserAnalytics(params),
                this.getOrderAnalytics(params),
                this.getMenuAnalytics(params)
            ]);

            this.analyticsData = {
                sales: salesData.analytics || {},
                users: userStats,
                orders: orderStats,
                menu: menuStats,
                lastUpdated: new Date()
            };

            this.renderCurrentView();

        } catch (error) {
            console.error('Failed to load analytics data:', error);
            Alert.show('Failed to load analytics data', 'error');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    async getUserAnalytics(params) {
        try {
            const response = await api.getAdminUsers({ ...params, limit: 1000 });
            const users = response.users || [];
            
            return this.calculateUserMetrics(users);
        } catch (error) {
            console.error('Failed to get user analytics:', error);
            return {};
        }
    }

    async getOrderAnalytics(params) {
        try {
            const response = await api.getAdminOrders({ ...params, limit: 1000 });
            const orders = response.orders || [];
            
            return this.calculateOrderMetrics(orders);
        } catch (error) {
            console.error('Failed to get order analytics:', error);
            return {};
        }
    }

    async getMenuAnalytics(params) {
        try {
            const [itemsResponse, categoriesResponse] = await Promise.all([
                api.getMenuItems(),
                api.getMenuCategories()
            ]);
            
            return this.calculateMenuMetrics(itemsResponse.items || [], categoriesResponse.categories || []);
        } catch (error) {
            console.error('Failed to get menu analytics:', error);
            return {};
        }
    }

    calculateUserMetrics(users) {
        const now = new Date();
        const periodStart = this.getPeriodStartDate();
        
        const newUsers = users.filter(u => new Date(u.createdAt) >= periodStart);
        const activeUsers = users.filter(u => u.isActive);
        const usersWithOrders = users.filter(u => (u.orderCount || 0) > 0);
        
        return {
            totalUsers: users.length,
            newUsers: newUsers.length,
            activeUsers: activeUsers.length,
            usersWithOrders: usersWithOrders.length,
            conversionRate: users.length > 0 ? (usersWithOrders.length / users.length) * 100 : 0,
            averageOrdersPerUser: users.reduce((sum, u) => sum + (u.orderCount || 0), 0) / users.length,
            totalCustomerValue: users.reduce((sum, u) => sum + (u.totalSpent || 0), 0),
            averageCustomerValue: users.length > 0 ? users.reduce((sum, u) => sum + (u.totalSpent || 0), 0) / users.length : 0,
            userGrowthRate: this.calculateGrowthRate(users, 'createdAt')
        };
    }

    calculateOrderMetrics(orders) {
        const periodStart = this.getPeriodStartDate();
        const periodOrders = orders.filter(o => new Date(o.createdAt) >= periodStart);
        
        const statusCounts = {};
        const dailyOrders = {};
        const hourlyOrders = {};
        
        periodOrders.forEach(order => {
            // Status counts
            statusCounts[order.status] = (statusCounts[order.status] || 0) + 1;
            
            // Daily breakdown
            const date = new Date(order.createdAt).toDateString();
            if (!dailyOrders[date]) {
                dailyOrders[date] = { count: 0, revenue: 0 };
            }
            dailyOrders[date].count++;
            dailyOrders[date].revenue += parseFloat(order.totalAmount) || 0;
            
            // Hourly breakdown
            const hour = new Date(order.createdAt).getHours();
            hourlyOrders[hour] = (hourlyOrders[hour] || 0) + 1;
        });
        
        const totalRevenue = periodOrders.reduce((sum, o) => sum + (parseFloat(o.totalAmount) || 0), 0);
        const averageOrderValue = periodOrders.length > 0 ? totalRevenue / periodOrders.length : 0;
        
        return {
            totalOrders: periodOrders.length,
            totalRevenue,
            averageOrderValue,
            statusCounts,
            dailyOrders,
            hourlyOrders,
            completionRate: periodOrders.length > 0 ? ((statusCounts.delivered || 0) / periodOrders.length) * 100 : 0,
            cancellationRate: periodOrders.length > 0 ? ((statusCounts.cancelled || 0) / periodOrders.length) * 100 : 0,
            orderGrowthRate: this.calculateGrowthRate(orders, 'createdAt')
        };
    }

    calculateMenuMetrics(items, categories) {
        const availableItems = items.filter(i => i.is_available);
        const featuredItems = items.filter(i => i.is_featured);
        
        const categoryStats = categories.map(category => {
            const categoryItems = items.filter(i => i.category_id === category.id);
            return {
                ...category,
                itemCount: categoryItems.length,
                availableCount: categoryItems.filter(i => i.is_available).length,
                averagePrice: categoryItems.length > 0 ? 
                    categoryItems.reduce((sum, i) => sum + (parseFloat(i.price) || 0), 0) / categoryItems.length : 0
            };
        });
        
        const priceRanges = {
            under1000: items.filter(i => parseFloat(i.price) < 1000).length,
            '1000-2500': items.filter(i => parseFloat(i.price) >= 1000 && parseFloat(i.price) < 2500).length,
            '2500-5000': items.filter(i => parseFloat(i.price) >= 2500 && parseFloat(i.price) < 5000).length,
            over5000: items.filter(i => parseFloat(i.price) >= 5000).length
        };
        
        return {
            totalItems: items.length,
            availableItems: availableItems.length,
            featuredItems: featuredItems.length,
            totalCategories: categories.length,
            categoryStats,
            priceRanges,
            averagePrice: items.length > 0 ? items.reduce((sum, i) => sum + (parseFloat(i.price) || 0), 0) / items.length : 0,
            availabilityRate: items.length > 0 ? (availableItems.length / items.length) * 100 : 0
        };
    }

    switchView(view) {
        this.currentView = view;
        
        // Update active tab
        document.querySelectorAll('.analytics-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');
        
        this.renderCurrentView();
    }

    renderCurrentView() {
        const container = document.querySelector('#analytics-content');
        if (!container) return;

        switch (this.currentView) {
            case 'overview':
                this.renderOverviewView(container);
                break;
            case 'sales':
                this.renderSalesView(container);
                break;
            case 'customers':
                this.renderCustomersView(container);
                break;
            case 'menu':
                this.renderMenuView(container);
                break;
            case 'performance':
                this.renderPerformanceView(container);
                break;
            default:
                this.renderOverviewView(container);
        }
    }

    renderOverviewView(container) {
        const { sales, users, orders, menu } = this.analyticsData;
        
        container.innerHTML = `
            <div class="analytics-overview">
                <div class="overview-header">
                    <h3>Analytics Overview</h3>
                    <div class="period-info">
                        <span>Period: ${this.getPeriodLabel()}</span>
                        <span class="last-updated">Last updated: ${this.analyticsData.lastUpdated?.toLocaleTimeString()}</span>
                    </div>
                </div>
                
                <div class="kpi-cards">
                    <div class="kpi-card revenue">
                        <div class="kpi-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="kpi-content">
                            <h4>Total Revenue</h4>
                            <div class="kpi-value">${formatPrice(orders.totalRevenue || 0)}</div>
                            <div class="kpi-change ${orders.orderGrowthRate >= 0 ? 'positive' : 'negative'}">
                                <i class="fas fa-arrow-${orders.orderGrowthRate >= 0 ? 'up' : 'down'}"></i>
                                ${Math.abs(orders.orderGrowthRate || 0).toFixed(1)}% vs previous period
                            </div>
                        </div>
                    </div>
                    
                    <div class="kpi-card orders">
                        <div class="kpi-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="kpi-content">
                            <h4>Total Orders</h4>
                            <div class="kpi-value">${orders.totalOrders || 0}</div>
                            <div class="kpi-change">
                                Avg: ${formatPrice(orders.averageOrderValue || 0)} per order
                            </div>
                        </div>
                    </div>
                    
                    <div class="kpi-card customers">
                        <div class="kpi-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="kpi-content">
                            <h4>New Customers</h4>
                            <div class="kpi-value">${users.newUsers || 0}</div>
                            <div class="kpi-change">
                                ${users.conversionRate?.toFixed(1) || 0}% conversion rate
                            </div>
                        </div>
                    </div>
                    
                    <div class="kpi-card completion">
                        <div class="kpi-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="kpi-content">
                            <h4>Order Completion</h4>
                            <div class="kpi-value">${orders.completionRate?.toFixed(1) || 0}%</div>
                            <div class="kpi-change">
                                ${orders.cancellationRate?.toFixed(1) || 0}% cancellation rate
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="overview-charts">
                    <div class="chart-container">
                        <h4>Revenue Trend</h4>
                        <canvas id="revenueTrendChart"></canvas>
                    </div>
                    
                    <div class="chart-container">
                        <h4>Order Status Distribution</h4>
                        <canvas id="orderStatusChart"></canvas>
                    </div>
                </div>
                
                <div class="quick-insights">
                    <h4>Quick Insights</h4>
                    <div class="insights-grid">
                        <div class="insight-card">
                            <h5>Peak Hours</h5>
                            <p>Most orders between ${this.getPeakHours(orders.hourlyOrders)}</p>
                        </div>
                        
                        <div class="insight-card">
                            <h5>Top Category</h5>
                            <p>${this.getTopCategory(menu.categoryStats)}</p>
                        </div>
                        
                        <div class="insight-card">
                            <h5>Customer Retention</h5>
                            <p>${users.averageOrdersPerUser?.toFixed(1) || 0} orders per customer</p>
                        </div>
                        
                        <div class="insight-card">
                            <h5>Menu Performance</h5>
                            <p>${menu.availabilityRate?.toFixed(1) || 0}% items available</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Initialize charts
        this.initializeOverviewCharts();
    }

    renderSalesView(container) {
        const { orders } = this.analyticsData;
        
        container.innerHTML = `
            <div class="sales-analytics">
                <div class="sales-header">
                    <h3>Sales Analytics</h3>
                    <div class="sales-actions">
                        <button class="btn btn-primary export-report-btn" data-type="sales">
                            <i class="fas fa-download"></i> Export Report
                        </button>
                    </div>
                </div>
                
                <div class="sales-metrics">
                    <div class="metric-card">
                        <h4>Revenue Breakdown</h4>
                        <div class="revenue-breakdown">
                            <div class="breakdown-item">
                                <span class="label">Gross Revenue:</span>
                                <span class="value">${formatPrice(orders.totalRevenue || 0)}</span>
                            </div>
                            <div class="breakdown-item">
                                <span class="label">Average Order Value:</span>
                                <span class="value">${formatPrice(orders.averageOrderValue || 0)}</span>
                            </div>
                            <div class="breakdown-item">
                                <span class="label">Orders Count:</span>
                                <span class="value">${orders.totalOrders || 0}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <h4>Daily Performance</h4>
                        <canvas id="dailySalesChart"></canvas>
                    </div>
                </div>
                
                <div class="sales-tables">
                    <div class="table-container">
                        <h4>Top Performing Days</h4>
                        <table class="analytics-table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Orders</th>
                                    <th>Revenue</th>
                                    <th>Avg Order</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${this.renderTopDays(orders.dailyOrders)}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
        
        this.initializeSalesCharts();
    }

    renderCustomersView(container) {
        const { users } = this.analyticsData;
        
        container.innerHTML = `
            <div class="customer-analytics">
                <div class="customer-header">
                    <h3>Customer Analytics</h3>
                </div>
                
                <div class="customer-metrics">
                    <div class="metric-grid">
                        <div class="metric-item">
                            <h4>Total Customers</h4>
                            <div class="metric-value">${users.totalUsers || 0}</div>
                        </div>
                        <div class="metric-item">
                            <h4>New Customers</h4>
                            <div class="metric-value">${users.newUsers || 0}</div>
                        </div>
                        <div class="metric-item">
                            <h4>Active Customers</h4>
                            <div class="metric-value">${users.activeUsers || 0}</div>
                        </div>
                        <div class="metric-item">
                            <h4>Customer LTV</h4>
                            <div class="metric-value">${formatPrice(users.averageCustomerValue || 0)}</div>
                        </div>
                    </div>
                </div>
                
                <div class="customer-charts">
                    <div class="chart-container">
                        <h4>Customer Acquisition</h4>
                        <canvas id="customerAcquisitionChart"></canvas>
                    </div>
                    
                    <div class="chart-container">
                        <h4>Customer Segments</h4>
                        <canvas id="customerSegmentsChart"></canvas>
                    </div>
                </div>
            </div>
        `;
        
        this.initializeCustomerCharts();
    }

    renderMenuView(container) {
        const { menu } = this.analyticsData;
        
        container.innerHTML = `
            <div class="menu-analytics">
                <div class="menu-header">
                    <h3>Menu Analytics</h3>
                </div>
                
                <div class="menu-overview">
                    <div class="overview-stats">
                        <div class="stat-item">
                            <h4>Total Items</h4>
                            <div class="stat-value">${menu.totalItems || 0}</div>
                        </div>
                        <div class="stat-item">
                            <h4>Available Items</h4>
                            <div class="stat-value">${menu.availableItems || 0}</div>
                        </div>
                        <div class="stat-item">
                            <h4>Categories</h4>
                            <div class="stat-value">${menu.totalCategories || 0}</div>
                        </div>
                        <div class="stat-item">
                            <h4>Avg Price</h4>
                            <div class="stat-value">${formatPrice(menu.averagePrice || 0)}</div>
                        </div>
                    </div>
                </div>
                
                <div class="menu-charts">
                    <div class="chart-container">
                        <h4>Category Distribution</h4>
                        <canvas id="categoryDistributionChart"></canvas>
                    </div>
                    
                    <div class="chart-container">
                        <h4>Price Range Distribution</h4>
                        <canvas id="priceRangeChart"></canvas>
                    </div>
                </div>
                
                <div class="category-performance">
                    <h4>Category Performance</h4>
                    <table class="analytics-table">
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Items</th>
                                <th>Available</th>
                                <th>Avg Price</th>
                                <th>Availability Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.renderCategoryStats(menu.categoryStats)}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        
        this.initializeMenuCharts();
    }

    renderPerformanceView(container) {
        const { orders } = this.analyticsData;
        
        container.innerHTML = `
            <div class="performance-analytics">
                <div class="performance-header">
                    <h3>Performance Analytics</h3>
                </div>
                
                <div class="performance-metrics">
                    <div class="performance-grid">
                        <div class="performance-card">
                            <h4>Order Fulfillment</h4>
                            <div class="performance-chart">
                                <canvas id="fulfillmentChart"></canvas>
                            </div>
                        </div>
                        
                        <div class="performance-card">
                            <h4>Hourly Distribution</h4>
                            <div class="performance-chart">
                                <canvas id="hourlyDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="performance-insights">
                    <h4>Performance Insights</h4>
                    <div class="insights-list">
                        <div class="insight-item">
                            <i class="fas fa-clock"></i>
                            <span>Peak ordering time: ${this.getPeakHours(orders.hourlyOrders)}</span>
                        </div>
                        <div class="insight-item">
                            <i class="fas fa-chart-line"></i>
                            <span>Order completion rate: ${orders.completionRate?.toFixed(1) || 0}%</span>
                        </div>
                        <div class="insight-item">
                            <i class="fas fa-times-circle"></i>
                            <span>Cancellation rate: ${orders.cancellationRate?.toFixed(1) || 0}%</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.initializePerformanceCharts();
    }

    // Chart initialization methods
    initializeOverviewCharts() {
        if (window.Chart) {
            this.initializeRevenueTrendChart();
            this.initializeOrderStatusChart();
        }
    }

    initializeSalesCharts() {
        if (window.Chart) {
            this.initializeDailySalesChart();
        }
    }

    initializeCustomerCharts() {
        if (window.Chart) {
            this.initializeCustomerAcquisitionChart();
            this.initializeCustomerSegmentsChart();
        }
    }

    initializeMenuCharts() {
        if (window.Chart) {
            this.initializeCategoryDistributionChart();
            this.initializePriceRangeChart();
        }
    }

    initializePerformanceCharts() {
        if (window.Chart) {
            this.initializeFulfillmentChart();
            this.initializeHourlyDistributionChart();
        }
    }

    // Utility methods
    getPeriodStartDate() {
        const now = new Date();
        switch (this.currentPeriod) {
            case '7d':
                return new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
            case '30d':
                return new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
            case '90d':
                return new Date(now.getTime() - (90 * 24 * 60 * 60 * 1000));
            case '1y':
                return new Date(now.getTime() - (365 * 24 * 60 * 60 * 1000));
            default:
                return new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
        }
    }

    getPeriodLabel() {
        const labels = {
            '7d': 'Last 7 Days',
            '30d': 'Last 30 Days',
            '90d': 'Last 90 Days',
            '1y': 'Last Year',
            'custom': 'Custom Range'
        };
        return labels[this.currentPeriod] || 'Last 30 Days';
    }

    calculateGrowthRate(data, dateField) {
        // Simple growth rate calculation
        const now = new Date();
        const periodStart = this.getPeriodStartDate();
        const previousPeriodStart = new Date(periodStart.getTime() - (now.getTime() - periodStart.getTime()));
        
        const currentPeriodData = data.filter(item => {
            const date = new Date(item[dateField]);
            return date >= periodStart && date <= now;
        });
        
        const previousPeriodData = data.filter(item => {
            const date = new Date(item[dateField]);
            return date >= previousPeriodStart && date < periodStart;
        });
        
        if (previousPeriodData.length === 0) return 0;
        
        return ((currentPeriodData.length - previousPeriodData.length) / previousPeriodData.length) * 100;
    }

    getPeakHours(hourlyData) {
        if (!hourlyData || Object.keys(hourlyData).length === 0) return 'N/A';
        
        const maxHour = Object.keys(hourlyData).reduce((a, b) => 
            hourlyData[a] > hourlyData[b] ? a : b
        );
        
        return `${maxHour}:00 - ${parseInt(maxHour) + 1}:00`;
    }

    getTopCategory(categoryStats) {
        if (!categoryStats || categoryStats.length === 0) return 'N/A';
        
        const topCategory = categoryStats.reduce((a, b) => 
            a.itemCount > b.itemCount ? a : b
        );
        
        return `${topCategory.name} (${topCategory.itemCount} items)`;
    }

    renderTopDays(dailyOrders) {
        if (!dailyOrders) return '<tr><td colspan="4">No data available</td></tr>';
        
        const sortedDays = Object.entries(dailyOrders)
            .sort(([,a], [,b]) => b.revenue - a.revenue)
            .slice(0, 5);
        
        return sortedDays.map(([date, data]) => `
            <tr>
                <td>${new Date(date).toLocaleDateString()}</td>
                <td>${data.count}</td>
                <td>${formatPrice(data.revenue)}</td>
                <td>${formatPrice(data.revenue / data.count)}</td>
            </tr>
        `).join('');
    }

    renderCategoryStats(categoryStats) {
        if (!categoryStats || categoryStats.length === 0) {
            return '<tr><td colspan="5">No data available</td></tr>';
        }
        
        return categoryStats.map(category => `
            <tr>
                <td>${category.name}</td>
                <td>${category.itemCount}</td>
                <td>${category.availableCount}</td>
                <td>${formatPrice(category.averagePrice)}</td>
                <td>${category.itemCount > 0 ? ((category.availableCount / category.itemCount) * 100).toFixed(1) : 0}%</td>
            </tr>
        `).join('');
    }

    updatePeriod(period) {
        this.currentPeriod = period;
        this.loadAnalyticsData();
    }

    updateCustomDateRange() {
        if (this.currentPeriod === 'custom') {
            this.loadAnalyticsData();
        }
    }

    startAutoRefresh() {
        this.stopAutoRefresh();
        this.autoRefreshInterval = setInterval(() => {
            this.loadAnalyticsData();
        }, 5 * 60 * 1000); // Refresh every 5 minutes
    }

    stopAutoRefresh() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }
    }

    exportReport(type) {
        const data = this.analyticsData[type] || this.analyticsData;
        const csvContent = this.generateReportCSV(data, type);
        
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `${type}-report-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    generateReportCSV(data, type) {
        // Generate CSV based on report type
        switch (type) {
            case 'sales':
                return this.generateSalesCSV(data);
            case 'customers':
                return this.generateCustomersCSV(data);
            case 'menu':
                return this.generateMenuCSV(data);
            default:
                return this.generateOverviewCSV(data);
        }
    }

    generateSalesCSV(data) {
        const headers = ['Metric', 'Value'];
        const rows = [
            ['Total Revenue', formatPrice(data.totalRevenue || 0)],
            ['Total Orders', data.totalOrders || 0],
            ['Average Order Value', formatPrice(data.averageOrderValue || 0)],
            ['Completion Rate', `${data.completionRate?.toFixed(1) || 0}%`],
            ['Cancellation Rate', `${data.cancellationRate?.toFixed(1) || 0}%`]
        ];
        
        return [headers, ...rows].map(row => 
            row.map(field => `"${field}"`).join(',')
        ).join('\n');
    }

    generateOverviewCSV(data) {
        const headers = ['Category', 'Metric', 'Value'];
        const rows = [
            ['Sales', 'Total Revenue', formatPrice(data.orders?.totalRevenue || 0)],
            ['Sales', 'Total Orders', data.orders?.totalOrders || 0],
            ['Sales', 'Average Order Value', formatPrice(data.orders?.averageOrderValue || 0)],
            ['Customers', 'Total Users', data.users?.totalUsers || 0],
            ['Customers', 'New Users', data.users?.newUsers || 0],
            ['Customers', 'Conversion Rate', `${data.users?.conversionRate?.toFixed(1) || 0}%`],
            ['Menu', 'Total Items', data.menu?.totalItems || 0],
            ['Menu', 'Available Items', data.menu?.availableItems || 0],
            ['Menu', 'Average Price', formatPrice(data.menu?.averagePrice || 0)]
        ];
        
        return [headers, ...rows].map(row => 
            row.map(field => `"${field}"`).join(',')
        ).join('\n');
    }

    showLoading(show) {
        const loader = document.querySelector('.analytics-loader');
        if (loader) {
            loader.style.display = show ? 'block' : 'none';
        }
    }

    destroy() {
        this.stopAutoRefresh();
        
        // Destroy charts
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        this.charts = {};
    }
}

// Create global admin analytics manager instance
export const adminAnalyticsManager = new AdminAnalyticsManager();
