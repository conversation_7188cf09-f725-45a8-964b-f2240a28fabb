# Frontend Integration Guide

## Overview

This guide explains how to integrate the new backend-connected frontend system with your existing Magic Menu website. The integration provides a complete full-stack solution with authentication, cart management, order processing, and admin functionality.

## 🚀 Quick Start

### 1. Include the Main Application Script

Add this to your HTML pages (preferably in the `<head>` section):

```html
<!-- Main application entry point -->
<script type="module" src="/assets/scripts/app.js"></script>
```

### 2. Update API Configuration

The system automatically loads configuration from `/config/api-config.json`. Update the `baseUrl` to match your backend server:

```json
{
  "baseUrl": "http://localhost:3000",
  "endpoints": { ... }
}
```

### 3. Start the Backend Server

```bash
cd server
npm install
npm run dev
```

## 📁 New File Structure

The integration adds these new files to your project:

```
assets/scripts/
├── app.js                    # Main application entry point
├── api.js                    # Updated API client with backend integration
├── utils/
│   ├── AuthManager.js        # Authentication state management
│   ├── CartManager.js        # Cart operations with backend sync
│   ├── MenuManager.js        # Dynamic menu loading from backend
│   ├── OrderManager.js       # Order processing and tracking
│   ├── AdminManager.js       # Admin panel functionality
│   ├── ContactManager.js     # Contact form backend integration
│   └── StateManager.js       # Global state coordination
└── config/
    └── currency.js           # Currency formatting utilities

server/                       # Complete backend system
├── index.js                  # Express server
├── models/                   # Database models
├── routes/                   # API endpoints
├── middleware/               # Authentication & security
├── database/                 # Schema & migrations
└── docs/                     # API documentation
```

## 🔧 Integration Steps

### Step 1: Update HTML Pages

Add the module script to all your HTML pages:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Your existing head content -->
    <script type="module" src="/assets/scripts/app.js"></script>
</head>
<body>
    <!-- Your existing content -->
</body>
</html>
```

### Step 2: Update Form Classes

Ensure your forms have the correct classes for automatic integration:

**Login/Register Forms:**
```html
<form class="contact-form" id="loginForm">
  <!-- Your form fields -->
</form>

<form class="contact-form" id="signupForm">
  <!-- Your form fields -->
</form>
```

**Contact Form:**
```html
<form class="contact-form" id="contact-form">
  <!-- Your form fields -->
</form>
```

**Menu Items:**
```html
<div class="menu-item-card" data-id="item-uuid">
  <!-- Item content -->
  <button class="add-to-cart-btn" data-item-id="item-uuid">Add to Cart</button>
</div>
```

### Step 3: Update Navigation

Add authentication-aware navigation:

```html
<nav>
  <!-- Existing navigation -->
  
  <!-- Authentication links -->
  <a href="/account.html" class="login-btn">Login</a>
  <button class="logout-btn" style="display: none;">Logout</button>
  <span class="user-info" style="display: none;"></span>
  
  <!-- Admin link (shown only to admins) -->
  <a href="/admin.html" class="admin-only" style="display: none;">Admin Panel</a>
  
  <!-- Cart with count -->
  <a href="/cart.html" class="cart-link">
    Cart <span class="cart-count">0</span>
  </a>
</nav>
```

## 🎯 Key Features

### Authentication System

**Automatic Login/Logout:**
- Forms with classes `.contact-form` and IDs `#loginForm` or `#signupForm` are automatically handled
- JWT tokens are stored and managed automatically
- User sessions persist across page reloads
- Multi-tab synchronization

**Usage:**
```javascript
// Check if user is authenticated
if (authManager.isAuthenticated()) {
  console.log('User is logged in:', authManager.getCurrentUser());
}

// Listen for auth changes
authManager.onAuthChange((event, user) => {
  if (event === 'login') {
    console.log('User logged in:', user);
  }
});
```

### Cart Management

**Real-time Cart Updates:**
- Add to cart buttons automatically sync with backend
- Cart persists for both guest and authenticated users
- Guest carts merge with user carts on login

**Usage:**
```javascript
// Add item to cart
await cartManager.addItem('item-uuid', 2, 'Extra spicy');

// Get cart contents
const cart = cartManager.getCart();

// Listen for cart changes
cartManager.onCartChange((event, data, cart) => {
  console.log('Cart updated:', cart);
});
```

### Menu System

**Dynamic Menu Loading:**
- Menu items load from backend API
- Search and filtering functionality
- Category-based navigation
- Real-time availability updates

**Usage:**
```javascript
// Search menu items
await menuManager.searchMenu('jollof rice');

// Filter by category
await menuManager.filterByCategory('category-uuid');

// Refresh menu data
await menuManager.refreshMenu();
```

### Order Processing

**Complete Order Flow:**
- Checkout form integration
- Order tracking by order number
- Order history for authenticated users

**Usage:**
```javascript
// Create order from cart
const order = await orderManager.createOrder({
  customerInfo: { /* customer details */ },
  paymentInfo: { method: 'cash' }
});

// Track order
const orderStatus = await orderManager.trackOrder('MM-12345');
```

### Admin Panel

**Full Admin Functionality:**
- User management
- Order status updates
- Sales analytics
- Menu management (via API)

**Usage:**
```javascript
// Get dashboard data
const dashboard = await adminManager.loadDashboardData();

// Update order status
await adminManager.updateOrderStatus('order-id', 'confirmed');
```

## 🔒 Security Features

- **JWT Authentication:** Secure token-based authentication
- **Role-based Access:** Customer/Admin role separation
- **Rate Limiting:** Contact form and auth endpoint protection
- **Input Validation:** All forms validated client and server-side
- **CSRF Protection:** Cross-site request forgery prevention

## 📱 Responsive Design

The integration maintains your existing responsive design while adding:
- Mobile-optimized cart sidebar
- Touch-friendly admin controls
- Responsive data tables
- Mobile-first form layouts

## 🎨 UI/UX Enhancements

### Loading States
```html
<!-- Automatic loading indicators -->
<button class="add-to-cart-btn" data-item-id="uuid">
  Add to Cart
  <!-- Automatically shows "Adding..." when clicked -->
</button>
```

### Notifications
```javascript
// Automatic success/error messages
Alert.show('Item added to cart!', 'success');
Alert.show('Login failed', 'error');
```

### Real-time Updates
- Cart count updates automatically
- Order status changes reflect immediately
- User authentication state syncs across tabs

## 🛠️ Development Tools

When running in development mode (`localhost`), additional debugging tools are available:

```javascript
// Access all managers and utilities
window.magicMenu.cartManager.getCart();
window.magicMenu.authManager.getCurrentUser();
window.magicMenu.stateManager.getState();
```

## 📊 Analytics Integration

The system includes built-in analytics tracking:

```javascript
// Track custom events
Analytics.track('menu_item_viewed', {
  item_id: 'uuid',
  category: 'Nigerian Dishes'
});
```

## 🚨 Error Handling

Comprehensive error handling with user-friendly messages:

- Network errors show retry options
- Validation errors highlight specific fields
- Server errors display helpful messages
- Offline detection with graceful degradation

## 🔄 Migration from Static to Dynamic

### Before (Static)
```html
<div class="menu-item">
  <h3>Jollof Rice</h3>
  <p>₦2,500</p>
  <button onclick="addToCart('static-id')">Add to Cart</button>
</div>
```

### After (Dynamic)
```html
<!-- Items are now loaded dynamically from backend -->
<div class="menu-container">
  <!-- MenuManager populates this automatically -->
</div>
```

## 📈 Performance Optimizations

- **Lazy Loading:** Menu items load on demand
- **Caching:** API responses cached appropriately
- **Debounced Search:** Search requests optimized
- **Efficient Updates:** Only changed data re-renders

## 🧪 Testing

Test the integration:

```bash
# Run backend API tests
node server/tests/api-test.js

# Test specific functionality
node server/tests/api-test.js auth
node server/tests/api-test.js menu
node server/tests/api-test.js cart
```

## 🚀 Deployment

### Development
```bash
# Start backend
cd server && npm run dev

# Serve frontend (if using a local server)
python -m http.server 8000
```

### Production
1. Update `config/api-config.json` with production API URL
2. Deploy backend to your server
3. Update frontend files on your web server
4. Configure environment variables

## 📞 Support

If you encounter issues:

1. Check browser console for errors
2. Verify backend server is running
3. Check API configuration
4. Review network requests in browser dev tools
5. Use development tools: `window.magicMenu`

## 🎉 Next Steps

With the integration complete, you now have:

✅ **Full-stack restaurant ordering system**
✅ **User authentication and profiles**
✅ **Real-time cart management**
✅ **Order processing and tracking**
✅ **Admin panel for management**
✅ **Contact form with backend**
✅ **Comprehensive API documentation**

Your Magic Menu website is now a complete, production-ready restaurant ordering platform!
