import { api } from '../api.js';
import { Alert } from './Alert.js';

export class AdminSystemManager {
    constructor() {
        this.settings = {};
        this.backupData = {};
        this.isLoading = false;
        this.currentView = 'general';
        
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadSystemSettings();
    }

    setupEventListeners() {
        // Settings navigation
        document.addEventListener('click', (e) => {
            if (e.target.matches('.settings-nav-btn')) {
                this.switchSettingsView(e.target.dataset.view);
            }
        });

        // Form submissions
        document.addEventListener('submit', async (e) => {
            if (e.target.matches('#general-settings-form')) {
                e.preventDefault();
                await this.saveGeneralSettings(e.target);
            }
            
            if (e.target.matches('#notification-settings-form')) {
                e.preventDefault();
                await this.saveNotificationSettings(e.target);
            }
            
            if (e.target.matches('#security-settings-form')) {
                e.preventDefault();
                await this.saveSecuritySettings(e.target);
            }
        });

        // System actions
        document.addEventListener('click', async (e) => {
            if (e.target.matches('#backup-system-btn')) {
                e.preventDefault();
                await this.createSystemBackup();
            }
            
            if (e.target.matches('#restore-system-btn')) {
                e.preventDefault();
                await this.showRestoreDialog();
            }
            
            if (e.target.matches('#clear-cache-btn')) {
                e.preventDefault();
                await this.clearSystemCache();
            }
            
            if (e.target.matches('#test-email-btn')) {
                e.preventDefault();
                await this.testEmailSettings();
            }
        });

        // File uploads
        document.addEventListener('change', (e) => {
            if (e.target.matches('#logo-upload')) {
                this.handleLogoUpload(e.target);
            }
            
            if (e.target.matches('#backup-file-input')) {
                this.handleBackupFileSelect(e.target);
            }
        });

        // Real-time validation
        document.addEventListener('input', (e) => {
            if (e.target.matches('.validate-on-input')) {
                this.validateField(e.target);
            }
        });
    }

    async loadSystemSettings() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading(true);

        try {
            const response = await api.getSystemSettings();
            this.settings = response.settings || {};
            this.renderCurrentView();
            
        } catch (error) {
            console.error('Failed to load system settings:', error);
            Alert.show('Failed to load system settings', 'error');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    switchSettingsView(view) {
        this.currentView = view;
        
        // Update active navigation
        document.querySelectorAll('.settings-nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');
        
        this.renderCurrentView();
    }

    renderCurrentView() {
        const container = document.querySelector('#system-settings-content');
        if (!container) return;

        switch (this.currentView) {
            case 'general':
                this.renderGeneralSettings(container);
                break;
            case 'notifications':
                this.renderNotificationSettings(container);
                break;
            case 'security':
                this.renderSecuritySettings(container);
                break;
            case 'backup':
                this.renderBackupSettings(container);
                break;
            case 'maintenance':
                this.renderMaintenanceSettings(container);
                break;
            default:
                this.renderGeneralSettings(container);
        }
    }

    renderGeneralSettings(container) {
        const settings = this.settings;
        
        container.innerHTML = `
            <div class="settings-section">
                <h3>General Settings</h3>
                
                <form id="general-settings-form" class="settings-form">
                    <div class="form-section">
                        <h4>Restaurant Information</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="restaurant-name">Restaurant Name</label>
                                <input type="text" id="restaurant-name" name="restaurantName" 
                                       value="${settings.restaurantName || 'Magic Menu'}" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="restaurant-tagline">Tagline</label>
                                <input type="text" id="restaurant-tagline" name="restaurantTagline" 
                                       value="${settings.restaurantTagline || 'Delicious Nigerian Cuisine'}">
                            </div>
                            
                            <div class="form-group">
                                <label for="restaurant-phone">Phone Number</label>
                                <input type="tel" id="restaurant-phone" name="restaurantPhone" 
                                       value="${settings.restaurantPhone || ''}" class="validate-on-input">
                            </div>
                            
                            <div class="form-group">
                                <label for="restaurant-email">Email Address</label>
                                <input type="email" id="restaurant-email" name="restaurantEmail" 
                                       value="${settings.restaurantEmail || ''}" class="validate-on-input">
                            </div>
                            
                            <div class="form-group full-width">
                                <label for="restaurant-address">Address</label>
                                <textarea id="restaurant-address" name="restaurantAddress" rows="3">${settings.restaurantAddress || ''}</textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h4>Business Hours</h4>
                        <div class="hours-grid">
                            ${this.renderBusinessHours(settings.businessHours || {})}
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h4>Order Configuration</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="min-order-amount">Minimum Order Amount (₦)</label>
                                <input type="number" id="min-order-amount" name="minOrderAmount" 
                                       value="${settings.minOrderAmount || 0}" min="0" step="100">
                            </div>
                            
                            <div class="form-group">
                                <label for="delivery-fee">Delivery Fee (₦)</label>
                                <input type="number" id="delivery-fee" name="deliveryFee" 
                                       value="${settings.deliveryFee || 500}" min="0" step="50">
                            </div>
                            
                            <div class="form-group">
                                <label for="tax-rate">Tax Rate (%)</label>
                                <input type="number" id="tax-rate" name="taxRate" 
                                       value="${settings.taxRate || 7.5}" min="0" max="100" step="0.1">
                            </div>
                            
                            <div class="form-group">
                                <label for="prep-time">Average Prep Time (minutes)</label>
                                <input type="number" id="prep-time" name="averagePrepTime" 
                                       value="${settings.averagePrepTime || 30}" min="5" step="5">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h4>Website Settings</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="site-title">Site Title</label>
                                <input type="text" id="site-title" name="siteTitle" 
                                       value="${settings.siteTitle || 'Magic Menu'}">
                            </div>
                            
                            <div class="form-group">
                                <label for="site-description">Meta Description</label>
                                <textarea id="site-description" name="siteDescription" rows="3">${settings.siteDescription || ''}</textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="currency">Currency</label>
                                <select id="currency" name="currency">
                                    <option value="NGN" ${settings.currency === 'NGN' ? 'selected' : ''}>Nigerian Naira (₦)</option>
                                    <option value="USD" ${settings.currency === 'USD' ? 'selected' : ''}>US Dollar ($)</option>
                                    <option value="EUR" ${settings.currency === 'EUR' ? 'selected' : ''}>Euro (€)</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="timezone">Timezone</label>
                                <select id="timezone" name="timezone">
                                    <option value="Africa/Lagos" ${settings.timezone === 'Africa/Lagos' ? 'selected' : ''}>Africa/Lagos</option>
                                    <option value="UTC" ${settings.timezone === 'UTC' ? 'selected' : ''}>UTC</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="allowGuestOrders" 
                                       ${settings.allowGuestOrders !== false ? 'checked' : ''}>
                                Allow guest orders (without registration)
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="enableOrderTracking" 
                                       ${settings.enableOrderTracking !== false ? 'checked' : ''}>
                                Enable public order tracking
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save General Settings
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="location.reload()">
                            <i class="fas fa-undo"></i> Reset Changes
                        </button>
                    </div>
                </form>
            </div>
        `;
    }

    renderNotificationSettings(container) {
        const settings = this.settings.notifications || {};
        
        container.innerHTML = `
            <div class="settings-section">
                <h3>Notification Settings</h3>
                
                <form id="notification-settings-form" class="settings-form">
                    <div class="form-section">
                        <h4>Email Configuration</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="smtp-host">SMTP Host</label>
                                <input type="text" id="smtp-host" name="smtpHost" 
                                       value="${settings.smtpHost || ''}" placeholder="smtp.gmail.com">
                            </div>
                            
                            <div class="form-group">
                                <label for="smtp-port">SMTP Port</label>
                                <input type="number" id="smtp-port" name="smtpPort" 
                                       value="${settings.smtpPort || 587}" min="1" max="65535">
                            </div>
                            
                            <div class="form-group">
                                <label for="smtp-username">SMTP Username</label>
                                <input type="text" id="smtp-username" name="smtpUsername" 
                                       value="${settings.smtpUsername || ''}">
                            </div>
                            
                            <div class="form-group">
                                <label for="smtp-password">SMTP Password</label>
                                <input type="password" id="smtp-password" name="smtpPassword" 
                                       value="${settings.smtpPassword || ''}" placeholder="••••••••">
                            </div>
                            
                            <div class="form-group">
                                <label for="from-email">From Email</label>
                                <input type="email" id="from-email" name="fromEmail" 
                                       value="${settings.fromEmail || ''}" placeholder="<EMAIL>">
                            </div>
                            
                            <div class="form-group">
                                <label for="from-name">From Name</label>
                                <input type="text" id="from-name" name="fromName" 
                                       value="${settings.fromName || 'Magic Menu'}" placeholder="Magic Menu">
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" id="test-email-btn" class="btn btn-secondary">
                                <i class="fas fa-envelope"></i> Test Email Settings
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h4>Notification Preferences</h4>
                        <div class="notification-toggles">
                            <div class="toggle-group">
                                <h5>Order Notifications</h5>
                                <label class="toggle-label">
                                    <input type="checkbox" name="notifyNewOrder" 
                                           ${settings.notifyNewOrder !== false ? 'checked' : ''}>
                                    New order received
                                </label>
                                <label class="toggle-label">
                                    <input type="checkbox" name="notifyOrderStatusChange" 
                                           ${settings.notifyOrderStatusChange !== false ? 'checked' : ''}>
                                    Order status changes
                                </label>
                                <label class="toggle-label">
                                    <input type="checkbox" name="notifyOrderCancellation" 
                                           ${settings.notifyOrderCancellation !== false ? 'checked' : ''}>
                                    Order cancellations
                                </label>
                            </div>
                            
                            <div class="toggle-group">
                                <h5>User Notifications</h5>
                                <label class="toggle-label">
                                    <input type="checkbox" name="notifyNewUser" 
                                           ${settings.notifyNewUser !== false ? 'checked' : ''}>
                                    New user registrations
                                </label>
                                <label class="toggle-label">
                                    <input type="checkbox" name="notifyContactForm" 
                                           ${settings.notifyContactForm !== false ? 'checked' : ''}>
                                    Contact form submissions
                                </label>
                            </div>
                            
                            <div class="toggle-group">
                                <h5>System Notifications</h5>
                                <label class="toggle-label">
                                    <input type="checkbox" name="notifySystemErrors" 
                                           ${settings.notifySystemErrors !== false ? 'checked' : ''}>
                                    System errors
                                </label>
                                <label class="toggle-label">
                                    <input type="checkbox" name="notifyLowStock" 
                                           ${settings.notifyLowStock !== false ? 'checked' : ''}>
                                    Low stock alerts
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Notification Settings
                        </button>
                    </div>
                </form>
            </div>
        `;
    }

    renderSecuritySettings(container) {
        const settings = this.settings.security || {};
        
        container.innerHTML = `
            <div class="settings-section">
                <h3>Security Settings</h3>
                
                <form id="security-settings-form" class="settings-form">
                    <div class="form-section">
                        <h4>Authentication</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="jwt-expiry">JWT Token Expiry (hours)</label>
                                <input type="number" id="jwt-expiry" name="jwtExpiry" 
                                       value="${settings.jwtExpiry || 24}" min="1" max="168">
                            </div>
                            
                            <div class="form-group">
                                <label for="refresh-token-expiry">Refresh Token Expiry (days)</label>
                                <input type="number" id="refresh-token-expiry" name="refreshTokenExpiry" 
                                       value="${settings.refreshTokenExpiry || 30}" min="1" max="365">
                            </div>
                            
                            <div class="form-group">
                                <label for="max-login-attempts">Max Login Attempts</label>
                                <input type="number" id="max-login-attempts" name="maxLoginAttempts" 
                                       value="${settings.maxLoginAttempts || 5}" min="3" max="10">
                            </div>
                            
                            <div class="form-group">
                                <label for="lockout-duration">Lockout Duration (minutes)</label>
                                <input type="number" id="lockout-duration" name="lockoutDuration" 
                                       value="${settings.lockoutDuration || 15}" min="5" max="60">
                            </div>
                        </div>
                        
                        <div class="security-toggles">
                            <label class="toggle-label">
                                <input type="checkbox" name="requireEmailVerification" 
                                       ${settings.requireEmailVerification !== false ? 'checked' : ''}>
                                Require email verification for new accounts
                            </label>
                            <label class="toggle-label">
                                <input type="checkbox" name="enableTwoFactor" 
                                       ${settings.enableTwoFactor === true ? 'checked' : ''}>
                                Enable two-factor authentication (2FA)
                            </label>
                            <label class="toggle-label">
                                <input type="checkbox" name="logSecurityEvents" 
                                       ${settings.logSecurityEvents !== false ? 'checked' : ''}>
                                Log security events
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h4>Rate Limiting</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="api-rate-limit">API Rate Limit (requests per minute)</label>
                                <input type="number" id="api-rate-limit" name="apiRateLimit" 
                                       value="${settings.apiRateLimit || 100}" min="10" max="1000">
                            </div>
                            
                            <div class="form-group">
                                <label for="contact-rate-limit">Contact Form Rate Limit (per hour)</label>
                                <input type="number" id="contact-rate-limit" name="contactRateLimit" 
                                       value="${settings.contactRateLimit || 3}" min="1" max="10">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Security Settings
                        </button>
                    </div>
                </form>
            </div>
        `;
    }

    renderBackupSettings(container) {
        container.innerHTML = `
            <div class="settings-section">
                <h3>Backup & Restore</h3>
                
                <div class="backup-section">
                    <h4>System Backup</h4>
                    <p>Create a complete backup of your system including database, settings, and uploaded files.</p>
                    
                    <div class="backup-actions">
                        <button id="backup-system-btn" class="btn btn-primary">
                            <i class="fas fa-download"></i> Create Backup
                        </button>
                        <div class="backup-info">
                            <small>Last backup: ${this.settings.lastBackup ? new Date(this.settings.lastBackup).toLocaleString() : 'Never'}</small>
                        </div>
                    </div>
                </div>
                
                <div class="restore-section">
                    <h4>System Restore</h4>
                    <p>Restore your system from a previous backup. <strong>Warning:</strong> This will overwrite current data.</p>
                    
                    <div class="restore-actions">
                        <input type="file" id="backup-file-input" accept=".zip,.tar.gz" style="display: none;">
                        <button id="restore-system-btn" class="btn btn-warning">
                            <i class="fas fa-upload"></i> Restore from Backup
                        </button>
                    </div>
                </div>
                
                <div class="backup-schedule">
                    <h4>Automatic Backups</h4>
                    <form id="backup-schedule-form">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="enableAutoBackup" 
                                       ${this.settings.enableAutoBackup === true ? 'checked' : ''}>
                                Enable automatic backups
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label for="backup-frequency">Backup Frequency</label>
                            <select id="backup-frequency" name="backupFrequency">
                                <option value="daily" ${this.settings.backupFrequency === 'daily' ? 'selected' : ''}>Daily</option>
                                <option value="weekly" ${this.settings.backupFrequency === 'weekly' ? 'selected' : ''}>Weekly</option>
                                <option value="monthly" ${this.settings.backupFrequency === 'monthly' ? 'selected' : ''}>Monthly</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="backup-retention">Keep Backups (days)</label>
                            <input type="number" id="backup-retention" name="backupRetention" 
                                   value="${this.settings.backupRetention || 30}" min="7" max="365">
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Backup Settings
                        </button>
                    </form>
                </div>
            </div>
        `;
    }

    renderMaintenanceSettings(container) {
        container.innerHTML = `
            <div class="settings-section">
                <h3>System Maintenance</h3>
                
                <div class="maintenance-section">
                    <h4>Cache Management</h4>
                    <p>Clear system cache to improve performance and resolve issues.</p>
                    
                    <div class="cache-actions">
                        <button id="clear-cache-btn" class="btn btn-secondary">
                            <i class="fas fa-broom"></i> Clear Cache
                        </button>
                        <div class="cache-info">
                            <small>Cache size: ${this.formatBytes(this.settings.cacheSize || 0)}</small>
                        </div>
                    </div>
                </div>
                
                <div class="maintenance-mode">
                    <h4>Maintenance Mode</h4>
                    <p>Enable maintenance mode to temporarily disable the website for updates.</p>
                    
                    <form id="maintenance-form">
                        <div class="form-group">
                            <label class="toggle-label">
                                <input type="checkbox" name="maintenanceMode" 
                                       ${this.settings.maintenanceMode === true ? 'checked' : ''}>
                                Enable maintenance mode
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label for="maintenance-message">Maintenance Message</label>
                            <textarea id="maintenance-message" name="maintenanceMessage" rows="3">${this.settings.maintenanceMessage || 'We are currently performing scheduled maintenance. Please check back soon.'}</textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Maintenance Settings
                        </button>
                    </form>
                </div>
                
                <div class="system-info">
                    <h4>System Information</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Version:</label>
                            <span>1.0.0</span>
                        </div>
                        <div class="info-item">
                            <label>Database:</label>
                            <span>PostgreSQL</span>
                        </div>
                        <div class="info-item">
                            <label>Node.js:</label>
                            <span>${process?.version || 'Unknown'}</span>
                        </div>
                        <div class="info-item">
                            <label>Uptime:</label>
                            <span id="system-uptime">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Update uptime periodically
        this.updateSystemUptime();
    }

    renderBusinessHours(hours) {
        const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        
        return days.map((day, index) => {
            const dayHours = hours[day] || { open: '09:00', close: '22:00', closed: false };
            
            return `
                <div class="hours-day">
                    <label class="day-label">${dayNames[index]}</label>
                    <div class="hours-controls">
                        <label class="checkbox-label">
                            <input type="checkbox" name="${day}.closed" ${dayHours.closed ? 'checked' : ''}>
                            Closed
                        </label>
                        <input type="time" name="${day}.open" value="${dayHours.open}" 
                               ${dayHours.closed ? 'disabled' : ''}>
                        <span>to</span>
                        <input type="time" name="${day}.close" value="${dayHours.close}" 
                               ${dayHours.closed ? 'disabled' : ''}>
                    </div>
                </div>
            `;
        }).join('');
    }

    async saveGeneralSettings(form) {
        try {
            const formData = new FormData(form);
            const settings = this.processFormData(formData);
            
            await api.updateSystemSettings({ general: settings });
            Alert.show('General settings saved successfully', 'success');
            
            // Update local settings
            Object.assign(this.settings, settings);
            
        } catch (error) {
            console.error('Failed to save general settings:', error);
            Alert.show('Failed to save general settings', 'error');
        }
    }

    async saveNotificationSettings(form) {
        try {
            const formData = new FormData(form);
            const settings = this.processFormData(formData);
            
            await api.updateSystemSettings({ notifications: settings });
            Alert.show('Notification settings saved successfully', 'success');
            
        } catch (error) {
            console.error('Failed to save notification settings:', error);
            Alert.show('Failed to save notification settings', 'error');
        }
    }

    async saveSecuritySettings(form) {
        try {
            const formData = new FormData(form);
            const settings = this.processFormData(formData);
            
            await api.updateSystemSettings({ security: settings });
            Alert.show('Security settings saved successfully', 'success');
            
        } catch (error) {
            console.error('Failed to save security settings:', error);
            Alert.show('Failed to save security settings', 'error');
        }
    }

    processFormData(formData) {
        const settings = {};
        
        for (const [key, value] of formData.entries()) {
            if (key.includes('.')) {
                // Handle nested objects (like business hours)
                const [parent, child] = key.split('.');
                if (!settings[parent]) settings[parent] = {};
                settings[parent][child] = value;
            } else {
                settings[key] = value;
            }
        }
        
        // Handle checkboxes
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            if (checkbox.name) {
                if (checkbox.name.includes('.')) {
                    const [parent, child] = checkbox.name.split('.');
                    if (!settings[parent]) settings[parent] = {};
                    settings[parent][child] = checkbox.checked;
                } else {
                    settings[checkbox.name] = checkbox.checked;
                }
            }
        });
        
        return settings;
    }

    async createSystemBackup() {
        try {
            Alert.show('Creating system backup...', 'info');
            
            const response = await api.createSystemBackup();
            
            // Download the backup file
            const blob = new Blob([response.data], { type: 'application/zip' });
            const url = window.URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `magic-menu-backup-${new Date().toISOString().split('T')[0]}.zip`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            Alert.show('Backup created and downloaded successfully', 'success');
            
        } catch (error) {
            console.error('Backup creation failed:', error);
            Alert.show('Failed to create backup', 'error');
        }
    }

    async testEmailSettings() {
        try {
            Alert.show('Sending test email...', 'info');
            
            await api.testEmailSettings();
            Alert.show('Test email sent successfully', 'success');
            
        } catch (error) {
            console.error('Email test failed:', error);
            Alert.show('Failed to send test email', 'error');
        }
    }

    async clearSystemCache() {
        try {
            Alert.show('Clearing system cache...', 'info');
            
            await api.clearSystemCache();
            Alert.show('System cache cleared successfully', 'success');
            
            // Update cache size display
            this.settings.cacheSize = 0;
            this.renderCurrentView();
            
        } catch (error) {
            console.error('Cache clear failed:', error);
            Alert.show('Failed to clear cache', 'error');
        }
    }

    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';
        
        switch (field.type) {
            case 'email':
                isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
                message = isValid ? '' : 'Please enter a valid email address';
                break;
                
            case 'tel':
                isValid = /^[\+]?[0-9\s\-\(\)]{10,}$/.test(value);
                message = isValid ? '' : 'Please enter a valid phone number';
                break;
        }
        
        // Update field styling
        field.classList.toggle('invalid', !isValid);
        
        // Show/hide error message
        let errorElement = field.parentElement.querySelector('.field-error');
        if (!isValid && message) {
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.className = 'field-error';
                field.parentElement.appendChild(errorElement);
            }
            errorElement.textContent = message;
        } else if (errorElement) {
            errorElement.remove();
        }
        
        return isValid;
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    updateSystemUptime() {
        const uptimeElement = document.querySelector('#system-uptime');
        if (uptimeElement) {
            // This would typically come from the server
            uptimeElement.textContent = '2 days, 14 hours';
        }
    }

    showLoading(show) {
        const loader = document.querySelector('.system-settings-loader');
        if (loader) {
            loader.style.display = show ? 'block' : 'none';
        }
    }
}

// Create global admin system manager instance
export const adminSystemManager = new AdminSystemManager();
