# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/magic_menu
DB_HOST=localhost
DB_PORT=5432
DB_NAME=magic_menu
DB_USER=magic_menu_user
DB_PASSWORD=your_secure_password

# Server Configuration
PORT=3000
NODE_ENV=development

# Session Configuration
SESSION_SECRET=your_very_secure_session_secret_key_here
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your_jwt_refresh_secret_here
JWT_REFRESH_EXPIRES_IN=7d

# Security Configuration
BCRYPT_SALT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (for future use)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# API Configuration
API_BASE_URL=http://localhost:3000/api
FRONTEND_URL=http://localhost:3000

# Logging Configuration
LOG_LEVEL=info
ELASTICSEARCH_URL=http://localhost:9200

# Cookie Configuration
COOKIE_DOMAIN=localhost
COOKIE_SECURE=false

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=SecureAdminPassword123!

# Payment Configuration (for future integration)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Google Analytics (optional)
GA_MEASUREMENT_ID=G-XXXXXXXXXX
