# Magic Menu Backend System

## Overview

The Magic Menu backend is a comprehensive Node.js/Express.js API system that provides complete functionality for a restaurant ordering platform. It includes user authentication, menu management, cart operations, order processing, and administrative features.

## Features

### 🔐 Authentication & Authorization
- JWT-based authentication with refresh tokens
- Role-based access control (customer/admin)
- Password hashing with bcrypt
- Email verification and password reset
- Session management with Redis

### 🍽️ Menu Management
- Category and item CRUD operations
- Search and filtering capabilities
- Allergen and nutritional information
- Featured items and popularity tracking
- Admin-only management endpoints

### 🛒 Shopping Cart
- User and session-based carts
- Real-time cart updates
- Guest cart merging on login
- Cart validation and totals calculation
- Persistent cart storage

### 📦 Order Processing
- Complete order lifecycle management
- Order status tracking
- Payment integration ready
- Order history and analytics
- Admin order management

### 👨‍💼 Admin Panel
- Comprehensive dashboard with analytics
- User management and statistics
- Sales reporting and insights
- System monitoring and health checks
- Data export capabilities

### 📞 Contact Management
- Contact form submissions
- Admin response tracking
- Inquiry categorization
- Bulk operations support

## Technology Stack

- **Runtime**: Node.js 16+
- **Framework**: Express.js
- **Database**: PostgreSQL with connection pooling
- **Authentication**: JWT with bcrypt
- **Session Store**: Redis (optional)
- **Validation**: Joi
- **Security**: Helmet, CORS, Rate limiting
- **Testing**: Jest, Supertest
- **Documentation**: Comprehensive API docs

## Project Structure

```
server/
├── index.js                 # Main server file
├── package.json             # Dependencies and scripts
├── .env                     # Environment configuration
├── .env.example             # Environment template
├── README.md                # This file
├── database/                # Database management
│   ├── schema.sql           # Database schema
│   ├── migrate.js           # Migration system
│   ├── seed.js              # Seed data
│   ├── reset.js             # Database utilities
│   ├── test.js              # Database tests
│   └── migrations/          # Migration files
├── models/                  # Data models
│   ├── BaseModel.js         # Base model class
│   ├── User.js              # User model
│   ├── MenuItem.js          # Menu item model
│   ├── MenuCategory.js      # Menu category model
│   ├── Cart.js              # Shopping cart model
│   ├── Order.js             # Order model
│   └── Contact.js           # Contact model
├── routes/                  # API routes
│   ├── auth.js              # Authentication routes
│   ├── menu.js              # Menu routes
│   ├── cart.js              # Cart routes
│   ├── orders.js            # Order routes
│   ├── admin.js             # Admin routes
│   └── contact.js           # Contact routes
├── middleware/              # Custom middleware
│   ├── auth.js              # Authentication middleware
│   └── security.js          # Security middleware
├── utils/                   # Utility modules
│   ├── DatabaseSecurity.js  # Database utilities
│   ├── JwtUtils.js          # JWT utilities
│   ├── SessionManager.js    # Session management
│   └── PasswordManager.js   # Password utilities
├── docs/                    # Documentation
│   └── API.md               # API documentation
└── tests/                   # Test files
    └── api-test.js          # API test suite
```

## Quick Start

### 1. Prerequisites

- Node.js 16+ and npm
- PostgreSQL 12+
- Redis (optional, for sessions)

### 2. Installation

```bash
# Clone the repository
git clone <repository-url>
cd food-pro

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env
# Edit .env with your configuration
```

### 3. Database Setup

```bash
# Create PostgreSQL database
createdb magic_menu

# Run migrations
npm run db:migrate

# Seed initial data
npm run db:seed
```

### 4. Start the Server

```bash
# Development mode
npm run dev

# Production mode
npm start
```

The server will start on `http://localhost:3000`

## Environment Configuration

Key environment variables in `.env`:

```env
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/magic_menu
DB_HOST=localhost
DB_PORT=5432
DB_NAME=magic_menu
DB_USER=magic_menu_user
DB_PASSWORD=your_password

# Server
PORT=3000
NODE_ENV=development

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your_refresh_secret
JWT_REFRESH_EXPIRES_IN=7d

# Security
BCRYPT_SALT_ROUNDS=12
SESSION_SECRET=your_session_secret

# Admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=SecureAdminPassword123!
```

## Available Scripts

```bash
# Server management
npm start                    # Start production server
npm run dev                  # Start development server with nodemon

# Database management
npm run db:migrate           # Run database migrations
npm run db:seed              # Seed initial data
npm run db:reset             # Reset database (drop, migrate, seed)

# Testing
npm test                     # Run all tests
npm run test:watch           # Run tests in watch mode
npm run test:coverage        # Run tests with coverage
node server/tests/api-test.js # Run API tests

# Database utilities
node server/database/migrate.js     # Migration management
node server/database/seed.js        # Seed data management
node server/database/reset.js       # Database utilities
node server/database/test.js        # Database tests
```

## API Documentation

Comprehensive API documentation is available at `server/docs/API.md`.

### Key Endpoints

- **Authentication**: `/api/auth/*`
- **Menu**: `/api/menu/*`
- **Cart**: `/api/cart/*`
- **Orders**: `/api/orders/*`
- **Admin**: `/api/admin/*`
- **Contact**: `/api/contact/*`
- **Health Check**: `/health`

### Authentication

Most endpoints require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Default Credentials

### Admin User
- **Email**: <EMAIL>
- **Password**: SecureAdminPassword123!

### Sample Customer
- **Email**: <EMAIL>
- **Password**: CustomerPassword123!

## Security Features

- **JWT Authentication** with refresh tokens
- **Password Hashing** with bcrypt (12 salt rounds)
- **Rate Limiting** on sensitive endpoints
- **Input Validation** with Joi schemas
- **SQL Injection Prevention** with parameterized queries
- **CORS Protection** with configurable origins
- **Security Headers** with Helmet
- **Session Security** with secure cookies

## Testing

### Database Tests
```bash
node server/database/test.js
```

### API Tests
```bash
node server/tests/api-test.js
```

### Unit Tests
```bash
npm test
```

## Monitoring & Health

### Health Check
```bash
curl http://localhost:3000/health
```

### System Status (Admin)
```bash
curl -H "Authorization: Bearer <admin-token>" \
     http://localhost:3000/api/admin/system/status
```

## Production Deployment

### Environment Setup
1. Set `NODE_ENV=production`
2. Configure production database
3. Set secure JWT secrets
4. Configure Redis for sessions
5. Set up SSL/TLS
6. Configure reverse proxy (nginx)

### Database Migration
```bash
npm run db:migrate
npm run db:seed
```

### Process Management
Use PM2 or similar for process management:

```bash
pm2 start server/index.js --name "magic-menu-api"
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check PostgreSQL is running
   - Verify credentials in .env
   - Ensure database exists

2. **JWT Token Errors**
   - Check JWT_SECRET is set
   - Verify token format
   - Check token expiration

3. **Permission Denied**
   - Verify user role
   - Check authentication token
   - Ensure proper middleware order

### Debug Mode
Set `LOG_LEVEL=debug` in .env for detailed logging.

## Contributing

1. Follow the existing code structure
2. Add tests for new features
3. Update documentation
4. Follow security best practices
5. Test thoroughly before submitting

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the API documentation
3. Run the test suite to identify problems
4. Check server logs for detailed error messages

## License

This project is licensed under the MIT License.
