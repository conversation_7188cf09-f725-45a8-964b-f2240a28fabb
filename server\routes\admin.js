const express = require('express');
const Joi = require('joi');
const User = require('../models/User');
const MenuItem = require('../models/MenuItem');
const MenuCategory = require('../models/MenuCategory');
const Order = require('../models/Order');
const AuthMiddleware = require('../middleware/auth');

const router = express.Router();

// All admin routes require authentication and admin role
router.use(AuthMiddleware.authenticate);
router.use(AuthMiddleware.requireAdmin);

// Dashboard overview
router.get('/dashboard', async (req, res) => {
    try {
        const [userStats, menuStats, orderStats] = await Promise.all([
            User.getUserStats(),
            MenuItem.getMenuStats(),
            Order.getOrderStats()
        ]);

        // Get recent orders
        const recentOrders = await Order.paginate(1, 5, {}, 'created_at DESC');
        
        // Get popular items
        const popularItems = await MenuItem.getPopularItems(5);

        res.json({
            dashboard: {
                users: {
                    total: parseInt(userStats.total_users),
                    customers: parseInt(userStats.customers),
                    admins: parseInt(userStats.admins),
                    active: parseInt(userStats.active_users),
                    verified: parseInt(userStats.verified_users),
                    newLast30Days: parseInt(userStats.new_users_30d)
                },
                menu: {
                    totalItems: parseInt(menuStats.total_items),
                    availableItems: parseInt(menuStats.available_items),
                    featuredItems: parseInt(menuStats.featured_items),
                    averagePrice: parseFloat(menuStats.average_price || 0),
                    minPrice: parseFloat(menuStats.min_price || 0),
                    maxPrice: parseFloat(menuStats.max_price || 0),
                    totalCategories: parseInt(menuStats.total_categories)
                },
                orders: {
                    total: parseInt(orderStats.total_orders),
                    pending: parseInt(orderStats.pending_orders),
                    confirmed: parseInt(orderStats.confirmed_orders),
                    preparing: parseInt(orderStats.preparing_orders),
                    ready: parseInt(orderStats.ready_orders),
                    delivered: parseInt(orderStats.delivered_orders),
                    cancelled: parseInt(orderStats.cancelled_orders),
                    totalRevenue: parseFloat(orderStats.total_revenue || 0),
                    averageOrderValue: parseFloat(orderStats.average_order_value || 0),
                    ordersLast30Days: parseInt(orderStats.orders_last_30_days)
                },
                recentOrders: recentOrders.data.map(order => ({
                    id: order.id,
                    orderNumber: order.order_number,
                    customerName: order.customer_name,
                    status: order.status,
                    totalAmount: order.total_amount,
                    createdAt: order.created_at
                })),
                popularItems: popularItems.map(item => ({
                    id: item.id,
                    name: item.name,
                    categoryName: item.category_name,
                    orderCount: parseInt(item.order_count || 0),
                    totalQuantity: parseInt(item.total_quantity || 0)
                }))
            }
        });

    } catch (error) {
        console.error('Dashboard error:', error);
        res.status(500).json({
            error: 'Failed to fetch dashboard data',
            message: 'Internal server error'
        });
    }
});

// USER MANAGEMENT

// Get all users
router.get('/users', async (req, res) => {
    try {
        const { page = 1, limit = 20, search, role } = req.query;
        
        let result;
        if (search) {
            result = await User.searchUsers(search, role, parseInt(page), parseInt(limit));
            result = {
                data: result.users,
                pagination: result.pagination
            };
        } else {
            const conditions = {};
            if (role) conditions.role = role;
            
            result = await User.paginate(parseInt(page), parseInt(limit), conditions, 'created_at DESC');
        }

        const users = result.data.map(user => ({
            id: user.id,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            phone: user.phone,
            role: user.role,
            isActive: user.is_active,
            emailVerified: user.email_verified,
            lastLogin: user.last_login,
            createdAt: user.created_at
        }));

        res.json({
            users,
            pagination: result.pagination
        });

    } catch (error) {
        console.error('Get users error:', error);
        res.status(500).json({
            error: 'Failed to fetch users',
            message: 'Internal server error'
        });
    }
});

// Get user details
router.get('/users/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
            return res.status(400).json({
                error: 'Invalid ID format',
                message: 'User ID must be a valid UUID'
            });
        }

        const user = await User.getUserWithAddresses(id);
        if (!user) {
            return res.status(404).json({
                error: 'User not found',
                message: 'The requested user does not exist'
            });
        }

        // Get user's order history
        const orderHistory = await Order.getOrdersByUser(id, 1, 10);

        res.json({
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                phone: user.phone,
                role: user.role,
                isActive: user.is_active,
                emailVerified: user.email_verified,
                lastLogin: user.last_login,
                createdAt: user.created_at,
                addresses: user.addresses || [],
                orderHistory: {
                    orders: orderHistory.orders.map(order => ({
                        id: order.id,
                        orderNumber: order.order_number,
                        status: order.status,
                        totalAmount: order.total_amount,
                        createdAt: order.created_at
                    })),
                    pagination: orderHistory.pagination
                }
            }
        });

    } catch (error) {
        console.error('Get user details error:', error);
        res.status(500).json({
            error: 'Failed to fetch user details',
            message: 'Internal server error'
        });
    }
});

// Update user status
router.patch('/users/:id/status', async (req, res) => {
    try {
        const { id } = req.params;
        const { isActive } = req.body;
        
        if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
            return res.status(400).json({
                error: 'Invalid ID format',
                message: 'User ID must be a valid UUID'
            });
        }

        if (typeof isActive !== 'boolean') {
            return res.status(400).json({
                error: 'Validation failed',
                message: 'isActive must be a boolean value'
            });
        }

        // Prevent admin from deactivating themselves
        if (id === req.user.id && !isActive) {
            return res.status(400).json({
                error: 'Invalid operation',
                message: 'You cannot deactivate your own account'
            });
        }

        const updatedUser = await User.update(id, { is_active: isActive });
        if (!updatedUser) {
            return res.status(404).json({
                error: 'User not found',
                message: 'The requested user does not exist'
            });
        }

        res.json({
            message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
            user: {
                id: updatedUser.id,
                email: updatedUser.email,
                isActive: updatedUser.is_active
            }
        });

    } catch (error) {
        console.error('Update user status error:', error);
        res.status(500).json({
            error: 'Failed to update user status',
            message: 'Internal server error'
        });
    }
});

// ANALYTICS AND REPORTS

// Get sales analytics
router.get('/analytics/sales', async (req, res) => {
    try {
        const { period = '30d', startDate, endDate } = req.query;
        
        let start, end;
        const now = new Date();
        
        if (startDate && endDate) {
            start = new Date(startDate);
            end = new Date(endDate);
        } else {
            switch (period) {
                case '7d':
                    start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case '30d':
                    start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    break;
                case '90d':
                    start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                    break;
                default:
                    start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            }
            end = now;
        }

        const orderStats = await Order.getOrderStats(start, end);
        const popularItems = await MenuItem.getPopularItems(10);

        // Get daily sales data (simplified - in production you'd want more sophisticated analytics)
        const dailySalesQuery = `
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as orders,
                SUM(total_amount) FILTER (WHERE payment_status = 'completed') as revenue
            FROM orders 
            WHERE created_at BETWEEN $1 AND $2
            GROUP BY DATE(created_at)
            ORDER BY date DESC
            LIMIT 30
        `;
        
        const dailySales = await Order.query(dailySalesQuery, [start, end]);

        res.json({
            analytics: {
                period: { start, end },
                summary: {
                    totalOrders: parseInt(orderStats.total_orders),
                    totalRevenue: parseFloat(orderStats.total_revenue || 0),
                    averageOrderValue: parseFloat(orderStats.average_order_value || 0),
                    completionRate: orderStats.total_orders > 0 
                        ? ((parseInt(orderStats.delivered_orders) / parseInt(orderStats.total_orders)) * 100).toFixed(2)
                        : 0
                },
                dailySales: dailySales.map(day => ({
                    date: day.date,
                    orders: parseInt(day.orders),
                    revenue: parseFloat(day.revenue || 0)
                })),
                popularItems: popularItems.slice(0, 10).map(item => ({
                    name: item.name,
                    category: item.category_name,
                    orderCount: parseInt(item.order_count || 0),
                    revenue: parseFloat(item.price) * parseInt(item.total_quantity || 0)
                }))
            }
        });

    } catch (error) {
        console.error('Sales analytics error:', error);
        res.status(500).json({
            error: 'Failed to fetch sales analytics',
            message: 'Internal server error'
        });
    }
});

// SYSTEM MANAGEMENT

// Get system status
router.get('/system/status', async (req, res) => {
    try {
        const db = require('../utils/DatabaseSecurity');
        
        // Test database connection
        const dbConnected = await db.testConnection();
        
        // Get system stats
        const systemStats = {
            database: {
                connected: dbConnected,
                connectionPool: {
                    total: db.pool.totalCount,
                    idle: db.pool.idleCount,
                    waiting: db.pool.waitingCount
                }
            },
            server: {
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                nodeVersion: process.version,
                environment: process.env.NODE_ENV || 'development'
            },
            timestamp: new Date().toISOString()
        };

        res.json({
            status: 'healthy',
            system: systemStats
        });

    } catch (error) {
        console.error('System status error:', error);
        res.status(500).json({
            error: 'Failed to fetch system status',
            message: 'Internal server error'
        });
    }
});

// Export data (simplified version)
router.get('/export/:type', async (req, res) => {
    try {
        const { type } = req.params;
        const { format = 'json', startDate, endDate } = req.query;
        
        let data;
        let filename;
        
        switch (type) {
            case 'orders':
                const conditions = {};
                if (startDate && endDate) {
                    // This would need to be implemented in the Order model
                    data = await Order.findAll(conditions, 'created_at DESC', 1000);
                } else {
                    data = await Order.findAll({}, 'created_at DESC', 1000);
                }
                filename = `orders-export-${new Date().toISOString().split('T')[0]}`;
                break;
                
            case 'users':
                data = await User.findAll({}, 'created_at DESC', 1000);
                filename = `users-export-${new Date().toISOString().split('T')[0]}`;
                break;
                
            case 'menu':
                data = await MenuItem.getAllWithCategories();
                filename = `menu-export-${new Date().toISOString().split('T')[0]}`;
                break;
                
            default:
                return res.status(400).json({
                    error: 'Invalid export type',
                    message: 'Supported types: orders, users, menu'
                });
        }

        if (format === 'csv') {
            // In a real implementation, you'd convert to CSV format
            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
            res.send('CSV export not implemented yet');
        } else {
            res.setHeader('Content-Type', 'application/json');
            res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`);
            res.json({
                exportType: type,
                exportDate: new Date().toISOString(),
                recordCount: data.length,
                data
            });
        }

    } catch (error) {
        console.error('Export data error:', error);
        res.status(500).json({
            error: 'Failed to export data',
            message: 'Internal server error'
        });
    }
});

module.exports = router;
