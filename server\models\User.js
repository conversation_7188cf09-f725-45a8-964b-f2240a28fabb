const BaseModel = require('./BaseModel');
const bcrypt = require('bcrypt');
const crypto = require('crypto');

class User extends BaseModel {
    constructor() {
        super('users');
    }

    // Find user by email
    async findByEmail(email) {
        try {
            return await this.findOne({ email: email.toLowerCase() });
        } catch (error) {
            console.error('Error finding user by email:', error);
            throw error;
        }
    }

    // Create new user with hashed password
    async createUser(userData) {
        try {
            const { password, ...otherData } = userData;
            
            // Hash password
            const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
            const password_hash = await bcrypt.hash(password, saltRounds);
            
            // Prepare user data
            const userToCreate = {
                ...otherData,
                email: otherData.email.toLowerCase(),
                password_hash,
                email_verification_token: crypto.randomBytes(32).toString('hex')
            };
            
            return await this.create(userToCreate);
        } catch (error) {
            console.error('Error creating user:', error);
            throw error;
        }
    }

    // Verify password
    async verifyPassword(user, password) {
        try {
            return await bcrypt.compare(password, user.password_hash);
        } catch (error) {
            console.error('Error verifying password:', error);
            throw error;
        }
    }

    // Update password
    async updatePassword(userId, newPassword) {
        try {
            const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
            const password_hash = await bcrypt.hash(newPassword, saltRounds);
            
            return await this.update(userId, { 
                password_hash,
                password_reset_token: null,
                password_reset_expires: null
            });
        } catch (error) {
            console.error('Error updating password:', error);
            throw error;
        }
    }

    // Generate password reset token
    async generatePasswordResetToken(email) {
        try {
            const user = await this.findByEmail(email);
            if (!user) {
                return null;
            }

            const resetToken = crypto.randomBytes(32).toString('hex');
            const resetExpires = new Date(Date.now() + 3600000); // 1 hour

            await this.update(user.id, {
                password_reset_token: resetToken,
                password_reset_expires: resetExpires
            });

            return { user, resetToken };
        } catch (error) {
            console.error('Error generating password reset token:', error);
            throw error;
        }
    }

    // Verify email
    async verifyEmail(token) {
        try {
            const user = await this.findOne({ email_verification_token: token });
            if (!user) {
                return null;
            }

            return await this.update(user.id, {
                email_verified: true,
                email_verification_token: null
            });
        } catch (error) {
            console.error('Error verifying email:', error);
            throw error;
        }
    }

    // Update last login
    async updateLastLogin(userId) {
        try {
            return await this.update(userId, { last_login: new Date() });
        } catch (error) {
            console.error('Error updating last login:', error);
            throw error;
        }
    }

    // Get user profile (without sensitive data)
    async getProfile(userId) {
        try {
            const query = `
                SELECT 
                    id, email, first_name, last_name, phone, role, 
                    is_active, email_verified, last_login, created_at
                FROM users 
                WHERE id = $1
            `;
            const result = await this.db.query(query, [userId]);
            return result[0] || null;
        } catch (error) {
            console.error('Error getting user profile:', error);
            throw error;
        }
    }

    // Get user with addresses
    async getUserWithAddresses(userId) {
        try {
            const query = `
                SELECT 
                    u.id, u.email, u.first_name, u.last_name, u.phone, u.role,
                    u.is_active, u.email_verified, u.last_login, u.created_at,
                    json_agg(
                        json_build_object(
                            'id', ua.id,
                            'address_line_1', ua.address_line_1,
                            'address_line_2', ua.address_line_2,
                            'city', ua.city,
                            'state', ua.state,
                            'postal_code', ua.postal_code,
                            'country', ua.country,
                            'is_default', ua.is_default
                        )
                    ) FILTER (WHERE ua.id IS NOT NULL) as addresses
                FROM users u
                LEFT JOIN user_addresses ua ON u.id = ua.user_id
                WHERE u.id = $1
                GROUP BY u.id
            `;
            const result = await this.db.query(query, [userId]);
            return result[0] || null;
        } catch (error) {
            console.error('Error getting user with addresses:', error);
            throw error;
        }
    }

    // Search users (admin function)
    async searchUsers(searchTerm, role = null, page = 1, limit = 10) {
        try {
            let query = `
                SELECT 
                    id, email, first_name, last_name, phone, role, 
                    is_active, email_verified, last_login, created_at
                FROM users 
                WHERE (
                    first_name ILIKE $1 OR 
                    last_name ILIKE $1 OR 
                    email ILIKE $1
                )
            `;
            const values = [`%${searchTerm}%`];
            
            if (role) {
                query += ` AND role = $${values.length + 1}`;
                values.push(role);
            }
            
            query += ` ORDER BY created_at DESC`;
            
            const offset = (page - 1) * limit;
            query += ` LIMIT $${values.length + 1} OFFSET $${values.length + 2}`;
            values.push(limit, offset);
            
            const [users, totalCount] = await Promise.all([
                this.db.query(query, values),
                this.count({}) // This could be optimized to count with search conditions
            ]);
            
            return {
                users,
                pagination: {
                    page,
                    limit,
                    total: totalCount,
                    pages: Math.ceil(totalCount / limit)
                }
            };
        } catch (error) {
            console.error('Error searching users:', error);
            throw error;
        }
    }

    // Get user statistics (admin function)
    async getUserStats() {
        try {
            const query = `
                SELECT 
                    COUNT(*) as total_users,
                    COUNT(*) FILTER (WHERE role = 'customer') as customers,
                    COUNT(*) FILTER (WHERE role = 'admin') as admins,
                    COUNT(*) FILTER (WHERE is_active = true) as active_users,
                    COUNT(*) FILTER (WHERE email_verified = true) as verified_users,
                    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as new_users_30d
                FROM users
            `;
            const result = await this.db.query(query);
            return result[0];
        } catch (error) {
            console.error('Error getting user stats:', error);
            throw error;
        }
    }
}

module.exports = new User();
