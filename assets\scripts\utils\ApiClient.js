import { ApiError<PERSON><PERSON><PERSON>, ApiError } from './ApiErrorHandler.js';

export class ApiClient {
    constructor(baseURL = 'http://localhost:3000') {
        this.baseURL = baseURL;
        this.defaultTimeout = 30000; // 30 seconds
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;

        // Load API configuration
        this.loadConfig();
    }

    async loadConfig() {
        try {
            const response = await fetch('/config/api-config.json');
            this.config = await response.json();
            this.baseURL = this.config.baseUrl;
        } catch (error) {
            console.warn('Could not load API configuration, using defaults');
            this.config = { baseUrl: 'http://localhost:3000' };
        }
    }

    async request(endpoint, options = {}) {
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), options.timeout || this.defaultTimeout);

        try {
            // Security headers
            const securityHeaders = {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            };

            // Add authorization header if token exists
            const token = this.getAuthToken();
            if (token) {
                securityHeaders['Authorization'] = `Bearer ${token}`;
            }

            // Add CSRF token if available
            if (this.csrfToken) {
                securityHeaders['X-CSRF-Token'] = this.csrfToken;
            }

            const response = await fetch(`${this.baseURL}${endpoint}`, {
                ...options,
                headers: {
                    ...securityHeaders,
                    ...options.headers,
                },
                credentials: 'include', // Include cookies for cross-origin requests
                signal: controller.signal
            });

            return await ApiErrorHandler.handleResponse(response);
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new ApiError('Request timeout', 'TIMEOUT', 408);
            }
            throw error;
        } finally {
            clearTimeout(timeout);
        }
    }

    async get(endpoint, options = {}) {
        return this.request(endpoint, { ...options, method: 'GET' });
    }

    async post(endpoint, data, options = {}) {
        return this.request(endpoint, {
            ...options,
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    async put(endpoint, data, options = {}) {
        return this.request(endpoint, {
            ...options,
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    async delete(endpoint, options = {}) {
        return this.request(endpoint, { ...options, method: 'DELETE' });
    }

    getAuthToken() {
        // Get JWT token from localStorage
        return localStorage.getItem('authToken');
    }

    setAuthToken(token) {
        if (token) {
            localStorage.setItem('authToken', token);
        } else {
            localStorage.removeItem('authToken');
        }
    }

    getRefreshToken() {
        return localStorage.getItem('refreshToken');
    }

    setRefreshToken(token) {
        if (token) {
            localStorage.setItem('refreshToken', token);
        } else {
            localStorage.removeItem('refreshToken');
        }
    }

    clearTokens() {
        localStorage.removeItem('authToken');
        localStorage.removeItem('refreshToken');
    }

    isAuthenticated() {
        return !!this.getAuthToken();
    }

    // Auto-refresh token if expired
    async refreshTokenIfNeeded() {
        const token = this.getAuthToken();
        const refreshToken = this.getRefreshToken();

        if (!token || !refreshToken) {
            return false;
        }

        try {
            // Check if token is expired (simple check - in production you'd decode JWT)
            const tokenData = JSON.parse(atob(token.split('.')[1]));
            const now = Date.now() / 1000;

            if (tokenData.exp && tokenData.exp < now) {
                // Token is expired, try to refresh
                const response = await this.post('/api/auth/refresh', {
                    refreshToken: refreshToken
                });

                if (response.tokens) {
                    this.setAuthToken(response.tokens.accessToken);
                    if (response.tokens.refreshToken) {
                        this.setRefreshToken(response.tokens.refreshToken);
                    }
                    return true;
                }
            }
            return true;
        } catch (error) {
            console.error('Token refresh failed:', error);
            this.clearTokens();
            return false;
        }
    }

    // Method to sanitize data before sending to API
    sanitizeData(data) {
        if (typeof data !== 'object' || data === null) return data;
        
        const sanitized = {};
        for (const [key, value] of Object.entries(data)) {
            if (typeof value === 'string') {
                sanitized[key] = this.sanitizeString(value);
            } else if (typeof value === 'object' && value !== null) {
                sanitized[key] = this.sanitizeData(value);
            } else {
                sanitized[key] = value;
            }
        }
        return sanitized;
    }

    sanitizeString(str) {
        const div = document.createElement('div');
        div.textContent = str;
        return div.innerHTML;
    }
}

export const api = new ApiClient();
