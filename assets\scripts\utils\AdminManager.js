import { api } from '../api.js';
import { Alert } from './Alert.js';
import { formatPrice } from '../config/currency.js';
import { authManager } from './AuthManager.js';

export class AdminManager {
    constructor() {
        this.dashboardData = null;
        this.currentSection = 'dashboard';
        this.isLoading = false;
        this.callbacks = [];
        
        this.init();
    }

    async init() {
        // Check if user is admin
        if (!authManager.isAdmin()) {
            console.log('User is not admin, redirecting...');
            window.location.href = '/account.html';
            return;
        }

        this.setupEventListeners();
        await this.loadDashboardData();
    }

    setupEventListeners() {
        // Navigation links
        document.addEventListener('click', (e) => {
            if (e.target.matches('.admin-nav-link')) {
                e.preventDefault();
                const section = e.target.dataset.section;
                this.navigateToSection(section);
            }
        });

        // Action buttons
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.user-status-toggle')) {
                e.preventDefault();
                await this.toggleUserStatus(e.target);
            }
            
            if (e.target.matches('.order-status-update')) {
                e.preventDefault();
                await this.updateOrderStatus(e.target);
            }
        });

        // Search forms
        document.addEventListener('submit', async (e) => {
            if (e.target.matches('.admin-search-form')) {
                e.preventDefault();
                await this.handleSearch(e.target);
            }
        });

        // Refresh buttons
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.refresh-data-btn')) {
                e.preventDefault();
                await this.refreshCurrentSection();
            }
        });
    }

    async loadDashboardData() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading(true);

        try {
            const response = await api.getAdminDashboard();
            this.dashboardData = response.dashboard;
            this.renderDashboard();
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            Alert.show('Failed to load dashboard data', 'error');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    async navigateToSection(section) {
        this.currentSection = section;
        
        // Update active navigation
        document.querySelectorAll('.admin-nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`).classList.add('active');

        // Show appropriate content
        document.querySelectorAll('.admin-section').forEach(sec => {
            sec.style.display = 'none';
        });
        
        const sectionElement = document.querySelector(`#admin-${section}`);
        if (sectionElement) {
            sectionElement.style.display = 'block';
        }

        // Load section-specific data
        await this.loadSectionData(section);
    }

    async loadSectionData(section) {
        this.showLoading(true);

        try {
            switch (section) {
                case 'dashboard':
                    await this.loadDashboardData();
                    break;
                case 'users':
                    await this.loadUsers();
                    break;
                case 'orders':
                    await this.loadOrders();
                    break;
                case 'analytics':
                    await this.loadAnalytics();
                    break;
                default:
                    console.log(`No specific loader for section: ${section}`);
            }
        } catch (error) {
            console.error(`Failed to load ${section} data:`, error);
            Alert.show(`Failed to load ${section} data`, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async loadUsers(page = 1, search = '', role = '') {
        try {
            const params = { page, limit: 20 };
            if (search) params.search = search;
            if (role) params.role = role;

            const response = await api.getAdminUsers(params);
            this.renderUsers(response.users, response.pagination);
        } catch (error) {
            console.error('Failed to load users:', error);
            throw error;
        }
    }

    async loadOrders(page = 1, status = '', search = '') {
        try {
            const params = { page, limit: 20 };
            if (status) params.status = status;
            if (search) params.search = search;

            const response = await api.getAdminOrders(params);
            this.renderOrders(response.orders, response.pagination);
        } catch (error) {
            console.error('Failed to load orders:', error);
            throw error;
        }
    }

    async loadAnalytics(period = '30d') {
        try {
            const response = await api.getSalesAnalytics({ period });
            this.renderAnalytics(response.analytics);
        } catch (error) {
            console.error('Failed to load analytics:', error);
            throw error;
        }
    }

    renderDashboard() {
        if (!this.dashboardData) return;

        const data = this.dashboardData;

        // Update stats cards
        this.updateStatCard('total-users', data.users.total, 'Users');
        this.updateStatCard('total-orders', data.orders.total, 'Orders');
        this.updateStatCard('total-revenue', formatPrice(data.orders.totalRevenue), 'Revenue');
        this.updateStatCard('active-users', data.users.active, 'Active Users');

        // Update recent orders
        this.renderRecentOrders(data.recentOrders);

        // Update popular items
        this.renderPopularItems(data.popularItems);
    }

    updateStatCard(id, value, label) {
        const card = document.querySelector(`[data-stat="${id}"]`);
        if (card) {
            const valueElement = card.querySelector('.stat-value');
            const labelElement = card.querySelector('.stat-label');
            
            if (valueElement) valueElement.textContent = value;
            if (labelElement) labelElement.textContent = label;
        }
    }

    renderRecentOrders(orders) {
        const container = document.querySelector('.recent-orders-list');
        if (!container || !orders) return;

        container.innerHTML = orders.map(order => `
            <div class="order-item">
                <div class="order-info">
                    <span class="order-number">#${order.orderNumber}</span>
                    <span class="order-customer">${order.customerName}</span>
                </div>
                <div class="order-details">
                    <span class="order-amount">${formatPrice(order.totalAmount)}</span>
                    <span class="order-status status-${order.status}">${this.formatStatus(order.status)}</span>
                </div>
                <div class="order-actions">
                    <button class="btn btn-sm order-status-update" 
                            data-order-id="${order.id}" 
                            data-current-status="${order.status}">
                        Update Status
                    </button>
                </div>
            </div>
        `).join('');
    }

    renderPopularItems(items) {
        const container = document.querySelector('.popular-items-list');
        if (!container || !items) return;

        container.innerHTML = items.map(item => `
            <div class="popular-item">
                <div class="item-info">
                    <span class="item-name">${item.name}</span>
                    <span class="item-category">${item.category}</span>
                </div>
                <div class="item-stats">
                    <span class="item-orders">${item.orderCount} orders</span>
                    <span class="item-revenue">${formatPrice(item.revenue)}</span>
                </div>
            </div>
        `).join('');
    }

    renderUsers(users, pagination) {
        const container = document.querySelector('.users-table-body');
        if (!container) return;

        container.innerHTML = users.map(user => `
            <tr>
                <td>${user.firstName} ${user.lastName}</td>
                <td>${user.email}</td>
                <td>${user.phone || 'N/A'}</td>
                <td><span class="role-badge role-${user.role}">${user.role}</span></td>
                <td>
                    <span class="status-badge ${user.isActive ? 'active' : 'inactive'}">
                        ${user.isActive ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                <td>
                    <button class="btn btn-sm user-status-toggle" 
                            data-user-id="${user.id}" 
                            data-current-status="${user.isActive}">
                        ${user.isActive ? 'Deactivate' : 'Activate'}
                    </button>
                </td>
            </tr>
        `).join('');

        this.renderPagination(pagination, 'users');
    }

    renderOrders(orders, pagination) {
        const container = document.querySelector('.orders-table-body');
        if (!container) return;

        container.innerHTML = orders.map(order => `
            <tr>
                <td>#${order.orderNumber}</td>
                <td>${order.customerName}</td>
                <td>${formatPrice(order.totalAmount)}</td>
                <td>
                    <span class="status-badge status-${order.status}">
                        ${this.formatStatus(order.status)}
                    </span>
                </td>
                <td>${new Date(order.createdAt).toLocaleDateString()}</td>
                <td>
                    <select class="order-status-select" 
                            data-order-id="${order.id}" 
                            data-current-status="${order.status}">
                        <option value="pending" ${order.status === 'pending' ? 'selected' : ''}>Pending</option>
                        <option value="confirmed" ${order.status === 'confirmed' ? 'selected' : ''}>Confirmed</option>
                        <option value="preparing" ${order.status === 'preparing' ? 'selected' : ''}>Preparing</option>
                        <option value="ready" ${order.status === 'ready' ? 'selected' : ''}>Ready</option>
                        <option value="delivered" ${order.status === 'delivered' ? 'selected' : ''}>Delivered</option>
                        <option value="cancelled" ${order.status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
                    </select>
                </td>
            </tr>
        `).join('');

        // Add change listeners to status selects
        container.querySelectorAll('.order-status-select').forEach(select => {
            select.addEventListener('change', async (e) => {
                await this.updateOrderStatus(e.target);
            });
        });

        this.renderPagination(pagination, 'orders');
    }

    renderAnalytics(analytics) {
        const container = document.querySelector('.analytics-content');
        if (!container) return;

        container.innerHTML = `
            <div class="analytics-summary">
                <div class="summary-card">
                    <h3>Total Orders</h3>
                    <div class="summary-value">${analytics.summary.totalOrders}</div>
                </div>
                <div class="summary-card">
                    <h3>Total Revenue</h3>
                    <div class="summary-value">${formatPrice(analytics.summary.totalRevenue)}</div>
                </div>
                <div class="summary-card">
                    <h3>Average Order Value</h3>
                    <div class="summary-value">${formatPrice(analytics.summary.averageOrderValue)}</div>
                </div>
                <div class="summary-card">
                    <h3>Completion Rate</h3>
                    <div class="summary-value">${analytics.summary.completionRate}%</div>
                </div>
            </div>
            
            <div class="analytics-charts">
                <div class="chart-container">
                    <h3>Daily Sales</h3>
                    <canvas id="dailySalesChart"></canvas>
                </div>
                <div class="chart-container">
                    <h3>Popular Items</h3>
                    <div class="popular-items-chart">
                        ${analytics.popularItems.map(item => `
                            <div class="popular-item-bar">
                                <span class="item-name">${item.name}</span>
                                <div class="item-bar">
                                    <div class="bar-fill" style="width: ${(item.orderCount / Math.max(...analytics.popularItems.map(i => i.orderCount))) * 100}%"></div>
                                    <span class="bar-value">${item.orderCount}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        // Initialize charts if Chart.js is available
        if (window.Chart) {
            this.initializeDailySalesChart(analytics.dailySales);
        }
    }

    renderPagination(pagination, section) {
        const container = document.querySelector(`.${section}-pagination`);
        if (!container) return;

        const { page, pages, hasNext, hasPrev } = pagination;
        
        container.innerHTML = `
            <button class="btn btn-sm" ${!hasPrev ? 'disabled' : ''} 
                    onclick="adminManager.loadSectionData('${section}', ${page - 1})">
                Previous
            </button>
            <span class="pagination-info">Page ${page} of ${pages}</span>
            <button class="btn btn-sm" ${!hasNext ? 'disabled' : ''} 
                    onclick="adminManager.loadSectionData('${section}', ${page + 1})">
                Next
            </button>
        `;
    }

    async toggleUserStatus(button) {
        const userId = button.dataset.userId;
        const currentStatus = button.dataset.currentStatus === 'true';
        const newStatus = !currentStatus;

        try {
            await api.updateUserStatus(userId, newStatus);
            Alert.show(`User ${newStatus ? 'activated' : 'deactivated'} successfully`, 'success');
            await this.loadUsers(); // Refresh the users list
        } catch (error) {
            console.error('Failed to update user status:', error);
            Alert.show('Failed to update user status', 'error');
        }
    }

    async updateOrderStatus(select) {
        const orderId = select.dataset.orderId;
        const newStatus = select.value;
        const currentStatus = select.dataset.currentStatus;

        if (newStatus === currentStatus) return;

        try {
            await api.updateOrderStatus(orderId, newStatus);
            Alert.show('Order status updated successfully', 'success');
            select.dataset.currentStatus = newStatus;
        } catch (error) {
            console.error('Failed to update order status:', error);
            Alert.show('Failed to update order status', 'error');
            // Revert the select value
            select.value = currentStatus;
        }
    }

    async handleSearch(form) {
        const formData = new FormData(form);
        const searchTerm = formData.get('search');
        const section = form.dataset.section;

        switch (section) {
            case 'users':
                await this.loadUsers(1, searchTerm);
                break;
            case 'orders':
                await this.loadOrders(1, '', searchTerm);
                break;
        }
    }

    async refreshCurrentSection() {
        await this.loadSectionData(this.currentSection);
        Alert.show('Data refreshed', 'success');
    }

    formatStatus(status) {
        const statusMap = {
            'pending': 'Pending',
            'confirmed': 'Confirmed',
            'preparing': 'Preparing',
            'ready': 'Ready',
            'delivered': 'Delivered',
            'cancelled': 'Cancelled'
        };
        
        return statusMap[status] || status;
    }

    showLoading(show) {
        const loader = document.querySelector('.admin-loader');
        if (loader) {
            loader.style.display = show ? 'block' : 'none';
        }
    }

    initializeDailySalesChart(dailySales) {
        const ctx = document.getElementById('dailySalesChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: dailySales.map(day => new Date(day.date).toLocaleDateString()),
                datasets: [{
                    label: 'Revenue',
                    data: dailySales.map(day => day.revenue),
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatPrice(value);
                            }
                        }
                    }
                }
            }
        });
    }

    // Callback management
    onDataChange(callback) {
        this.callbacks.push(callback);
        
        return () => {
            const index = this.callbacks.indexOf(callback);
            if (index > -1) {
                this.callbacks.splice(index, 1);
            }
        };
    }

    notifyCallbacks(event, data = null) {
        this.callbacks.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Admin callback error:', error);
            }
        });
    }
}

// Create global admin manager instance
export const adminManager = new AdminManager();

// Make it available globally for debugging
window.adminManager = adminManager;
