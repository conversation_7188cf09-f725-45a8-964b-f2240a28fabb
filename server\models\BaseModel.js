const db = require('../utils/DatabaseSecurity');

class BaseModel {
    constructor(tableName) {
        this.tableName = tableName;
        this.db = db;
    }

    // Find by ID
    async findById(id) {
        try {
            const result = await this.db.safeSelect(this.tableName, { id });
            return result[0] || null;
        } catch (error) {
            console.error(`Error finding ${this.tableName} by ID:`, error);
            throw error;
        }
    }

    // Find all records with optional conditions
    async findAll(conditions = {}, orderBy = 'created_at DESC', limit = null) {
        try {
            let query = `SELECT * FROM ${this.tableName}`;
            const values = [];
            
            if (Object.keys(conditions).length > 0) {
                const whereClause = Object.keys(conditions)
                    .map((key, index) => `${key} = $${index + 1}`)
                    .join(' AND ');
                query += ` WHERE ${whereClause}`;
                values.push(...Object.values(conditions));
            }
            
            if (orderBy) {
                query += ` ORDER BY ${orderBy}`;
            }
            
            if (limit) {
                query += ` LIMIT ${limit}`;
            }
            
            return await this.db.query(query, values);
        } catch (error) {
            console.error(`Error finding all ${this.tableName}:`, error);
            throw error;
        }
    }

    // Find one record with conditions
    async findOne(conditions) {
        try {
            const result = await this.db.safeSelect(this.tableName, conditions);
            return result[0] || null;
        } catch (error) {
            console.error(`Error finding one ${this.tableName}:`, error);
            throw error;
        }
    }

    // Create new record
    async create(data) {
        try {
            const result = await this.db.safeInsert(this.tableName, data);
            return result[0];
        } catch (error) {
            console.error(`Error creating ${this.tableName}:`, error);
            throw error;
        }
    }

    // Update record
    async update(id, data) {
        try {
            const result = await this.db.safeUpdate(this.tableName, data, { id });
            return result[0] || null;
        } catch (error) {
            console.error(`Error updating ${this.tableName}:`, error);
            throw error;
        }
    }

    // Delete record
    async delete(id) {
        try {
            const result = await this.db.safeDelete(this.tableName, { id });
            return result[0] || null;
        } catch (error) {
            console.error(`Error deleting ${this.tableName}:`, error);
            throw error;
        }
    }

    // Count records
    async count(conditions = {}) {
        try {
            let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
            const values = [];
            
            if (Object.keys(conditions).length > 0) {
                const whereClause = Object.keys(conditions)
                    .map((key, index) => `${key} = $${index + 1}`)
                    .join(' AND ');
                query += ` WHERE ${whereClause}`;
                values.push(...Object.values(conditions));
            }
            
            const result = await this.db.query(query, values);
            return parseInt(result[0].count);
        } catch (error) {
            console.error(`Error counting ${this.tableName}:`, error);
            throw error;
        }
    }

    // Check if record exists
    async exists(conditions) {
        try {
            const count = await this.count(conditions);
            return count > 0;
        } catch (error) {
            console.error(`Error checking existence in ${this.tableName}:`, error);
            throw error;
        }
    }

    // Paginated results
    async paginate(page = 1, limit = 10, conditions = {}, orderBy = 'created_at DESC') {
        try {
            const offset = (page - 1) * limit;
            let query = `SELECT * FROM ${this.tableName}`;
            const values = [];
            
            if (Object.keys(conditions).length > 0) {
                const whereClause = Object.keys(conditions)
                    .map((key, index) => `${key} = $${index + 1}`)
                    .join(' AND ');
                query += ` WHERE ${whereClause}`;
                values.push(...Object.values(conditions));
            }
            
            if (orderBy) {
                query += ` ORDER BY ${orderBy}`;
            }
            
            query += ` LIMIT $${values.length + 1} OFFSET $${values.length + 2}`;
            values.push(limit, offset);
            
            const [data, totalCount] = await Promise.all([
                this.db.query(query, values),
                this.count(conditions)
            ]);
            
            return {
                data,
                pagination: {
                    page,
                    limit,
                    total: totalCount,
                    pages: Math.ceil(totalCount / limit),
                    hasNext: page < Math.ceil(totalCount / limit),
                    hasPrev: page > 1
                }
            };
        } catch (error) {
            console.error(`Error paginating ${this.tableName}:`, error);
            throw error;
        }
    }

    // Execute raw query
    async query(sql, params = []) {
        try {
            return await this.db.query(sql, params);
        } catch (error) {
            console.error(`Error executing query on ${this.tableName}:`, error);
            throw error;
        }
    }
}

module.exports = BaseModel;
