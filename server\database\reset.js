const DatabaseMigrator = require('./migrate');
const DatabaseSeeder = require('./seed');
const db = require('../utils/DatabaseSecurity');
require('dotenv').config();

class DatabaseReset {
    constructor() {
        this.migrator = new DatabaseMigrator();
        this.seeder = new DatabaseSeeder();
    }

    async resetDatabase() {
        try {
            console.log('🔄 Starting complete database reset...');
            
            // Test database connection
            const isConnected = await db.testConnection();
            if (!isConnected) {
                throw new Error('Database connection failed');
            }

            console.log('⚠️  WARNING: This will completely reset the database!');
            console.log('All existing data will be lost.');
            
            // Drop all tables and recreate schema
            console.log('🗑️  Dropping all tables...');
            await this.dropAllTables();
            
            // Run migrations
            console.log('📋 Running migrations...');
            await this.migrator.runMigrations();
            
            // Seed data
            console.log('🌱 Seeding initial data...');
            await this.seeder.seedAll();
            
            console.log('✅ Database reset completed successfully!');
            
        } catch (error) {
            console.error('❌ Database reset failed:', error.message);
            throw error;
        }
    }

    async dropAllTables() {
        try {
            const dropQuery = `
                -- Drop all tables in the correct order (respecting foreign key constraints)
                DROP TABLE IF EXISTS cart_items CASCADE;
                DROP TABLE IF EXISTS shopping_carts CASCADE;
                DROP TABLE IF EXISTS order_items CASCADE;
                DROP TABLE IF EXISTS orders CASCADE;
                DROP TABLE IF EXISTS menu_items CASCADE;
                DROP TABLE IF EXISTS menu_categories CASCADE;
                DROP TABLE IF EXISTS user_addresses CASCADE;
                DROP TABLE IF EXISTS user_sessions CASCADE;
                DROP TABLE IF EXISTS users CASCADE;
                DROP TABLE IF EXISTS contact_submissions CASCADE;
                DROP TABLE IF EXISTS migrations CASCADE;
                
                -- Drop custom types
                DROP TYPE IF EXISTS user_role CASCADE;
                DROP TYPE IF EXISTS order_status CASCADE;
                DROP TYPE IF EXISTS payment_status CASCADE;
                
                -- Drop functions
                DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
                
                -- Drop extension (optional, might be used by other databases)
                -- DROP EXTENSION IF EXISTS "uuid-ossp";
            `;
            
            await db.query(dropQuery);
            console.log('✓ All tables dropped');
            
        } catch (error) {
            console.error('✗ Error dropping tables:', error.message);
            throw error;
        }
    }

    async backupDatabase() {
        try {
            console.log('💾 Creating database backup...');
            
            // This is a simple backup - in production you'd use pg_dump
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupData = {
                timestamp,
                users: await db.query('SELECT * FROM users'),
                menu_categories: await db.query('SELECT * FROM menu_categories'),
                menu_items: await db.query('SELECT * FROM menu_items'),
                orders: await db.query('SELECT * FROM orders'),
                order_items: await db.query('SELECT * FROM order_items'),
                contact_submissions: await db.query('SELECT * FROM contact_submissions')
            };
            
            const fs = require('fs').promises;
            const path = require('path');
            const backupPath = path.join(__dirname, 'backups');
            
            // Create backups directory if it doesn't exist
            try {
                await fs.mkdir(backupPath, { recursive: true });
            } catch (error) {
                // Directory might already exist
            }
            
            const backupFile = path.join(backupPath, `backup-${timestamp}.json`);
            await fs.writeFile(backupFile, JSON.stringify(backupData, null, 2));
            
            console.log(`✓ Backup created: ${backupFile}`);
            return backupFile;
            
        } catch (error) {
            console.error('✗ Error creating backup:', error.message);
            throw error;
        }
    }

    async restoreFromBackup(backupFile) {
        try {
            console.log(`📥 Restoring from backup: ${backupFile}`);
            
            const fs = require('fs').promises;
            const backupData = JSON.parse(await fs.readFile(backupFile, 'utf8'));
            
            // Clear existing data
            await this.seeder.clearAll();
            
            // Restore data in correct order
            const tables = ['users', 'menu_categories', 'menu_items', 'orders', 'order_items', 'contact_submissions'];
            
            for (const table of tables) {
                if (backupData[table] && backupData[table].length > 0) {
                    console.log(`📋 Restoring ${table}...`);
                    
                    for (const row of backupData[table]) {
                        await db.safeInsert(table, row);
                    }
                }
            }
            
            console.log('✅ Backup restored successfully!');
            
        } catch (error) {
            console.error('❌ Error restoring backup:', error.message);
            throw error;
        }
    }

    async getStatus() {
        try {
            console.log('📊 Database Status:');
            
            // Check connection
            const isConnected = await db.testConnection();
            console.log(`Connection: ${isConnected ? '✅ Connected' : '❌ Failed'}`);
            
            if (!isConnected) return;
            
            // Check if tables exist
            const tablesQuery = `
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name
            `;
            const tables = await db.query(tablesQuery);
            console.log(`Tables: ${tables.length} found`);
            tables.forEach(table => console.log(`  - ${table.table_name}`));
            
            // Check data counts
            if (tables.length > 0) {
                console.log('\nData counts:');
                const dataTables = ['users', 'menu_categories', 'menu_items', 'orders', 'contact_submissions'];
                
                for (const table of dataTables) {
                    try {
                        const count = await db.query(`SELECT COUNT(*) as count FROM ${table}`);
                        console.log(`  - ${table}: ${count[0].count} records`);
                    } catch (error) {
                        console.log(`  - ${table}: table not found`);
                    }
                }
            }
            
        } catch (error) {
            console.error('Error getting database status:', error.message);
        }
    }
}

// CLI interface
async function main() {
    const reset = new DatabaseReset();
    const command = process.argv[2];
    const arg = process.argv[3];

    try {
        switch (command) {
            case 'reset':
                await reset.resetDatabase();
                break;
            case 'backup':
                await reset.backupDatabase();
                break;
            case 'restore':
                if (!arg) {
                    console.log('Usage: node reset.js restore <backup-file>');
                    process.exit(1);
                }
                await reset.restoreFromBackup(arg);
                break;
            case 'status':
            case undefined:
                await reset.getStatus();
                break;
            default:
                console.log('Usage: node reset.js [reset|backup|restore|status]');
                console.log('  reset: Complete database reset (drop, migrate, seed)');
                console.log('  backup: Create a backup of current data');
                console.log('  restore <file>: Restore from backup file');
                console.log('  status (default): Show database status');
        }
    } catch (error) {
        console.error('Reset command failed:', error.message);
        process.exit(1);
    } finally {
        await db.close();
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = DatabaseReset;
