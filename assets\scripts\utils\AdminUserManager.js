import { api } from '../api.js';
import { Alert } from './Alert.js';
import { formatPrice } from '../config/currency.js';

export class AdminUserManager {
    constructor() {
        this.users = [];
        this.selectedUsers = new Set();
        this.filters = {
            role: '',
            status: '',
            search: '',
            registrationDate: '',
            lastActivity: ''
        };
        this.sortBy = 'created_at';
        this.sortOrder = 'desc';
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.isLoading = false;
        this.currentView = 'list'; // 'list', 'profile', 'activity'
        
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadUsers();
    }

    setupEventListeners() {
        // View switching
        document.addEventListener('click', (e) => {
            if (e.target.matches('.view-switch-btn')) {
                this.switchView(e.target.dataset.view);
            }
        });

        // User selection
        document.addEventListener('change', (e) => {
            if (e.target.matches('.user-checkbox')) {
                this.handleUserSelection(e.target);
            }
            
            if (e.target.matches('#select-all-users')) {
                this.handleSelectAll(e.target.checked);
            }
        });

        // Bulk operations
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.bulk-action-btn')) {
                e.preventDefault();
                await this.handleBulkAction(e.target.dataset.action);
            }
        });

        // Individual user actions
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.view-user-btn')) {
                e.preventDefault();
                this.viewUserProfile(e.target.dataset.userId);
            }
            
            if (e.target.matches('.edit-user-btn')) {
                e.preventDefault();
                this.editUser(e.target.dataset.userId);
            }
            
            if (e.target.matches('.toggle-status-btn')) {
                e.preventDefault();
                await this.toggleUserStatus(e.target.dataset.userId);
            }
            
            if (e.target.matches('.send-message-btn')) {
                e.preventDefault();
                this.sendMessage(e.target.dataset.userId);
            }
            
            if (e.target.matches('.view-orders-btn')) {
                e.preventDefault();
                this.viewUserOrders(e.target.dataset.userId);
            }
        });

        // Filters and search
        document.addEventListener('change', (e) => {
            if (e.target.matches('.filter-select')) {
                this.updateFilter(e.target.name, e.target.value);
            }
        });

        document.addEventListener('input', (e) => {
            if (e.target.matches('#user-search')) {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.updateFilter('search', e.target.value);
                }, 300);
            }
        });

        // Sorting
        document.addEventListener('click', (e) => {
            if (e.target.matches('.sort-btn')) {
                this.updateSort(e.target.dataset.sort);
            }
        });

        // Pagination
        document.addEventListener('click', (e) => {
            if (e.target.matches('.page-btn')) {
                e.preventDefault();
                this.goToPage(parseInt(e.target.dataset.page));
            }
        });

        // Export users
        document.addEventListener('click', (e) => {
            if (e.target.matches('#export-users-btn')) {
                e.preventDefault();
                this.exportUsers();
            }
        });
    }

    async loadUsers() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading(true);

        try {
            const params = {
                page: this.currentPage,
                limit: this.itemsPerPage,
                sortBy: this.sortBy,
                sortOrder: this.sortOrder,
                ...this.filters
            };

            // Remove empty filters
            Object.keys(params).forEach(key => {
                if (params[key] === '') delete params[key];
            });

            const response = await api.getAdminUsers(params);
            this.users = response.users || [];
            this.pagination = response.pagination || {};

            this.renderCurrentView();
            this.updateUserStats();

        } catch (error) {
            console.error('Failed to load users:', error);
            Alert.show('Failed to load users', 'error');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    switchView(view) {
        this.currentView = view;
        
        // Update active tab
        document.querySelectorAll('.view-switch-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');
        
        this.renderCurrentView();
    }

    renderCurrentView() {
        const container = document.querySelector('#user-management-content');
        if (!container) return;

        switch (this.currentView) {
            case 'list':
                this.renderUsersListView(container);
                break;
            case 'analytics':
                this.renderUserAnalyticsView(container);
                break;
            default:
                this.renderUsersListView(container);
        }
    }

    renderUsersListView(container) {
        const filteredUsers = this.getFilteredUsers();
        
        container.innerHTML = `
            <div class="users-header">
                <div class="users-controls">
                    <div class="search-box">
                        <input type="text" id="user-search" placeholder="Search users..." 
                               value="${this.filters.search}">
                        <i class="fas fa-search"></i>
                    </div>
                    
                    <div class="filters">
                        <select name="role" class="filter-select">
                            <option value="">All Roles</option>
                            <option value="customer" ${this.filters.role === 'customer' ? 'selected' : ''}>Customers</option>
                            <option value="admin" ${this.filters.role === 'admin' ? 'selected' : ''}>Admins</option>
                        </select>
                        
                        <select name="status" class="filter-select">
                            <option value="">All Status</option>
                            <option value="true" ${this.filters.status === 'true' ? 'selected' : ''}>Active</option>
                            <option value="false" ${this.filters.status === 'false' ? 'selected' : ''}>Inactive</option>
                        </select>
                        
                        <select name="registrationDate" class="filter-select">
                            <option value="">All Time</option>
                            <option value="today" ${this.filters.registrationDate === 'today' ? 'selected' : ''}>Today</option>
                            <option value="week" ${this.filters.registrationDate === 'week' ? 'selected' : ''}>This Week</option>
                            <option value="month" ${this.filters.registrationDate === 'month' ? 'selected' : ''}>This Month</option>
                        </select>
                    </div>
                    
                    <button id="export-users-btn" class="btn btn-secondary">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
                
                <div class="bulk-actions" ${this.selectedUsers.size === 0 ? 'style="display: none;"' : ''}>
                    <span class="selected-count">${this.selectedUsers.size} users selected</span>
                    <div class="bulk-buttons">
                        <button class="bulk-action-btn btn btn-sm" data-action="activate">Activate</button>
                        <button class="bulk-action-btn btn btn-sm" data-action="deactivate">Deactivate</button>
                        <button class="bulk-action-btn btn btn-sm" data-action="message">Send Message</button>
                        <button class="bulk-action-btn btn btn-sm" data-action="export">Export Selected</button>
                    </div>
                </div>
            </div>
            
            <div class="users-table">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="select-all-users">
                            </th>
                            <th class="sortable" data-sort="name">
                                Name <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="email">
                                Email <i class="fas fa-sort"></i>
                            </th>
                            <th>Phone</th>
                            <th class="sortable" data-sort="role">
                                Role <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="status">
                                Status <i class="fas fa-sort"></i>
                            </th>
                            <th>Orders</th>
                            <th>Total Spent</th>
                            <th class="sortable" data-sort="created_at">
                                Joined <i class="fas fa-sort"></i>
                            </th>
                            <th>Last Activity</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredUsers.map(user => this.renderUserRow(user)).join('')}
                    </tbody>
                </table>
                
                ${filteredUsers.length === 0 ? `
                    <div class="empty-state">
                        <i class="fas fa-users"></i>
                        <h3>No users found</h3>
                        <p>Try adjusting your filters or check back later.</p>
                    </div>
                ` : ''}
            </div>
            
            ${this.renderPagination()}
        `;
    }

    renderUserRow(user) {
        const isSelected = this.selectedUsers.has(user.id);
        const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'N/A';
        
        return `
            <tr class="user-row ${isSelected ? 'selected' : ''} ${!user.isActive ? 'inactive' : ''}">
                <td>
                    <input type="checkbox" class="user-checkbox" 
                           value="${user.id}" ${isSelected ? 'checked' : ''}>
                </td>
                <td>
                    <div class="user-info">
                        <div class="user-avatar">
                            <img src="${user.avatar || '/assets/images/default-avatar.png'}" 
                                 alt="${fullName}" class="avatar-img">
                        </div>
                        <div class="user-details">
                            <strong class="user-name">${fullName}</strong>
                            ${user.emailVerified ? '<i class="fas fa-check-circle verified" title="Email Verified"></i>' : ''}
                        </div>
                    </div>
                </td>
                <td>
                    <span class="user-email">${user.email}</span>
                </td>
                <td>
                    <span class="user-phone">${user.phone || 'N/A'}</span>
                </td>
                <td>
                    <span class="role-badge role-${user.role}">${user.role}</span>
                </td>
                <td>
                    <span class="status-badge ${user.isActive ? 'active' : 'inactive'}">
                        ${user.isActive ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>
                    <span class="order-count">${user.orderCount || 0}</span>
                </td>
                <td>
                    <span class="total-spent">${formatPrice(user.totalSpent || 0)}</span>
                </td>
                <td>
                    <span class="join-date" title="${new Date(user.createdAt).toLocaleString()}">
                        ${this.getTimeAgo(user.createdAt)}
                    </span>
                </td>
                <td>
                    <span class="last-activity" title="${user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleString() : 'Never'}">
                        ${user.lastLoginAt ? this.getTimeAgo(user.lastLoginAt) : 'Never'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm view-user-btn" data-user-id="${user.id}" title="View Profile">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm edit-user-btn" data-user-id="${user.id}" title="Edit User">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm toggle-status-btn" 
                                data-user-id="${user.id}" 
                                title="${user.isActive ? 'Deactivate' : 'Activate'}">
                            <i class="fas fa-${user.isActive ? 'user-slash' : 'user-check'}"></i>
                        </button>
                        <button class="btn btn-sm send-message-btn" data-user-id="${user.id}" title="Send Message">
                            <i class="fas fa-envelope"></i>
                        </button>
                        <button class="btn btn-sm view-orders-btn" data-user-id="${user.id}" title="View Orders">
                            <i class="fas fa-shopping-bag"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    renderUserAnalyticsView(container) {
        const stats = this.calculateUserStats();
        
        container.innerHTML = `
            <div class="user-analytics">
                <div class="analytics-header">
                    <h3>User Analytics</h3>
                    <div class="date-range-selector">
                        <select id="analytics-period">
                            <option value="7d">Last 7 Days</option>
                            <option value="30d" selected>Last 30 Days</option>
                            <option value="90d">Last 90 Days</option>
                            <option value="1y">Last Year</option>
                        </select>
                    </div>
                </div>
                
                <div class="analytics-cards">
                    <div class="analytics-card">
                        <div class="card-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="card-content">
                            <h4>Total Users</h4>
                            <div class="card-value">${stats.totalUsers}</div>
                            <div class="card-change positive">+${stats.newUsersThisMonth} this month</div>
                        </div>
                    </div>
                    
                    <div class="analytics-card">
                        <div class="card-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="card-content">
                            <h4>Active Users</h4>
                            <div class="card-value">${stats.activeUsers}</div>
                            <div class="card-change">${((stats.activeUsers / stats.totalUsers) * 100).toFixed(1)}% of total</div>
                        </div>
                    </div>
                    
                    <div class="analytics-card">
                        <div class="card-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="card-content">
                            <h4>Users with Orders</h4>
                            <div class="card-value">${stats.usersWithOrders}</div>
                            <div class="card-change">${((stats.usersWithOrders / stats.totalUsers) * 100).toFixed(1)}% conversion</div>
                        </div>
                    </div>
                    
                    <div class="analytics-card">
                        <div class="card-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="card-content">
                            <h4>Avg. Customer Value</h4>
                            <div class="card-value">${formatPrice(stats.averageCustomerValue)}</div>
                            <div class="card-change">Per customer lifetime</div>
                        </div>
                    </div>
                </div>
                
                <div class="analytics-charts">
                    <div class="chart-container">
                        <h4>User Registration Trend</h4>
                        <canvas id="registrationChart"></canvas>
                    </div>
                    
                    <div class="chart-container">
                        <h4>User Activity</h4>
                        <canvas id="activityChart"></canvas>
                    </div>
                </div>
                
                <div class="user-segments">
                    <h4>User Segments</h4>
                    <div class="segments-grid">
                        <div class="segment-card">
                            <h5>New Users (0 orders)</h5>
                            <div class="segment-count">${stats.newUsers}</div>
                            <div class="segment-percentage">${((stats.newUsers / stats.totalUsers) * 100).toFixed(1)}%</div>
                        </div>
                        
                        <div class="segment-card">
                            <h5>Regular Customers (1-5 orders)</h5>
                            <div class="segment-count">${stats.regularCustomers}</div>
                            <div class="segment-percentage">${((stats.regularCustomers / stats.totalUsers) * 100).toFixed(1)}%</div>
                        </div>
                        
                        <div class="segment-card">
                            <h5>VIP Customers (5+ orders)</h5>
                            <div class="segment-count">${stats.vipCustomers}</div>
                            <div class="segment-percentage">${((stats.vipCustomers / stats.totalUsers) * 100).toFixed(1)}%</div>
                        </div>
                        
                        <div class="segment-card">
                            <h5>Inactive Users (30+ days)</h5>
                            <div class="segment-count">${stats.inactiveUsers}</div>
                            <div class="segment-percentage">${((stats.inactiveUsers / stats.totalUsers) * 100).toFixed(1)}%</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderPagination() {
        if (!this.pagination) return '';

        const { page, pages, total, hasNext, hasPrev } = this.pagination;
        
        return `
            <div class="users-pagination">
                <div class="pagination-info">
                    Showing ${((page - 1) * this.itemsPerPage) + 1} to ${Math.min(page * this.itemsPerPage, total)} of ${total} users
                </div>
                <div class="pagination-controls">
                    <button class="btn btn-sm page-btn" data-page="1" ${!hasPrev ? 'disabled' : ''}>
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button class="btn btn-sm page-btn" data-page="${page - 1}" ${!hasPrev ? 'disabled' : ''}>
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <span class="page-info">Page ${page} of ${pages}</span>
                    <button class="btn btn-sm page-btn" data-page="${page + 1}" ${!hasNext ? 'disabled' : ''}>
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <button class="btn btn-sm page-btn" data-page="${pages}" ${!hasNext ? 'disabled' : ''}>
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        `;
    }

    getFilteredUsers() {
        return this.users.filter(user => {
            if (this.filters.role && user.role !== this.filters.role) return false;
            if (this.filters.status !== '' && user.isActive.toString() !== this.filters.status) return false;
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                const fullName = `${user.firstName || ''} ${user.lastName || ''}`.toLowerCase();
                if (!fullName.includes(searchTerm) && !user.email.toLowerCase().includes(searchTerm)) {
                    return false;
                }
            }
            return true;
        });
    }

    calculateUserStats() {
        const now = new Date();
        const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
        
        return {
            totalUsers: this.users.length,
            activeUsers: this.users.filter(u => u.isActive).length,
            newUsersThisMonth: this.users.filter(u => new Date(u.createdAt) > thirtyDaysAgo).length,
            usersWithOrders: this.users.filter(u => (u.orderCount || 0) > 0).length,
            averageCustomerValue: this.users.reduce((sum, u) => sum + (u.totalSpent || 0), 0) / this.users.length,
            newUsers: this.users.filter(u => (u.orderCount || 0) === 0).length,
            regularCustomers: this.users.filter(u => (u.orderCount || 0) >= 1 && (u.orderCount || 0) <= 5).length,
            vipCustomers: this.users.filter(u => (u.orderCount || 0) > 5).length,
            inactiveUsers: this.users.filter(u => {
                if (!u.lastLoginAt) return true;
                const lastLogin = new Date(u.lastLoginAt);
                return (now - lastLogin) > (30 * 24 * 60 * 60 * 1000);
            }).length
        };
    }

    async toggleUserStatus(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        try {
            await api.updateUserStatus(userId, !user.isActive);
            user.isActive = !user.isActive;
            
            Alert.show(`User ${user.isActive ? 'activated' : 'deactivated'} successfully`, 'success');
            this.renderCurrentView();
            
        } catch (error) {
            console.error('Failed to toggle user status:', error);
            Alert.show('Failed to update user status', 'error');
        }
    }

    viewUserProfile(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        this.showUserProfileModal(user);
    }

    showUserProfileModal(user) {
        const modal = document.createElement('div');
        modal.className = 'modal user-profile-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>User Profile</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="user-profile-content">
                        <div class="profile-header">
                            <div class="profile-avatar">
                                <img src="${user.avatar || '/assets/images/default-avatar.png'}" 
                                     alt="${user.firstName} ${user.lastName}">
                            </div>
                            <div class="profile-info">
                                <h4>${user.firstName} ${user.lastName}</h4>
                                <p class="user-email">${user.email}</p>
                                <div class="profile-badges">
                                    <span class="role-badge role-${user.role}">${user.role}</span>
                                    <span class="status-badge ${user.isActive ? 'active' : 'inactive'}">
                                        ${user.isActive ? 'Active' : 'Inactive'}
                                    </span>
                                    ${user.emailVerified ? '<span class="verified-badge">Verified</span>' : ''}
                                </div>
                            </div>
                        </div>
                        
                        <div class="profile-details">
                            <div class="detail-section">
                                <h5>Contact Information</h5>
                                <div class="detail-grid">
                                    <div class="detail-item">
                                        <label>Phone:</label>
                                        <span>${user.phone || 'Not provided'}</span>
                                    </div>
                                    <div class="detail-item">
                                        <label>Email Verified:</label>
                                        <span>${user.emailVerified ? 'Yes' : 'No'}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="detail-section">
                                <h5>Account Statistics</h5>
                                <div class="detail-grid">
                                    <div class="detail-item">
                                        <label>Total Orders:</label>
                                        <span>${user.orderCount || 0}</span>
                                    </div>
                                    <div class="detail-item">
                                        <label>Total Spent:</label>
                                        <span>${formatPrice(user.totalSpent || 0)}</span>
                                    </div>
                                    <div class="detail-item">
                                        <label>Joined:</label>
                                        <span>${new Date(user.createdAt).toLocaleDateString()}</span>
                                    </div>
                                    <div class="detail-item">
                                        <label>Last Login:</label>
                                        <span>${user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString() : 'Never'}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary modal-close">Close</button>
                    <button class="btn btn-primary edit-user-btn" data-user-id="${user.id}">Edit User</button>
                    <button class="btn btn-info view-orders-btn" data-user-id="${user.id}">View Orders</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.classList.add('active');

        // Close modal handlers
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => {
                modal.remove();
            });
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    handleUserSelection(checkbox) {
        const userId = checkbox.value;
        
        if (checkbox.checked) {
            this.selectedUsers.add(userId);
        } else {
            this.selectedUsers.delete(userId);
        }
        
        this.updateBulkActionsVisibility();
        this.updateSelectAllState();
    }

    handleSelectAll(checked) {
        const filteredUsers = this.getFilteredUsers();
        
        if (checked) {
            filteredUsers.forEach(user => this.selectedUsers.add(user.id));
        } else {
            filteredUsers.forEach(user => this.selectedUsers.delete(user.id));
        }
        
        // Update checkboxes
        document.querySelectorAll('.user-checkbox').forEach(checkbox => {
            checkbox.checked = checked;
        });
        
        this.updateBulkActionsVisibility();
    }

    updateBulkActionsVisibility() {
        const bulkActions = document.querySelector('.bulk-actions');
        const selectedCount = document.querySelector('.selected-count');
        
        if (bulkActions) {
            bulkActions.style.display = this.selectedUsers.size > 0 ? 'flex' : 'none';
        }
        
        if (selectedCount) {
            selectedCount.textContent = `${this.selectedUsers.size} users selected`;
        }
    }

    updateSelectAllState() {
        const selectAllCheckbox = document.querySelector('#select-all-users');
        const filteredUsers = this.getFilteredUsers();
        const selectedFilteredUsers = filteredUsers.filter(user => this.selectedUsers.has(user.id));
        
        if (selectAllCheckbox) {
            if (selectedFilteredUsers.length === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (selectedFilteredUsers.length === filteredUsers.length) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }
    }

    async handleBulkAction(action) {
        if (this.selectedUsers.size === 0) {
            Alert.show('No users selected', 'warning');
            return;
        }

        const userIds = Array.from(this.selectedUsers);
        
        try {
            switch (action) {
                case 'activate':
                    await this.bulkUpdateStatus(userIds, true);
                    Alert.show(`${userIds.length} users activated`, 'success');
                    break;
                    
                case 'deactivate':
                    await this.bulkUpdateStatus(userIds, false);
                    Alert.show(`${userIds.length} users deactivated`, 'success');
                    break;
                    
                case 'message':
                    this.showBulkMessageModal(userIds);
                    return;
                    
                case 'export':
                    this.exportSelectedUsers(userIds);
                    return;
            }
            
            // Clear selection and reload
            this.selectedUsers.clear();
            await this.loadUsers();
            
        } catch (error) {
            console.error('Bulk action failed:', error);
            Alert.show(`Failed to ${action} users`, 'error');
        }
    }

    async bulkUpdateStatus(userIds, isActive) {
        const promises = userIds.map(userId => api.updateUserStatus(userId, isActive));
        await Promise.all(promises);
    }

    updateFilter(filterName, value) {
        this.filters[filterName] = value;
        this.currentPage = 1; // Reset to first page
        this.loadUsers();
    }

    updateSort(sortBy) {
        if (this.sortBy === sortBy) {
            this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortBy = sortBy;
            this.sortOrder = 'desc';
        }
        
        this.loadUsers();
    }

    goToPage(page) {
        this.currentPage = page;
        this.loadUsers();
    }

    updateUserStats() {
        const stats = this.calculateUserStats();
        
        // Update stat cards if they exist
        this.updateStatCard('total-users', stats.totalUsers);
        this.updateStatCard('active-users', stats.activeUsers);
        this.updateStatCard('new-users', stats.newUsersThisMonth);
        this.updateStatCard('users-with-orders', stats.usersWithOrders);
    }

    updateStatCard(id, value) {
        const card = document.querySelector(`[data-stat="${id}"] .stat-value`);
        if (card) {
            card.textContent = value;
        }
    }

    getTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'Just now';
        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
        
        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `${diffInHours}h ago`;
        
        const diffInDays = Math.floor(diffInHours / 24);
        if (diffInDays < 30) return `${diffInDays}d ago`;
        
        const diffInMonths = Math.floor(diffInDays / 30);
        return `${diffInMonths}mo ago`;
    }

    exportUsers() {
        const csvContent = this.generateUserCSV(this.users);
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `users-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    generateUserCSV(users) {
        const headers = ['Name', 'Email', 'Phone', 'Role', 'Status', 'Orders', 'Total Spent', 'Joined', 'Last Login'];
        const rows = users.map(user => [
            `${user.firstName || ''} ${user.lastName || ''}`.trim(),
            user.email,
            user.phone || '',
            user.role,
            user.isActive ? 'Active' : 'Inactive',
            user.orderCount || 0,
            user.totalSpent || 0,
            new Date(user.createdAt).toLocaleDateString(),
            user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString() : 'Never'
        ]);
        
        return [headers, ...rows].map(row => 
            row.map(field => `"${field}"`).join(',')
        ).join('\n');
    }

    showLoading(show) {
        const loader = document.querySelector('.users-loader');
        if (loader) {
            loader.style.display = show ? 'block' : 'none';
        }
    }
}

// Create global admin user manager instance
export const adminUserManager = new AdminUserManager();
