# Magic Menu Database System

## Overview

This directory contains the complete database system for the Magic Menu restaurant ordering application. The system uses PostgreSQL with a comprehensive schema designed to support user management, menu operations, cart functionality, order processing, and administrative features.

## Database Architecture

### Core Tables

1. **users** - Customer and admin user accounts
2. **user_addresses** - User delivery addresses
3. **menu_categories** - Menu organization categories
4. **menu_items** - Restaurant menu items with full details
5. **shopping_carts** - User shopping carts (persistent)
6. **cart_items** - Items within shopping carts
7. **orders** - Customer orders with full details
8. **order_items** - Items within orders (historical snapshot)
9. **contact_submissions** - Contact form submissions
10. **user_sessions** - User authentication sessions

### Key Features

- **UUID Primary Keys** - All tables use UUID for better security and scalability
- **Role-Based Access** - Users can be 'customer' or 'admin'
- **Soft Deletes** - Important data is deactivated rather than deleted
- **Audit Trails** - Created/updated timestamps on all records
- **Data Integrity** - Comprehensive foreign key constraints
- **Performance Optimized** - Strategic indexes for common queries

## File Structure

```
server/database/
├── README.md              # This documentation
├── schema.sql             # Complete database schema
├── migrate.js             # Migration system
├── seed.js               # Initial data seeding
├── reset.js              # Database reset utilities
├── test.js               # Comprehensive testing
└── migrations/           # Future migration files
    └── 002_add_menu_item_tags.sql
```

## Setup Instructions

### 1. Prerequisites

- PostgreSQL 12+ installed and running
- Node.js 16+ with npm
- Environment variables configured (see .env.example)

### 2. Database Configuration

1. Create PostgreSQL database:
```sql
CREATE DATABASE magic_menu;
CREATE USER magic_menu_user WITH PASSWORD 'magic_menu_pass';
GRANT ALL PRIVILEGES ON DATABASE magic_menu TO magic_menu_user;
```

2. Update .env file with your database credentials:
```env
DATABASE_URL=postgresql://magic_menu_user:magic_menu_pass@localhost:5432/magic_menu
DB_HOST=localhost
DB_PORT=5432
DB_NAME=magic_menu
DB_USER=magic_menu_user
DB_PASSWORD=magic_menu_pass
```

### 3. Initialize Database

```bash
# Install dependencies
npm install

# Run migrations (creates schema)
npm run db:migrate

# Seed initial data
npm run db:seed

# Or do both at once
npm run db:reset
```

## Available Commands

### Migration Commands
```bash
node server/database/migrate.js          # Run pending migrations
node server/database/migrate.js up       # Same as above
node server/database/migrate.js rollback # Rollback last migration
node server/database/migrate.js reset    # Drop all tables and re-migrate
```

### Seeding Commands
```bash
node server/database/seed.js             # Add seed data
node server/database/seed.js seed        # Same as above
node server/database/seed.js clear       # Remove all seed data
node server/database/seed.js reset       # Clear and re-seed
```

### Reset Commands
```bash
node server/database/reset.js            # Show database status
node server/database/reset.js status     # Same as above
node server/database/reset.js reset      # Complete reset (drop, migrate, seed)
node server/database/reset.js backup     # Create backup
node server/database/reset.js restore <file> # Restore from backup
```

### Testing Commands
```bash
node server/database/test.js             # Run all database tests
```

## Data Models

### User Model (`server/models/User.js`)
- User creation with password hashing
- Email verification and password reset
- Role-based access control
- User profile management
- Search and statistics functions

### Menu Models (`server/models/MenuCategory.js`, `MenuItem.js`)
- Category management with ordering
- Menu item CRUD operations
- Search and filtering capabilities
- Allergen and nutritional information
- Popularity tracking

### Cart Model (`server/models/Cart.js`)
- User and session-based carts
- Item management (add, update, remove)
- Total calculations with tax and delivery
- Guest cart merging on login

## Seed Data

The system includes comprehensive seed data:

### Default Admin User
- **Email**: <EMAIL>
- **Password**: SecureAdminPassword123!
- **Role**: admin

### Sample Customer
- **Email**: <EMAIL>
- **Password**: CustomerPassword123!
- **Role**: customer

### Menu Categories
1. **Local Delights** - Traditional Nigerian dishes
2. **International Flavors** - Popular international dishes
3. **Beverages** - Drinks and traditional beverages
4. **Desserts** - Sweet treats and desserts

### Sample Menu Items
- 12 diverse menu items across all categories
- Realistic pricing in Nigerian Naira (₦)
- Complete nutritional information
- Allergen and ingredient data

## Security Features

### Password Security
- bcrypt hashing with configurable salt rounds
- Minimum password complexity requirements
- Password reset token system

### Data Protection
- Parameterized queries prevent SQL injection
- Input validation and sanitization
- Role-based access control
- Session management with Redis

### Audit Trail
- All modifications tracked with timestamps
- User action logging
- Data integrity constraints

## Performance Considerations

### Indexing Strategy
- Primary keys (UUID) automatically indexed
- Foreign keys indexed for join performance
- Search fields (email, name) indexed
- Composite indexes for common query patterns

### Query Optimization
- Efficient pagination support
- Bulk operation capabilities
- Connection pooling
- Query result caching (planned)

## Testing

The test suite covers:
- Database connectivity
- Schema integrity
- Model operations (CRUD)
- Data integrity constraints
- Performance benchmarks
- Error handling

Run tests with:
```bash
npm test
# or
node server/database/test.js
```

## Backup and Recovery

### Automated Backups
```bash
# Create backup
node server/database/reset.js backup

# Restore from backup
node server/database/reset.js restore backup-2024-01-01T12-00-00-000Z.json
```

### Production Backups
For production environments, use PostgreSQL's built-in tools:
```bash
# Create backup
pg_dump magic_menu > backup.sql

# Restore backup
psql magic_menu < backup.sql
```

## Migration System

### Adding New Migrations
1. Create new file in `migrations/` directory
2. Use sequential numbering: `003_add_new_feature.sql`
3. Include both UP and DOWN operations
4. Test thoroughly before deployment

### Migration Best Practices
- Always backup before running migrations
- Test migrations on development environment first
- Use transactions for complex migrations
- Document breaking changes

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check PostgreSQL is running
   - Verify credentials in .env file
   - Ensure database exists

2. **Migration Errors**
   - Check for syntax errors in SQL
   - Verify foreign key constraints
   - Ensure proper migration order

3. **Seed Data Conflicts**
   - Clear existing data before seeding
   - Check for unique constraint violations
   - Verify foreign key relationships

### Debug Mode
Set `LOG_LEVEL=debug` in .env for detailed logging.

## Contributing

When modifying the database:
1. Update schema.sql for new tables/columns
2. Create migration files for changes
3. Update relevant models
4. Add/update tests
5. Update this documentation

## Support

For database-related issues:
1. Check the logs for detailed error messages
2. Run the test suite to identify problems
3. Verify environment configuration
4. Check PostgreSQL server status
