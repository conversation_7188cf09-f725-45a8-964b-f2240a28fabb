const { Pool } = require('pg');
const sql = require('sql-template-strings');
require('dotenv').config();

class DatabaseSecurity {
    constructor() {
        this.pool = new Pool({
            connectionString: process.env.DATABASE_URL,
            host: process.env.DB_HOST,
            port: process.env.DB_PORT,
            database: process.env.DB_NAME,
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
            max: 20,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 2000,
        });

        // Handle pool errors
        this.pool.on('error', (err) => {
            console.error('Unexpected error on idle client', err);
            process.exit(-1);
        });
    }

    async query(text, params) {
        const client = await this.pool.connect();
        try {
            // Use parameterized queries
            const result = await client.query(text, params);
            return result.rows;
        } finally {
            client.release();
        }
    }

    // Safe query builder for common operations
    async safeSelect(table, conditions, fields = ['*']) {
        const query = sql`
            SELECT ${fields.join(', ')}
            FROM ${table}
            WHERE ${Object.keys(conditions).map(key => 
                `${key} = ${conditions[key]}`).join(' AND ')}
        `;
        return this.query(query);
    }

    async safeInsert(table, data) {
        const fields = Object.keys(data);
        const values = Object.values(data);
        const query = sql`
            INSERT INTO ${table} (${fields.join(', ')})
            VALUES (${values.map((_, i) => `$${i + 1}`).join(', ')})
            RETURNING *
        `;
        return this.query(query, values);
    }

    async safeUpdate(table, data, conditions) {
        const fields = Object.keys(data);
        const values = Object.values(data);
        const conditionKeys = Object.keys(conditions);
        const conditionValues = Object.values(conditions);

        const setClause = fields.map((field, i) => `${field} = $${i + 1}`).join(', ');
        const whereClause = conditionKeys.map((key, i) => `${key} = $${fields.length + i + 1}`).join(' AND ');

        const query = `
            UPDATE ${table}
            SET ${setClause}
            WHERE ${whereClause}
            RETURNING *
        `;

        return this.query(query, [...values, ...conditionValues]);
    }

    async safeDelete(table, conditions) {
        const conditionKeys = Object.keys(conditions);
        const conditionValues = Object.values(conditions);
        const whereClause = conditionKeys.map((key, i) => `${key} = $${i + 1}`).join(' AND ');

        const query = `DELETE FROM ${table} WHERE ${whereClause} RETURNING *`;
        return this.query(query, conditionValues);
    }

    // Test database connection
    async testConnection() {
        try {
            const client = await this.pool.connect();
            const result = await client.query('SELECT NOW()');
            client.release();
            console.log('Database connection successful:', result.rows[0]);
            return true;
        } catch (error) {
            console.error('Database connection failed:', error.message);
            return false;
        }
    }

    // Close all connections
    async close() {
        await this.pool.end();
    }
}

module.exports = new DatabaseSecurity();