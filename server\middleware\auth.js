const JwtUtils = require('../utils/JwtUtils');
const User = require('../models/User');

class AuthMiddleware {
    // Middleware to authenticate requests
    static async authenticate(req, res, next) {
        try {
            const authHeader = req.headers.authorization;
            const token = JwtUtils.extractTokenFromHeader(authHeader);

            if (!token) {
                return res.status(401).json({
                    error: 'Authentication required',
                    message: 'No token provided'
                });
            }

            // Verify token
            const decoded = JwtUtils.verifyAccessToken(token);
            
            // Get user from database
            const user = await User.findById(decoded.userId);
            if (!user) {
                return res.status(401).json({
                    error: 'Authentication failed',
                    message: 'User not found'
                });
            }

            if (!user.is_active) {
                return res.status(401).json({
                    error: 'Authentication failed',
                    message: 'User account is inactive'
                });
            }

            // Add user to request object
            req.user = {
                id: user.id,
                email: user.email,
                role: user.role,
                firstName: user.first_name,
                lastName: user.last_name,
                isActive: user.is_active,
                emailVerified: user.email_verified
            };

            next();
        } catch (error) {
            console.error('Authentication error:', error);
            
            if (error.message.includes('expired')) {
                return res.status(401).json({
                    error: 'Token expired',
                    message: 'Please login again'
                });
            }

            return res.status(401).json({
                error: 'Authentication failed',
                message: 'Invalid token'
            });
        }
    }

    // Middleware to check if user has required role
    static requireRole(roles) {
        return (req, res, next) => {
            if (!req.user) {
                return res.status(401).json({
                    error: 'Authentication required',
                    message: 'User not authenticated'
                });
            }

            const userRole = req.user.role;
            const allowedRoles = Array.isArray(roles) ? roles : [roles];

            if (!allowedRoles.includes(userRole)) {
                return res.status(403).json({
                    error: 'Access denied',
                    message: 'Insufficient permissions'
                });
            }

            next();
        };
    }

    // Middleware to require admin role
    static requireAdmin(req, res, next) {
        return AuthMiddleware.requireRole('admin')(req, res, next);
    }

    // Middleware to require customer role
    static requireCustomer(req, res, next) {
        return AuthMiddleware.requireRole('customer')(req, res, next);
    }

    // Middleware to require email verification
    static requireEmailVerification(req, res, next) {
        if (!req.user) {
            return res.status(401).json({
                error: 'Authentication required',
                message: 'User not authenticated'
            });
        }

        if (!req.user.emailVerified) {
            return res.status(403).json({
                error: 'Email verification required',
                message: 'Please verify your email address'
            });
        }

        next();
    }

    // Optional authentication - doesn't fail if no token
    static async optionalAuth(req, res, next) {
        try {
            const authHeader = req.headers.authorization;
            const token = JwtUtils.extractTokenFromHeader(authHeader);

            if (!token) {
                req.user = null;
                return next();
            }

            // Verify token
            const decoded = JwtUtils.verifyAccessToken(token);
            
            // Get user from database
            const user = await User.findById(decoded.userId);
            if (!user || !user.is_active) {
                req.user = null;
                return next();
            }

            // Add user to request object
            req.user = {
                id: user.id,
                email: user.email,
                role: user.role,
                firstName: user.first_name,
                lastName: user.last_name,
                isActive: user.is_active,
                emailVerified: user.email_verified
            };

            next();
        } catch (error) {
            // Don't fail on optional auth errors
            req.user = null;
            next();
        }
    }

    // Middleware to get user from session (for guest carts)
    static getSessionUser(req, res, next) {
        // If user is authenticated, use that
        if (req.user) {
            return next();
        }

        // Otherwise, create or get session ID for guest users
        if (!req.session.guestId) {
            req.session.guestId = require('crypto').randomUUID();
        }

        req.guestId = req.session.guestId;
        next();
    }

    // Rate limiting for authentication endpoints
    static authRateLimit(maxAttempts = 5, windowMs = 15 * 60 * 1000) {
        const attempts = new Map();

        return (req, res, next) => {
            const key = req.ip + ':' + req.path;
            const now = Date.now();
            
            // Clean old attempts
            for (const [k, v] of attempts.entries()) {
                if (now - v.firstAttempt > windowMs) {
                    attempts.delete(k);
                }
            }

            const userAttempts = attempts.get(key);
            
            if (!userAttempts) {
                attempts.set(key, {
                    count: 1,
                    firstAttempt: now
                });
                return next();
            }

            if (userAttempts.count >= maxAttempts) {
                const timeLeft = Math.ceil((windowMs - (now - userAttempts.firstAttempt)) / 1000 / 60);
                return res.status(429).json({
                    error: 'Too many attempts',
                    message: `Too many authentication attempts. Try again in ${timeLeft} minutes.`
                });
            }

            userAttempts.count++;
            next();
        };
    }

    // Middleware to log authentication events
    static logAuthEvent(event) {
        return (req, res, next) => {
            const originalSend = res.send;
            
            res.send = function(data) {
                // Log successful authentication
                if (res.statusCode < 400) {
                    console.log(`Auth Event: ${event} - User: ${req.user?.email || 'unknown'} - IP: ${req.ip}`);
                } else {
                    console.log(`Auth Event: ${event} FAILED - IP: ${req.ip} - Status: ${res.statusCode}`);
                }
                
                originalSend.call(this, data);
            };
            
            next();
        };
    }

    // Middleware to check if user owns resource
    static requireOwnership(getResourceUserId) {
        return async (req, res, next) => {
            try {
                if (!req.user) {
                    return res.status(401).json({
                        error: 'Authentication required',
                        message: 'User not authenticated'
                    });
                }

                // Admin can access any resource
                if (req.user.role === 'admin') {
                    return next();
                }

                const resourceUserId = await getResourceUserId(req);
                
                if (resourceUserId !== req.user.id) {
                    return res.status(403).json({
                        error: 'Access denied',
                        message: 'You can only access your own resources'
                    });
                }

                next();
            } catch (error) {
                console.error('Ownership check error:', error);
                return res.status(500).json({
                    error: 'Authorization check failed',
                    message: 'Unable to verify resource ownership'
                });
            }
        };
    }
}

module.exports = AuthMiddleware;
