{"apiName": "Magic Menu API", "company": "Magic Menu", "contactEmail": "<EMAIL>", "supportEmail": "<EMAIL>", "baseUrl": "http://localhost:3000", "version": "1.0.0", "endpoints": {"auth": {"login": "/api/auth/login", "register": "/api/auth/register", "logout": "/api/auth/logout", "profile": "/api/auth/profile", "refresh": "/api/auth/refresh"}, "menu": {"categories": "/api/menu/categories", "items": "/api/menu/items", "item": "/api/menu/items/{id}", "popular": "/api/menu/popular", "search": "/api/menu/items?search={query}"}, "cart": {"get": "/api/cart", "add": "/api/cart/add", "update": "/api/cart/items/{id}", "remove": "/api/cart/items/{id}", "clear": "/api/cart", "count": "/api/cart/count", "validate": "/api/cart/validate"}, "orders": {"create": "/api/orders", "get": "/api/orders/{id}", "track": "/api/orders/track/{orderNumber}", "history": "/api/orders/user/history"}, "contact": {"submit": "/api/contact/submit", "stats": "/api/contact/stats"}, "admin": {"dashboard": "/api/admin/dashboard", "users": "/api/admin/users", "orders": "/api/orders/admin/all", "analytics": "/api/admin/analytics/sales"}}, "authentication": {"type": "JWT", "tokenStorage": "localStorage", "tokenKey": "authToken", "refreshTokenKey": "refreshToken", "expiresIn": "24h"}, "features": {"guestCart": true, "orderTracking": true, "realTimeUpdates": false, "offlineSupport": false}}