/**
 * Magic Menu Application Entry Point
 * This file initializes the entire application and coordinates all managers
 */

// Import all managers and utilities
import { stateManager } from './utils/StateManager.js';
import { authManager } from './utils/AuthManager.js';
import { cartManager } from './utils/CartManager.js';
import { menuManager } from './utils/MenuManager.js';
import { orderManager } from './utils/OrderManager.js';
import { adminManager } from './utils/AdminManager.js';
import { contactManager } from './utils/ContactManager.js';
import { api } from './api.js';
import { Alert } from './utils/Alert.js';

class MagicMenuApp {
    constructor() {
        this.initialized = false;
        this.version = '1.0.0';
        this.environment = this.detectEnvironment();
        
        console.log(`🍽️ Magic Menu App v${this.version} - ${this.environment}`);
    }

    async init() {
        if (this.initialized) return;

        try {
            console.log('🚀 Initializing Magic Menu Application...');
            
            // Show loading indicator
            this.showGlobalLoading(true);
            
            // Initialize core systems
            await this.initializeCore();
            
            // Setup global event listeners
            this.setupGlobalEventListeners();
            
            // Setup error handling
            this.setupErrorHandling();
            
            // Initialize page-specific features
            await this.initializePageFeatures();
            
            // Setup development tools
            if (this.environment === 'development') {
                this.setupDevelopmentTools();
            }
            
            this.initialized = true;
            console.log('✅ Magic Menu Application initialized successfully');
            
            // Hide loading indicator
            this.showGlobalLoading(false);
            
            // Show welcome message for first-time users
            this.showWelcomeMessage();
            
        } catch (error) {
            console.error('❌ Application initialization failed:', error);
            this.handleInitializationError(error);
        }
    }

    async initializeCore() {
        // State manager initializes all other managers
        await stateManager.init();
        
        // Verify all managers are ready
        this.verifyManagersReady();
    }

    setupGlobalEventListeners() {
        // Handle navigation
        document.addEventListener('click', (e) => {
            // Handle navigation links
            if (e.target.matches('a[href]') && !e.target.matches('a[href^="http"]')) {
                this.handleInternalNavigation(e);
            }
            
            // Handle modal triggers
            if (e.target.matches('[data-modal]')) {
                this.handleModalTrigger(e);
            }
            
            // Handle quick actions
            if (e.target.matches('[data-action]')) {
                this.handleQuickAction(e);
            }
        });

        // Handle keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Handle online/offline status
        window.addEventListener('online', () => {
            Alert.show('Connection restored', 'success');
            this.handleOnlineStatus(true);
        });

        window.addEventListener('offline', () => {
            Alert.show('Connection lost. Some features may not work.', 'warning');
            this.handleOnlineStatus(false);
        });

        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
    }

    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.logError('JavaScript Error', event.error);
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.logError('Promise Rejection', event.reason);
        });
    }

    async initializePageFeatures() {
        const currentPage = this.getCurrentPage();
        
        // Initialize common features
        this.initializeCommonFeatures();
        
        // Initialize page-specific features
        switch (currentPage) {
            case 'home':
                await this.initializeHomePage();
                break;
            case 'menu':
                await this.initializeMenuPage();
                break;
            case 'cart':
                await this.initializeCartPage();
                break;
            case 'checkout':
                await this.initializeCheckoutPage();
                break;
            case 'account':
                await this.initializeAccountPage();
                break;
            case 'admin':
                await this.initializeAdminPage();
                break;
            case 'contact':
                await this.initializeContactPage();
                break;
            case 'dashboard':
                await this.initializeDashboardPage();
                break;
        }
    }

    initializeCommonFeatures() {
        // Initialize tooltips
        this.initializeTooltips();
        
        // Initialize animations
        this.initializeAnimations();
        
        // Initialize search functionality
        this.initializeSearch();
        
        // Initialize cart sidebar
        this.initializeCartSidebar();
        
        // Initialize notifications
        this.initializeNotifications();
    }

    async initializeHomePage() {
        // Load featured items
        try {
            const popularItems = await api.getPopularItems(6);
            this.displayFeaturedItems(popularItems.items);
        } catch (error) {
            console.error('Failed to load featured items:', error);
        }
    }

    async initializeMenuPage() {
        // Menu manager handles this
        console.log('Menu page initialized');
    }

    async initializeCartPage() {
        // Cart manager handles this
        console.log('Cart page initialized');
    }

    async initializeCheckoutPage() {
        // Order manager handles this
        console.log('Checkout page initialized');
    }

    async initializeAccountPage() {
        // Auth manager handles this
        console.log('Account page initialized');
    }

    async initializeAdminPage() {
        // Admin manager handles this
        console.log('Admin page initialized');
    }

    async initializeContactPage() {
        // Contact manager handles this
        console.log('Contact page initialized');
    }

    async initializeDashboardPage() {
        // State manager handles this
        console.log('Dashboard page initialized');
    }

    // Utility methods
    getCurrentPage() {
        const path = window.location.pathname;
        if (path.includes('menu')) return 'menu';
        if (path.includes('cart')) return 'cart';
        if (path.includes('checkout')) return 'checkout';
        if (path.includes('account')) return 'account';
        if (path.includes('admin')) return 'admin';
        if (path.includes('contact')) return 'contact';
        if (path.includes('dashboard')) return 'dashboard';
        return 'home';
    }

    detectEnvironment() {
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            return 'development';
        }
        if (window.location.hostname.includes('staging')) {
            return 'staging';
        }
        return 'production';
    }

    verifyManagersReady() {
        const managers = {
            stateManager,
            authManager,
            cartManager,
            menuManager,
            orderManager,
            adminManager,
            contactManager
        };

        Object.entries(managers).forEach(([name, manager]) => {
            if (!manager) {
                throw new Error(`${name} is not initialized`);
            }
        });

        console.log('✅ All managers verified and ready');
    }

    showGlobalLoading(show) {
        let loader = document.querySelector('.global-loader');
        
        if (show && !loader) {
            loader = document.createElement('div');
            loader.className = 'global-loader';
            loader.innerHTML = `
                <div class="loader-content">
                    <div class="loader-spinner"></div>
                    <p>Loading Magic Menu...</p>
                </div>
            `;
            document.body.appendChild(loader);
        }
        
        if (loader) {
            loader.style.display = show ? 'flex' : 'none';
        }
    }

    showWelcomeMessage() {
        const isFirstVisit = !localStorage.getItem('magic_menu_visited');
        
        if (isFirstVisit && this.getCurrentPage() === 'home') {
            setTimeout(() => {
                Alert.show('Welcome to Magic Menu! 🍽️', 'success');
                localStorage.setItem('magic_menu_visited', 'true');
            }, 1000);
        }
    }

    handleInitializationError(error) {
        this.showGlobalLoading(false);
        
        const errorContainer = document.createElement('div');
        errorContainer.className = 'initialization-error';
        errorContainer.innerHTML = `
            <div class="error-content">
                <h2>🚫 Application Failed to Load</h2>
                <p>We're sorry, but Magic Menu failed to initialize properly.</p>
                <p>Please try refreshing the page or contact support if the problem persists.</p>
                <button onclick="location.reload()" class="btn btn-primary">Refresh Page</button>
            </div>
        `;
        
        document.body.appendChild(errorContainer);
    }

    setupDevelopmentTools() {
        // Add development tools to window for debugging
        window.magicMenu = {
            app: this,
            stateManager,
            authManager,
            cartManager,
            menuManager,
            orderManager,
            adminManager,
            contactManager,
            api,
            version: this.version,
            environment: this.environment
        };

        console.log('🛠️ Development tools available at window.magicMenu');
    }

    // Event handlers
    handleInternalNavigation(e) {
        // Add loading state for internal navigation
        const link = e.target.closest('a');
        if (link && !link.target) {
            this.showGlobalLoading(true);
        }
    }

    handleModalTrigger(e) {
        // Handle modal opening
        const modalId = e.target.dataset.modal;
        const modal = document.querySelector(`#${modalId}`);
        if (modal) {
            modal.classList.add('active');
        }
    }

    handleQuickAction(e) {
        const action = e.target.dataset.action;
        
        switch (action) {
            case 'scroll-to-top':
                window.scrollTo({ top: 0, behavior: 'smooth' });
                break;
            case 'toggle-cart':
                this.toggleCartSidebar();
                break;
            case 'quick-order':
                window.location.href = '/menu.html';
                break;
        }
    }

    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('#menuSearch, .search-input');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            const activeModal = document.querySelector('.modal.active');
            if (activeModal) {
                activeModal.classList.remove('active');
            }
        }
    }

    handleOnlineStatus(isOnline) {
        document.body.classList.toggle('offline', !isOnline);
        
        if (isOnline) {
            // Retry failed requests
            this.retryFailedRequests();
        }
    }

    handleVisibilityChange() {
        if (document.hidden) {
            // Page is hidden, pause non-critical operations
            this.pauseNonCriticalOperations();
        } else {
            // Page is visible, resume operations
            this.resumeOperations();
        }
    }

    // Additional utility methods
    initializeTooltips() {
        // Initialize tooltip functionality
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(element => {
            // Add tooltip event listeners
        });
    }

    initializeAnimations() {
        // Initialize AOS or other animation libraries
        if (window.AOS) {
            window.AOS.init({
                duration: 800,
                once: true,
                offset: 100
            });
        }
    }

    initializeSearch() {
        // Global search functionality
        const searchInputs = document.querySelectorAll('.search-input');
        searchInputs.forEach(input => {
            let searchTimeout;
            input.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.performGlobalSearch(e.target.value);
                }, 300);
            });
        });
    }

    initializeCartSidebar() {
        // Cart sidebar toggle functionality
        const cartToggle = document.querySelector('.cart-toggle');
        const cartSidebar = document.querySelector('.cart-sidebar');
        
        if (cartToggle && cartSidebar) {
            cartToggle.addEventListener('click', () => {
                this.toggleCartSidebar();
            });
        }
    }

    initializeNotifications() {
        // Create notification container if it doesn't exist
        if (!document.querySelector('.notification-container')) {
            const container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
    }

    toggleCartSidebar() {
        const cartSidebar = document.querySelector('.cart-sidebar');
        if (cartSidebar) {
            cartSidebar.classList.toggle('active');
        }
    }

    async performGlobalSearch(query) {
        if (!query.trim()) return;
        
        try {
            const results = await api.searchMenuItems(query);
            this.displaySearchResults(results.items);
        } catch (error) {
            console.error('Search failed:', error);
        }
    }

    displaySearchResults(items) {
        // Display search results in a dropdown or modal
        console.log('Search results:', items);
    }

    displayFeaturedItems(items) {
        const container = document.querySelector('.featured-items');
        if (container && items) {
            // Render featured items
            console.log('Featured items:', items);
        }
    }

    logError(type, error) {
        // Log errors for monitoring
        console.error(`[${type}]`, error);
        
        // In production, send to error tracking service
        if (this.environment === 'production') {
            // Send to error tracking service
        }
    }

    pauseNonCriticalOperations() {
        // Pause animations, polling, etc.
    }

    resumeOperations() {
        // Resume paused operations
    }

    retryFailedRequests() {
        // Retry any failed API requests
    }
}

// Create and initialize the application
const app = new MagicMenuApp();

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => app.init());
} else {
    app.init();
}

// Export for external use
export default app;
