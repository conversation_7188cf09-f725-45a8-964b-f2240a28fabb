import { api } from '../api.js';
import { Alert } from './Alert.js';

export class AuthManager {
    constructor() {
        this.currentUser = null;
        this.authCallbacks = [];
        this.init();
    }

    async init() {
        // Check if user is authenticated on page load
        await this.checkAuthStatus();
        
        // Set up periodic token refresh
        this.setupTokenRefresh();
        
        // Listen for storage changes (for multi-tab support)
        window.addEventListener('storage', (e) => {
            if (e.key === 'authToken' || e.key === 'refreshToken') {
                this.handleStorageChange();
            }
        });
    }

    async checkAuthStatus() {
        if (api.isAuthenticated()) {
            try {
                const response = await api.getProfile();
                if (response.user) {
                    this.currentUser = response.user;
                    this.notifyAuthCallbacks('login', this.currentUser);
                    return true;
                }
            } catch (error) {
                console.log('Auth check failed:', error);
                await this.logout(false); // Don't call API logout if token is invalid
            }
        }
        
        this.currentUser = null;
        this.notifyAuthCallbacks('logout');
        return false;
    }

    async login(credentials) {
        try {
            const response = await api.login(credentials);
            if (response.user && response.tokens) {
                this.currentUser = response.user;
                this.notifyAuthCallbacks('login', this.currentUser);
                return response;
            }
            throw new Error('Invalid login response');
        } catch (error) {
            console.error('Login failed:', error);
            throw error;
        }
    }

    async register(userData) {
        try {
            const response = await api.register(userData);
            if (response.user && response.tokens) {
                this.currentUser = response.user;
                this.notifyAuthCallbacks('login', this.currentUser);
                return response;
            }
            throw new Error('Invalid registration response');
        } catch (error) {
            console.error('Registration failed:', error);
            throw error;
        }
    }

    async logout(callApi = true) {
        try {
            if (callApi && api.isAuthenticated()) {
                await api.logout();
            }
        } catch (error) {
            console.error('Logout API call failed:', error);
        } finally {
            // Always clear local state
            this.currentUser = null;
            api.client.clearTokens();
            this.notifyAuthCallbacks('logout');
        }
    }

    async updateProfile(profileData) {
        try {
            const response = await api.updateProfile(profileData);
            if (response.user) {
                this.currentUser = { ...this.currentUser, ...response.user };
                this.notifyAuthCallbacks('profileUpdate', this.currentUser);
            }
            return response;
        } catch (error) {
            console.error('Profile update failed:', error);
            throw error;
        }
    }

    // Authentication state getters
    isAuthenticated() {
        return !!this.currentUser && api.isAuthenticated();
    }

    getCurrentUser() {
        return this.currentUser;
    }

    isAdmin() {
        return this.currentUser && this.currentUser.role === 'admin';
    }

    isCustomer() {
        return this.currentUser && this.currentUser.role === 'customer';
    }

    // Callback management for auth state changes
    onAuthChange(callback) {
        this.authCallbacks.push(callback);
        
        // Return unsubscribe function
        return () => {
            const index = this.authCallbacks.indexOf(callback);
            if (index > -1) {
                this.authCallbacks.splice(index, 1);
            }
        };
    }

    notifyAuthCallbacks(event, data = null) {
        this.authCallbacks.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Auth callback error:', error);
            }
        });
    }

    // Token refresh management
    setupTokenRefresh() {
        // Refresh token every 20 minutes (tokens expire in 24 hours)
        setInterval(async () => {
            if (this.isAuthenticated()) {
                try {
                    await api.refreshTokenIfNeeded();
                } catch (error) {
                    console.error('Token refresh failed:', error);
                    await this.logout(false);
                }
            }
        }, 20 * 60 * 1000); // 20 minutes
    }

    // Handle storage changes for multi-tab support
    async handleStorageChange() {
        const wasAuthenticated = !!this.currentUser;
        const isNowAuthenticated = api.isAuthenticated();
        
        if (wasAuthenticated && !isNowAuthenticated) {
            // User logged out in another tab
            this.currentUser = null;
            this.notifyAuthCallbacks('logout');
        } else if (!wasAuthenticated && isNowAuthenticated) {
            // User logged in in another tab
            await this.checkAuthStatus();
        }
    }

    // Route protection
    requireAuth(redirectUrl = '/account.html') {
        if (!this.isAuthenticated()) {
            Alert.show('Please log in to access this page.', 'warning');
            setTimeout(() => {
                window.location.href = redirectUrl;
            }, 1500);
            return false;
        }
        return true;
    }

    requireAdmin(redirectUrl = '/') {
        if (!this.isAuthenticated()) {
            Alert.show('Please log in to access this page.', 'warning');
            setTimeout(() => {
                window.location.href = '/account.html';
            }, 1500);
            return false;
        }
        
        if (!this.isAdmin()) {
            Alert.show('Access denied. Admin privileges required.', 'error');
            setTimeout(() => {
                window.location.href = redirectUrl;
            }, 1500);
            return false;
        }
        
        return true;
    }

    // UI helpers
    updateAuthUI() {
        // Update login/logout buttons
        const loginBtns = document.querySelectorAll('.login-btn');
        const logoutBtns = document.querySelectorAll('.logout-btn');
        const userInfo = document.querySelectorAll('.user-info');
        const adminLinks = document.querySelectorAll('.admin-only');

        if (this.isAuthenticated()) {
            loginBtns.forEach(btn => btn.style.display = 'none');
            logoutBtns.forEach(btn => btn.style.display = 'block');
            
            userInfo.forEach(info => {
                info.style.display = 'block';
                info.textContent = `Welcome, ${this.currentUser.firstName}!`;
            });

            if (this.isAdmin()) {
                adminLinks.forEach(link => link.style.display = 'block');
            } else {
                adminLinks.forEach(link => link.style.display = 'none');
            }
        } else {
            loginBtns.forEach(btn => btn.style.display = 'block');
            logoutBtns.forEach(btn => btn.style.display = 'none');
            userInfo.forEach(info => info.style.display = 'none');
            adminLinks.forEach(link => link.style.display = 'none');
        }
    }

    // Setup logout handlers
    setupLogoutHandlers() {
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.logout-btn') || e.target.closest('.logout-btn')) {
                e.preventDefault();
                
                try {
                    await this.logout();
                    Alert.show('Logged out successfully', 'success');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } catch (error) {
                    Alert.show('Logout failed', 'error');
                }
            }
        });
    }
}

// Create global auth manager instance
export const authManager = new AuthManager();

// Setup global auth UI updates
authManager.onAuthChange((event, user) => {
    authManager.updateAuthUI();
    
    // Update cart count when user logs in/out
    if (window.cartManager) {
        window.cartManager.updateCartCount();
    }
});

// Setup logout handlers
document.addEventListener('DOMContentLoaded', () => {
    authManager.setupLogoutHandlers();
});
