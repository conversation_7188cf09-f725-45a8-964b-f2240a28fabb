import { api } from '../api.js';
import { Alert } from './Alert.js';
import { formatPrice } from '../config/currency.js';

export class AdminOrderManager {
    constructor() {
        this.orders = [];
        this.selectedOrders = new Set();
        this.filters = {
            status: '',
            dateRange: '',
            search: '',
            minAmount: '',
            maxAmount: ''
        };
        this.sortBy = 'created_at';
        this.sortOrder = 'desc';
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.isLoading = false;
        this.autoRefreshInterval = null;
        
        this.statusWorkflows = {
            'pending': ['confirmed', 'cancelled'],
            'confirmed': ['preparing', 'cancelled'],
            'preparing': ['ready', 'cancelled'],
            'ready': ['delivered'],
            'delivered': [],
            'cancelled': []
        };
        
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadOrders();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // Order selection
        document.addEventListener('change', (e) => {
            if (e.target.matches('.order-checkbox')) {
                this.handleOrderSelection(e.target);
            }
            
            if (e.target.matches('#select-all-orders')) {
                this.handleSelectAll(e.target.checked);
            }
        });

        // Bulk operations
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.bulk-action-btn')) {
                e.preventDefault();
                await this.handleBulkAction(e.target.dataset.action);
            }
        });

        // Individual order actions
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.view-order-btn')) {
                e.preventDefault();
                this.viewOrderDetails(e.target.dataset.orderId);
            }
            
            if (e.target.matches('.update-status-btn')) {
                e.preventDefault();
                await this.updateOrderStatus(e.target.dataset.orderId, e.target.dataset.status);
            }
            
            if (e.target.matches('.print-order-btn')) {
                e.preventDefault();
                this.printOrder(e.target.dataset.orderId);
            }
        });

        // Status change
        document.addEventListener('change', async (e) => {
            if (e.target.matches('.order-status-select')) {
                await this.updateOrderStatus(e.target.dataset.orderId, e.target.value);
            }
        });

        // Filters
        document.addEventListener('change', (e) => {
            if (e.target.matches('.filter-select')) {
                this.updateFilter(e.target.name, e.target.value);
            }
        });

        // Search
        document.addEventListener('input', (e) => {
            if (e.target.matches('#order-search')) {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.updateFilter('search', e.target.value);
                }, 300);
            }
        });

        // Sorting
        document.addEventListener('click', (e) => {
            if (e.target.matches('.sort-btn')) {
                this.updateSort(e.target.dataset.sort);
            }
        });

        // Pagination
        document.addEventListener('click', (e) => {
            if (e.target.matches('.page-btn')) {
                e.preventDefault();
                this.goToPage(parseInt(e.target.dataset.page));
            }
        });

        // Auto-refresh toggle
        document.addEventListener('change', (e) => {
            if (e.target.matches('#auto-refresh-toggle')) {
                if (e.target.checked) {
                    this.startAutoRefresh();
                } else {
                    this.stopAutoRefresh();
                }
            }
        });

        // Export orders
        document.addEventListener('click', (e) => {
            if (e.target.matches('#export-orders-btn')) {
                e.preventDefault();
                this.exportOrders();
            }
        });
    }

    async loadOrders() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading(true);

        try {
            const params = {
                page: this.currentPage,
                limit: this.itemsPerPage,
                sortBy: this.sortBy,
                sortOrder: this.sortOrder,
                ...this.filters
            };

            // Remove empty filters
            Object.keys(params).forEach(key => {
                if (params[key] === '') delete params[key];
            });

            const response = await api.getAdminOrders(params);
            this.orders = response.orders || [];
            this.pagination = response.pagination || {};

            this.renderOrders();
            this.renderPagination();
            this.updateOrderStats();

        } catch (error) {
            console.error('Failed to load orders:', error);
            Alert.show('Failed to load orders', 'error');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    renderOrders() {
        const container = document.querySelector('#orders-table-body');
        if (!container) return;

        if (this.orders.length === 0) {
            container.innerHTML = `
                <tr>
                    <td colspan="8" class="empty-state">
                        <div class="empty-content">
                            <i class="fas fa-receipt"></i>
                            <h3>No orders found</h3>
                            <p>Try adjusting your filters or check back later.</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        container.innerHTML = this.orders.map(order => this.renderOrderRow(order)).join('');
        this.updateBulkActionsVisibility();
    }

    renderOrderRow(order) {
        const isSelected = this.selectedOrders.has(order.id);
        const statusClass = `status-${order.status}`;
        const nextStatuses = this.statusWorkflows[order.status] || [];
        
        return `
            <tr class="order-row ${isSelected ? 'selected' : ''} ${statusClass}">
                <td>
                    <input type="checkbox" class="order-checkbox" 
                           value="${order.id}" ${isSelected ? 'checked' : ''}>
                </td>
                <td>
                    <div class="order-number">
                        <strong>#${order.orderNumber}</strong>
                        <small class="order-date">${new Date(order.createdAt).toLocaleDateString()}</small>
                    </div>
                </td>
                <td>
                    <div class="customer-info">
                        <strong>${order.customerName}</strong>
                        <small>${order.customerEmail}</small>
                        ${order.customerPhone ? `<small>${order.customerPhone}</small>` : ''}
                    </div>
                </td>
                <td>
                    <div class="order-items">
                        <span class="item-count">${order.itemCount || 0} items</span>
                        <small class="total-quantity">${order.totalQuantity || 0} qty</small>
                    </div>
                </td>
                <td>
                    <span class="order-amount">${formatPrice(order.totalAmount)}</span>
                </td>
                <td>
                    <select class="order-status-select status-${order.status}" 
                            data-order-id="${order.id}" data-current-status="${order.status}">
                        <option value="${order.status}" selected>${this.formatStatus(order.status)}</option>
                        ${nextStatuses.map(status => 
                            `<option value="${status}">${this.formatStatus(status)}</option>`
                        ).join('')}
                    </select>
                </td>
                <td>
                    <span class="time-ago" title="${new Date(order.createdAt).toLocaleString()}">
                        ${this.getTimeAgo(order.createdAt)}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm view-order-btn" data-order-id="${order.id}" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm print-order-btn" data-order-id="${order.id}" title="Print">
                            <i class="fas fa-print"></i>
                        </button>
                        ${order.status === 'pending' ? `
                            <button class="btn btn-sm btn-success update-status-btn" 
                                    data-order-id="${order.id}" data-status="confirmed" title="Confirm">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : ''}
                        ${order.status !== 'cancelled' && order.status !== 'delivered' ? `
                            <button class="btn btn-sm btn-danger update-status-btn" 
                                    data-order-id="${order.id}" data-status="cancelled" title="Cancel">
                                <i class="fas fa-times"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }

    renderPagination() {
        const container = document.querySelector('.orders-pagination');
        if (!container || !this.pagination) return;

        const { page, pages, total, hasNext, hasPrev } = this.pagination;
        
        container.innerHTML = `
            <div class="pagination-info">
                Showing ${((page - 1) * this.itemsPerPage) + 1} to ${Math.min(page * this.itemsPerPage, total)} of ${total} orders
            </div>
            <div class="pagination-controls">
                <button class="btn btn-sm page-btn" data-page="1" ${!hasPrev ? 'disabled' : ''}>
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button class="btn btn-sm page-btn" data-page="${page - 1}" ${!hasPrev ? 'disabled' : ''}>
                    <i class="fas fa-angle-left"></i>
                </button>
                <span class="page-info">Page ${page} of ${pages}</span>
                <button class="btn btn-sm page-btn" data-page="${page + 1}" ${!hasNext ? 'disabled' : ''}>
                    <i class="fas fa-angle-right"></i>
                </button>
                <button class="btn btn-sm page-btn" data-page="${pages}" ${!hasNext ? 'disabled' : ''}>
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </div>
        `;
    }

    updateOrderStats() {
        const stats = this.calculateOrderStats();
        
        // Update stat cards
        this.updateStatCard('total-orders', stats.total);
        this.updateStatCard('pending-orders', stats.pending);
        this.updateStatCard('confirmed-orders', stats.confirmed);
        this.updateStatCard('preparing-orders', stats.preparing);
        this.updateStatCard('ready-orders', stats.ready);
        this.updateStatCard('delivered-orders', stats.delivered);
        this.updateStatCard('cancelled-orders', stats.cancelled);
        this.updateStatCard('total-revenue', formatPrice(stats.totalRevenue));
        this.updateStatCard('average-order', formatPrice(stats.averageOrder));
    }

    calculateOrderStats() {
        const stats = {
            total: this.orders.length,
            pending: 0,
            confirmed: 0,
            preparing: 0,
            ready: 0,
            delivered: 0,
            cancelled: 0,
            totalRevenue: 0
        };

        this.orders.forEach(order => {
            stats[order.status] = (stats[order.status] || 0) + 1;
            stats.totalRevenue += parseFloat(order.totalAmount) || 0;
        });

        stats.averageOrder = stats.total > 0 ? stats.totalRevenue / stats.total : 0;

        return stats;
    }

    updateStatCard(id, value) {
        const card = document.querySelector(`[data-stat="${id}"] .stat-value`);
        if (card) {
            card.textContent = value;
        }
    }

    async updateOrderStatus(orderId, newStatus) {
        try {
            await api.updateOrderStatus(orderId, newStatus);
            Alert.show(`Order status updated to ${this.formatStatus(newStatus)}`, 'success');
            
            // Update local order data
            const order = this.orders.find(o => o.id === orderId);
            if (order) {
                order.status = newStatus;
                order.updatedAt = new Date().toISOString();
            }
            
            this.renderOrders();
            this.updateOrderStats();
            
        } catch (error) {
            console.error('Failed to update order status:', error);
            Alert.show('Failed to update order status', 'error');
            
            // Revert the select value
            const select = document.querySelector(`[data-order-id="${orderId}"]`);
            if (select) {
                const order = this.orders.find(o => o.id === orderId);
                if (order) {
                    select.value = order.status;
                }
            }
        }
    }

    async handleBulkAction(action) {
        if (this.selectedOrders.size === 0) {
            Alert.show('No orders selected', 'warning');
            return;
        }

        const orderIds = Array.from(this.selectedOrders);
        
        try {
            switch (action) {
                case 'confirm':
                    await this.bulkUpdateStatus(orderIds, 'confirmed');
                    Alert.show(`${orderIds.length} orders confirmed`, 'success');
                    break;
                    
                case 'prepare':
                    await this.bulkUpdateStatus(orderIds, 'preparing');
                    Alert.show(`${orderIds.length} orders set to preparing`, 'success');
                    break;
                    
                case 'ready':
                    await this.bulkUpdateStatus(orderIds, 'ready');
                    Alert.show(`${orderIds.length} orders marked as ready`, 'success');
                    break;
                    
                case 'deliver':
                    await this.bulkUpdateStatus(orderIds, 'delivered');
                    Alert.show(`${orderIds.length} orders marked as delivered`, 'success');
                    break;
                    
                case 'cancel':
                    if (confirm(`Are you sure you want to cancel ${orderIds.length} orders?`)) {
                        await this.bulkUpdateStatus(orderIds, 'cancelled');
                        Alert.show(`${orderIds.length} orders cancelled`, 'success');
                    } else {
                        return;
                    }
                    break;
                    
                case 'export':
                    this.exportSelectedOrders(orderIds);
                    return;
                    
                case 'print':
                    this.printSelectedOrders(orderIds);
                    return;
            }
            
            // Clear selection and reload
            this.selectedOrders.clear();
            await this.loadOrders();
            
        } catch (error) {
            console.error('Bulk action failed:', error);
            Alert.show(`Failed to ${action} orders`, 'error');
        }
    }

    async bulkUpdateStatus(orderIds, status) {
        // Update orders one by one (could be optimized with a bulk endpoint)
        const promises = orderIds.map(orderId => api.updateOrderStatus(orderId, status));
        await Promise.all(promises);
    }

    viewOrderDetails(orderId) {
        const order = this.orders.find(o => o.id === orderId);
        if (!order) return;

        // Create and show order details modal
        this.showOrderDetailsModal(order);
    }

    showOrderDetailsModal(order) {
        const modal = document.createElement('div');
        modal.className = 'modal order-details-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Order #${order.orderNumber}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="order-details-content">
                        <div class="order-info-section">
                            <h4>Order Information</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>Status:</label>
                                    <span class="status-badge status-${order.status}">${this.formatStatus(order.status)}</span>
                                </div>
                                <div class="info-item">
                                    <label>Order Date:</label>
                                    <span>${new Date(order.createdAt).toLocaleString()}</span>
                                </div>
                                <div class="info-item">
                                    <label>Total Amount:</label>
                                    <span class="amount">${formatPrice(order.totalAmount)}</span>
                                </div>
                                <div class="info-item">
                                    <label>Payment Method:</label>
                                    <span>${order.paymentMethod || 'Cash'}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="customer-section">
                            <h4>Customer Information</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>Name:</label>
                                    <span>${order.customerName}</span>
                                </div>
                                <div class="info-item">
                                    <label>Email:</label>
                                    <span>${order.customerEmail}</span>
                                </div>
                                <div class="info-item">
                                    <label>Phone:</label>
                                    <span>${order.customerPhone || 'N/A'}</span>
                                </div>
                                <div class="info-item">
                                    <label>Address:</label>
                                    <span>${order.deliveryAddress || 'N/A'}</span>
                                </div>
                            </div>
                        </div>
                        
                        ${order.items ? `
                            <div class="items-section">
                                <h4>Order Items</h4>
                                <div class="items-list">
                                    ${order.items.map(item => `
                                        <div class="order-item">
                                            <div class="item-info">
                                                <span class="item-name">${item.name}</span>
                                                <span class="item-quantity">x${item.quantity}</span>
                                            </div>
                                            <span class="item-price">${formatPrice(item.price)}</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                        
                        ${order.specialInstructions ? `
                            <div class="instructions-section">
                                <h4>Special Instructions</h4>
                                <p>${order.specialInstructions}</p>
                            </div>
                        ` : ''}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary modal-close">Close</button>
                    <button class="btn btn-primary print-order-btn" data-order-id="${order.id}">Print Order</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.classList.add('active');

        // Close modal handlers
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => {
                modal.remove();
            });
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    handleOrderSelection(checkbox) {
        const orderId = checkbox.value;
        
        if (checkbox.checked) {
            this.selectedOrders.add(orderId);
        } else {
            this.selectedOrders.delete(orderId);
        }
        
        this.updateBulkActionsVisibility();
        this.updateSelectAllState();
    }

    handleSelectAll(checked) {
        if (checked) {
            this.orders.forEach(order => this.selectedOrders.add(order.id));
        } else {
            this.selectedOrders.clear();
        }
        
        // Update checkboxes
        document.querySelectorAll('.order-checkbox').forEach(checkbox => {
            checkbox.checked = checked;
        });
        
        this.updateBulkActionsVisibility();
    }

    updateBulkActionsVisibility() {
        const bulkActions = document.querySelector('.bulk-actions');
        const selectedCount = document.querySelector('.selected-count');
        
        if (bulkActions) {
            bulkActions.style.display = this.selectedOrders.size > 0 ? 'flex' : 'none';
        }
        
        if (selectedCount) {
            selectedCount.textContent = `${this.selectedOrders.size} orders selected`;
        }
    }

    updateSelectAllState() {
        const selectAllCheckbox = document.querySelector('#select-all-orders');
        const selectedCount = this.selectedOrders.size;
        const totalCount = this.orders.length;
        
        if (selectAllCheckbox) {
            if (selectedCount === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (selectedCount === totalCount) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }
    }

    updateFilter(filterName, value) {
        this.filters[filterName] = value;
        this.currentPage = 1; // Reset to first page
        this.loadOrders();
    }

    updateSort(sortBy) {
        if (this.sortBy === sortBy) {
            this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortBy = sortBy;
            this.sortOrder = 'desc';
        }
        
        this.loadOrders();
    }

    goToPage(page) {
        this.currentPage = page;
        this.loadOrders();
    }

    startAutoRefresh() {
        this.stopAutoRefresh();
        this.autoRefreshInterval = setInterval(() => {
            this.loadOrders();
        }, 30000); // Refresh every 30 seconds
    }

    stopAutoRefresh() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }
    }

    formatStatus(status) {
        const statusMap = {
            'pending': 'Pending',
            'confirmed': 'Confirmed',
            'preparing': 'Preparing',
            'ready': 'Ready',
            'delivered': 'Delivered',
            'cancelled': 'Cancelled'
        };
        
        return statusMap[status] || status;
    }

    getTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'Just now';
        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
        
        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `${diffInHours}h ago`;
        
        const diffInDays = Math.floor(diffInHours / 24);
        return `${diffInDays}d ago`;
    }

    printOrder(orderId) {
        const order = this.orders.find(o => o.id === orderId);
        if (!order) return;

        // Create print window with order details
        const printWindow = window.open('', '_blank');
        printWindow.document.write(this.generatePrintHTML(order));
        printWindow.document.close();
        printWindow.print();
    }

    generatePrintHTML(order) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Order #${order.orderNumber}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .order-info { margin-bottom: 20px; }
                    .items-table { width: 100%; border-collapse: collapse; }
                    .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    .total { font-weight: bold; font-size: 1.2em; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Magic Menu</h1>
                    <h2>Order #${order.orderNumber}</h2>
                </div>
                
                <div class="order-info">
                    <p><strong>Date:</strong> ${new Date(order.createdAt).toLocaleString()}</p>
                    <p><strong>Status:</strong> ${this.formatStatus(order.status)}</p>
                    <p><strong>Customer:</strong> ${order.customerName}</p>
                    <p><strong>Email:</strong> ${order.customerEmail}</p>
                    ${order.customerPhone ? `<p><strong>Phone:</strong> ${order.customerPhone}</p>` : ''}
                    ${order.deliveryAddress ? `<p><strong>Address:</strong> ${order.deliveryAddress}</p>` : ''}
                </div>
                
                ${order.items ? `
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Quantity</th>
                                <th>Price</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${order.items.map(item => `
                                <tr>
                                    <td>${item.name}</td>
                                    <td>${item.quantity}</td>
                                    <td>${formatPrice(item.price)}</td>
                                    <td>${formatPrice(item.price * item.quantity)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                ` : ''}
                
                <div class="total">
                    <p>Total: ${formatPrice(order.totalAmount)}</p>
                </div>
                
                ${order.specialInstructions ? `
                    <div class="instructions">
                        <h3>Special Instructions:</h3>
                        <p>${order.specialInstructions}</p>
                    </div>
                ` : ''}
            </body>
            </html>
        `;
    }

    exportOrders() {
        // Export current filtered orders to CSV
        const csvContent = this.generateCSV(this.orders);
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `orders-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    generateCSV(orders) {
        const headers = ['Order Number', 'Customer Name', 'Email', 'Phone', 'Status', 'Total Amount', 'Order Date'];
        const rows = orders.map(order => [
            order.orderNumber,
            order.customerName,
            order.customerEmail,
            order.customerPhone || '',
            this.formatStatus(order.status),
            order.totalAmount,
            new Date(order.createdAt).toLocaleString()
        ]);
        
        return [headers, ...rows].map(row => 
            row.map(field => `"${field}"`).join(',')
        ).join('\n');
    }

    showLoading(show) {
        const loader = document.querySelector('.orders-loader');
        if (loader) {
            loader.style.display = show ? 'block' : 'none';
        }
    }

    destroy() {
        this.stopAutoRefresh();
    }
}

// Create global admin order manager instance
export const adminOrderManager = new AdminOrderManager();
