/**
 * Modern Contact Page Styles
 * Aligned with the modern design system and functionality standards
 */

/* CSS Custom Properties for Contact Page */
:root {
    --contact-hero-height: 60vh;
    --contact-card-radius: var(--radius-xl, 12px);
    --contact-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
    --contact-shadow-hover: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
    --contact-transition: var(--transition-default, all 0.3s ease);
    --contact-animation: var(--transition-slow, all 0.5s ease);
    --form-border-radius: var(--radius-lg, 8px);
    --input-height: 56px;
    --input-padding: var(--spacing-4, 1rem);
    --form-gap: var(--spacing-6, 1.5rem);
}

/* Screen Reader Only Content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Hero Section */
.contact-hero {
    position: relative;
    min-height: var(--contact-hero-height);
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-alpha, rgba(255, 122, 0, 0.1)), var(--secondary-alpha, rgba(44, 62, 80, 0.1)));
    overflow: hidden;
    margin-top: 65px; /* Account for fixed header */
    width: 100%;
    text-align: center;
}

/* Ensure container within hero is centered */
.contact-hero .container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 122, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 122, 0, 0.05) 0%, transparent 50%);
    background-size: 100px 100px;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(1deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    width: 100%;
    margin: 0 auto;
    padding: var(--spacing-8, 2rem);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.hero-title {
    font-size: var(--font-size-5xl, 3rem);
    font-weight: var(--font-weight-bold, 700);
    color: var(--text-color, #333);
    margin-bottom: var(--spacing-4, 1rem);
    font-family: var(--font-family-secondary, 'Inter', sans-serif);
    line-height: var(--line-height-tight, 1.1);
    text-align: center;
    width: 100%;
}

.hero-subtitle {
    font-size: var(--font-size-xl, 1.25rem);
    color: var(--text-light, #666);
    margin-bottom: var(--spacing-8, 2rem);
    line-height: var(--line-height-relaxed, 1.6);
    font-weight: var(--font-weight-medium, 500);
    text-align: center;
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-8, 2rem);
    flex-wrap: wrap;
    width: 100%;
    text-align: center;
    margin-top: var(--spacing-4, 1rem);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-4, 1rem);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg, 8px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    font-family: var(--font-family-secondary);
}

.stat-label {
    display: block;
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-top: var(--spacing-1);
    font-weight: var(--font-weight-medium);
}

/* Main Content */
.main-content {
    padding: var(--spacing-20, 5rem) 0;
    background: var(--background-color, #f8f9fa);
    min-height: calc(100vh - 65px); /* Ensure full height minus header */
}

/* Section Titles */
.section-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    text-align: center;
    margin-bottom: var(--spacing-12);
    font-family: var(--font-family-secondary);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -var(--spacing-4);
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 2px;
}

/* Contact Methods Section */
.contact-methods {
    margin-bottom: var(--spacing-20);
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-8);
    margin-top: var(--spacing-12);
}

.contact-card {
    background: var(--white);
    border-radius: var(--contact-card-radius);
    padding: var(--spacing-8);
    box-shadow: var(--contact-shadow);
    transition: var(--contact-transition);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transform: scaleX(0);
    transition: var(--contact-transition);
    transform-origin: left;
}

.contact-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--contact-shadow-hover);
    border-color: var(--primary-light);
}

.contact-card:hover::before {
    transform: scaleX(1);
}

.card-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-6);
    box-shadow: var(--shadow-sm);
    transition: var(--contact-transition);
}

.contact-card:hover .card-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-md);
}

.card-icon i {
    font-size: var(--font-size-xl);
    color: var(--white);
}

.card-content {
    flex: 1;
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: var(--spacing-3);
    font-family: var(--font-family-secondary);
}

.card-description {
    color: var(--text-light);
    margin-bottom: var(--spacing-4);
    line-height: var(--line-height-relaxed);
    font-size: var(--font-size-sm);
}

.card-address {
    color: var(--text-light);
    font-style: normal;
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-sm);
}

.card-action {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    transition: var(--contact-transition);
    padding: var(--spacing-2) 0;
}

.card-action:hover {
    color: var(--primary-dark);
    transform: translateX(4px);
}

.card-action i {
    font-size: var(--font-size-xs);
}

/* Hours List */
.hours-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2) 0;
    border-bottom: 1px solid var(--border-light);
}

.hours-item:last-child {
    border-bottom: none;
}

.hours-day {
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    font-size: var(--font-size-sm);
}

.hours-time {
    color: var(--text-light);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

/* Contact Form Section */
.contact-form-section {
    background: var(--background-light, #f8f9fa);
    padding: var(--spacing-16, 4rem) 0;
    border-radius: var(--radius-2xl, 16px);
    margin: var(--spacing-16, 4rem) 0;
}

.form-container {
    max-width: 800px;
    margin: 0 auto;
    background: var(--white, #ffffff);
    border-radius: var(--form-border-radius);
    box-shadow: var(--contact-shadow);
    overflow: hidden;
    border: 1px solid var(--border-color, #ddd);
    transition: var(--contact-transition);
}

.form-container:hover {
    box-shadow: var(--contact-shadow-hover);
    transform: translateY(-2px);
}

.form-header {
    padding: var(--spacing-8) var(--spacing-8) var(--spacing-6);
    background: linear-gradient(135deg, var(--primary-alpha), var(--secondary-alpha));
    text-align: center;
    position: relative;
}

.form-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    margin-bottom: var(--spacing-3);
    font-family: var(--font-family-secondary);
}

.form-description {
    color: var(--text-light);
    line-height: var(--line-height-relaxed);
    font-size: var(--font-size-base);
    max-width: 600px;
    margin: 0 auto;
}

/* Form Progress */
.form-progress {
    height: 4px;
    background: var(--background-light);
    position: relative;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    width: 0%;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Form Styling */
.contact-form {
    padding: var(--spacing-8);
}

.form-fieldset {
    border: none;
    margin: 0;
    padding: 0;
    margin-bottom: var(--spacing-8);
}

.fieldset-legend {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: var(--spacing-6);
    font-family: var(--font-family-secondary);
    padding: 0;
    border: none;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--form-gap);
}

@media (min-width: 640px) {
    .form-row {
        grid-template-columns: 1fr 1fr;
    }
}

.form-group {
    margin-bottom: var(--form-gap);
}

.form-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: var(--spacing-2);
}

.label-text {
    flex: 1;
}

.label-required {
    color: var(--error-color);
    font-weight: var(--font-weight-bold);
}

.label-optional {
    color: var(--text-light);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-normal);
    font-style: italic;
}

/* Input Wrapper */
.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: var(--input-padding);
    color: var(--text-light);
    font-size: var(--font-size-sm);
    z-index: 2;
    transition: var(--contact-transition);
    pointer-events: none;
}

/* Form Inputs */
.form-input {
    width: 100%;
    min-height: var(--input-height);
    padding: var(--input-padding) var(--input-padding) var(--input-padding) calc(var(--input-padding) * 2.5);
    border: 2px solid var(--border-color);
    border-radius: var(--form-border-radius);
    font-size: var(--font-size-base);
    font-family: var(--font-family-primary);
    color: var(--text-color);
    background: var(--white);
    transition: var(--contact-transition);
    resize: vertical;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-alpha);
}

.form-input:focus + .input-icon,
.form-input:not(:placeholder-shown) + .input-icon {
    color: var(--primary-color);
    transform: scale(1.1);
}

.form-input::placeholder {
    color: var(--text-light);
    opacity: 0.7;
}

/* Select Styling */
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--input-padding) center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: calc(var(--input-padding) * 2.5);
    appearance: none;
    cursor: pointer;
}

/* Textarea Styling */
.form-textarea {
    min-height: 120px;
    resize: vertical;
    font-family: var(--font-family-primary);
    line-height: var(--line-height-relaxed);
}

/* Input Help Text */
.input-help {
    font-size: var(--font-size-xs);
    color: var(--text-light);
    margin-top: var(--spacing-1);
    line-height: var(--line-height-snug);
}

/* Character Count */
.character-count {
    display: flex;
    justify-content: flex-end;
    font-size: var(--font-size-xs);
    color: var(--text-light);
    margin-top: var(--spacing-1);
}

.count-current {
    font-weight: var(--font-weight-semibold);
}

.character-count.warning .count-current {
    color: var(--warning-color);
}

.character-count.error .count-current {
    color: var(--error-color);
}

/* Error Messages */
.error-message {
    color: var(--error-color);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    margin-top: var(--spacing-2);
    display: none;
    animation: slideInDown 0.3s ease-out;
}

.error-message.show {
    display: block;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Validation States */
.form-group.error .form-input {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px var(--error-light);
}

.form-group.error .input-icon {
    color: var(--error-color);
}

.form-group.success .form-input {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px var(--success-light);
}

.form-group.success .input-icon {
    color: var(--success-color);
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: var(--spacing-4);
    justify-content: flex-end;
    align-items: center;
    margin-top: var(--spacing-8);
    padding-top: var(--spacing-6);
    border-top: 1px solid var(--border-light);
}

@media (max-width: 640px) {
    .form-actions {
        flex-direction: column-reverse;
        align-items: stretch;
    }
}

/* Button Styling */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2, 0.5rem);
    padding: var(--spacing-3, 0.75rem) var(--spacing-6, 1.5rem);
    border-radius: var(--form-border-radius);
    font-size: var(--font-size-sm, 0.875rem);
    font-weight: var(--font-weight-semibold, 600);
    text-decoration: none;
    transition: var(--contact-transition);
    cursor: pointer;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    min-height: 48px;
    font-family: var(--font-family-primary, 'Inter', sans-serif);
    white-space: nowrap;
}

.btn-large {
    padding: var(--spacing-4, 1rem) var(--spacing-8, 2rem);
    font-size: var(--font-size-base, 1rem);
    min-height: 56px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color, #ff7a00), var(--primary-dark, #e66d00));
    color: var(--white, #ffffff);
    box-shadow: var(--shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.1));
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-dark, #e66d00), var(--primary-color, #ff7a00));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.1));
}

.btn-primary:focus {
    outline: 2px solid var(--primary-color, #ff7a00);
    outline-offset: 2px;
}

.btn-secondary {
    background: var(--white, #ffffff);
    color: var(--text-color, #333);
    border-color: var(--border-color, #ddd);
}

.btn-secondary:hover {
    background: var(--background-light, #f8f9fa);
    border-color: var(--primary-color, #ff7a00);
    color: var(--primary-color, #ff7a00);
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color, #ff7a00);
    border-color: var(--primary-color, #ff7a00);
}

.btn-outline:hover {
    background: var(--primary-color, #ff7a00);
    color: var(--white, #ffffff);
    transform: translateY(-1px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: var(--shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.1)) !important;
}

/* Button Loading State */
.btn.loading .btn-text {
    opacity: 0.7;
}

.btn.loading .btn-icon {
    display: none;
}

.btn-loading {
    display: none;
}

.btn.loading .btn-loading {
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--white);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Form Status */
.form-status {
    margin-top: var(--spacing-4);
    padding: var(--spacing-3);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    text-align: center;
    display: none;
}

.form-status.success {
    background: var(--success-light);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.form-status.error {
    background: var(--error-light);
    color: var(--error-color);
    border: 1px solid var(--error-color);
}

.form-status.show {
    display: block;
    animation: slideInDown 0.3s ease-out;
}

/* Map Section */
.map-section {
    margin-top: var(--spacing-20);
}

.map-container {
    max-width: 1200px;
    margin: 0 auto;
}

.map-header {
    text-align: center;
    margin-bottom: var(--spacing-8);
}

.map-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    margin-bottom: var(--spacing-3);
    font-family: var(--font-family-secondary);
}

.map-description {
    color: var(--text-light);
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    max-width: 600px;
    margin: 0 auto;
}

.map-wrapper {
    border-radius: var(--contact-card-radius);
    overflow: hidden;
    box-shadow: var(--contact-shadow);
    background: var(--white);
    position: relative;
    height: 450px;
    margin-bottom: var(--spacing-6);
}

.map-wrapper iframe {
    width: 100%;
    height: 100%;
    border: none;
    display: block;
}

/* Map Placeholder */
.map-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: var(--background-light);
    color: var(--text-light);
    text-align: center;
    padding: var(--spacing-8);
}

.placeholder-content {
    max-width: 400px;
}

.placeholder-content i {
    font-size: var(--font-size-4xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-4);
}

.placeholder-content h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: var(--spacing-3);
    font-family: var(--font-family-secondary);
}

.placeholder-content address {
    font-style: normal;
    color: var(--text-light);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-4);
}

.map-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    color: var(--text-light);
    font-size: var(--font-size-sm);
    font-style: italic;
}

.map-loading .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
}

/* Map Error */
.map-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: var(--error-light);
    color: var(--error-color);
    text-align: center;
    padding: var(--spacing-8);
}

.map-error i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-4);
}

.map-error h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-3);
    color: var(--error-color);
}

.map-error p {
    margin-bottom: var(--spacing-4);
    line-height: var(--line-height-relaxed);
}

/* Map Actions */
.map-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-4);
    right: var(--spacing-4);
    z-index: var(--z-tooltip);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    pointer-events: none;
    max-width: 400px;
}

.toast {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-4) var(--spacing-6);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    min-width: 320px;
    transform: translateX(100%);
    opacity: 0;
    transition: var(--contact-animation);
    pointer-events: auto;
    border-left: 4px solid var(--primary-color);
    position: relative;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--error-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

.toast.info {
    border-left-color: var(--info-color);
}

.toast-icon {
    font-size: var(--font-size-lg);
    flex-shrink: 0;
    margin-top: var(--spacing-1);
}

.toast.success .toast-icon {
    color: var(--success-color);
}

.toast.error .toast-icon {
    color: var(--error-color);
}

.toast.warning .toast-icon {
    color: var(--warning-color);
}

.toast.info .toast-icon {
    color: var(--info-color);
}

.toast-content {
    flex: 1;
    min-width: 0;
}

.toast-title {
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    color: var(--text-color);
    margin-bottom: var(--spacing-1);
    line-height: var(--line-height-snug);
}

.toast-message {
    font-size: var(--font-size-xs);
    color: var(--text-light);
    line-height: var(--line-height-snug);
    margin: 0;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: var(--spacing-1);
    border-radius: var(--radius-sm);
    transition: var(--contact-transition);
    flex-shrink: 0;
    margin-top: var(--spacing-1);
}

.toast-close:hover {
    background: var(--background-light);
    color: var(--text-color);
}

.toast-close:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .contact-hero {
        min-height: 50vh;
    }

    .hero-title {
        font-size: var(--font-size-4xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-stats {
        gap: var(--spacing-6);
    }

    .main-content {
        padding: var(--spacing-16) 0;
    }

    .contact-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--spacing-6);
    }

    .form-container {
        margin: 0 var(--spacing-4);
    }
}

@media (max-width: 768px) {
    .contact-hero {
        min-height: 40vh;
        text-align: center;
    }

    .contact-hero .container {
        padding: 0 var(--spacing-4, 1rem);
    }

    .hero-content {
        padding: var(--spacing-6, 1.5rem);
        text-align: center;
        width: 100%;
        max-width: 100%;
    }

    .hero-title {
        font-size: var(--font-size-3xl, 2rem);
        text-align: center;
        margin-bottom: var(--spacing-3, 0.75rem);
    }

    .hero-subtitle {
        font-size: var(--font-size-base, 1rem);
        text-align: center;
        margin-bottom: var(--spacing-6, 1.5rem);
        max-width: 100%;
    }

    .hero-stats {
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-4, 1rem);
        width: 100%;
        text-align: center;
    }

    .stat-item {
        min-width: 200px;
        width: 100%;
        max-width: 280px;
        margin: 0 auto;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .contact-card {
        padding: var(--spacing-6);
    }

    .form-header {
        padding: var(--spacing-6);
    }

    .contact-form {
        padding: var(--spacing-6);
    }

    .map-wrapper {
        height: 350px;
    }

    .map-actions {
        flex-direction: column;
        align-items: center;
    }

    .toast-container {
        top: var(--spacing-2);
        right: var(--spacing-2);
        left: var(--spacing-2);
        max-width: none;
    }

    .toast {
        min-width: auto;
    }
}

@media (max-width: 640px) {
    .hero-title {
        font-size: var(--font-size-2xl);
    }

    .section-title {
        font-size: var(--font-size-2xl);
    }

    .contact-card {
        padding: var(--spacing-4);
    }

    .card-icon {
        width: 56px;
        height: 56px;
    }

    .card-title {
        font-size: var(--font-size-lg);
    }

    .form-header {
        padding: var(--spacing-4);
    }

    .form-title {
        font-size: var(--font-size-xl);
    }

    .contact-form {
        padding: var(--spacing-4);
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .map-wrapper {
        height: 300px;
    }

    .map-header {
        margin-bottom: var(--spacing-6);
    }

    .map-title {
        font-size: var(--font-size-xl);
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: var(--spacing-12, 3rem) 0;
    }

    .contact-hero {
        min-height: 50vh;
        text-align: center;
    }

    .hero-content {
        padding: var(--spacing-4, 1rem);
        text-align: center;
        width: 100%;
    }

    .hero-title {
        font-size: var(--font-size-2xl, 1.5rem);
        text-align: center;
        margin-bottom: var(--spacing-3, 0.75rem);
    }

    .hero-subtitle {
        font-size: var(--font-size-sm, 0.875rem);
        text-align: center;
        margin-bottom: var(--spacing-6, 1.5rem);
    }

    .hero-stats {
        gap: var(--spacing-3, 0.75rem);
    }

    .stat-item {
        min-width: 150px;
        padding: var(--spacing-3, 0.75rem);
    }

    .contact-methods {
        margin-bottom: var(--spacing-12, 3rem);
    }

    .contact-form-section {
        margin: var(--spacing-12, 3rem) 0;
        padding: var(--spacing-12, 3rem) 0;
    }

    .map-section {
        margin-top: var(--spacing-12, 3rem);
    }

    .form-container {
        margin: 0 var(--spacing-2, 0.5rem);
        border-radius: var(--radius-lg, 8px);
    }

    .btn {
        padding: var(--spacing-3, 0.75rem) var(--spacing-4, 1rem);
        font-size: var(--font-size-sm, 0.875rem);
        min-height: 44px; /* Improved touch target */
    }

    .btn-large {
        padding: var(--spacing-4, 1rem) var(--spacing-6, 1.5rem);
        min-height: 48px; /* Improved touch target */
    }

    /* Enhanced mobile form experience */
    .form-input {
        min-height: 48px; /* Better touch targets */
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .form-textarea {
        min-height: 120px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    /* Mobile-optimized contact cards */
    .contact-card {
        text-align: center;
        padding: var(--spacing-6, 1.5rem);
    }

    .card-icon {
        margin: 0 auto var(--spacing-4, 1rem);
    }

    /* Mobile toast positioning */
    .toast-container {
        top: var(--spacing-2, 0.5rem);
        right: var(--spacing-2, 0.5rem);
        left: var(--spacing-2, 0.5rem);
        max-width: none;
    }

    .toast {
        min-width: auto;
        margin-bottom: var(--spacing-2, 0.5rem);
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .contact-card,
    .form-container,
    .map-wrapper,
    .toast {
        border: 2px solid var(--text-color);
    }

    .form-input {
        border-width: 2px;
    }

    .btn {
        border-width: 2px;
    }

    .hero-pattern {
        display: none;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .hero-pattern,
    .contact-card,
    .form-input,
    .btn,
    .toast,
    .card-icon,
    .progress-bar::after {
        animation: none;
        transition: none;
    }

    .contact-card:hover,
    .btn:hover,
    .card-action:hover {
        transform: none;
    }

    .loading-spinner {
        animation: none;
        border: 2px solid var(--primary-color);
    }
}

/* Print Styles */
@media print {
    .contact-hero {
        background: none;
        min-height: auto;
        padding: var(--spacing-4) 0;
    }

    .hero-pattern,
    .hero-stats {
        display: none;
    }

    .main-content {
        padding: var(--spacing-4) 0;
    }

    .contact-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-4);
    }

    .contact-card,
    .form-container,
    .map-wrapper {
        box-shadow: none;
        border: 1px solid var(--border-color);
        break-inside: avoid;
    }

    .form-actions,
    .map-actions,
    .toast-container,
    .btn-loading,
    .loading-spinner {
        display: none;
    }

    .form-input {
        border: 1px solid var(--border-color);
        background: transparent;
    }

    .error-message,
    .input-help {
        display: none;
    }

    .map-section {
        page-break-before: always;
    }
}

/* Enhanced Focus Indicators */
.form-input:focus,
.btn:focus,
.card-action:focus,
.toast-close:focus,
.nav-link:focus,
.logo:focus {
    outline: 2px solid var(--primary-color, #ff7a00);
    outline-offset: 2px;
    border-radius: var(--radius-sm, 4px);
}

/* High contrast focus for better visibility */
@media (prefers-contrast: high) {
    .form-input:focus,
    .btn:focus,
    .card-action:focus,
    .toast-close:focus {
        outline: 3px solid var(--text-color, #333);
        outline-offset: 2px;
        background: var(--white, #ffffff);
    }
}

/* Focus within for form groups */
.form-group:focus-within .form-label {
    color: var(--primary-color, #ff7a00);
    font-weight: var(--font-weight-semibold, 600);
}

/* Skip link styling */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color, #ff7a00);
    color: var(--white, #ffffff);
    padding: var(--spacing-2, 0.5rem) var(--spacing-3, 0.75rem);
    text-decoration: none;
    border-radius: var(--radius-sm, 4px);
    z-index: 9999;
    transition: top 0.3s ease;
    font-weight: var(--font-weight-semibold, 600);
}

.skip-link:focus {
    top: 6px;
    outline: 2px solid var(--white, #ffffff);
    outline-offset: 2px;
}

/* Keyboard navigation indicators */
.contact-card:focus {
    outline: 2px solid var(--primary-color, #ff7a00);
    outline-offset: 2px;
    border-radius: var(--contact-card-radius);
}

/* Screen reader improvements */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

.bounce-in {
    animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Interactive hover effects */
.contact-card {
    transition: var(--contact-transition);
}

.contact-card:hover .card-icon {
    animation: bounceIn 0.6s ease-out;
}

.form-input:focus {
    animation: pulse 0.3s ease-out;
}

.btn-primary.ready {
    animation: pulse 2s infinite;
}

.btn-primary.ready:hover {
    animation: none;
}
