/**
 * Modern Contact Page Manager
 * Handles contact form validation, submission, and user experience
 * with modern ES6+ features and enhanced accessibility
 */

import { ModernErrorHandler } from './utils/ModernErrorHandler.js';
import { ModernUtils } from './utils/ModernUtils.js';

class ModernContactManager {
    constructor() {
        this.elements = {};
        this.state = {
            isLoading: false,
            formData: {},
            validationErrors: {},
            isFormValid: false,
            formProgress: 0,
            isDirty: false
        };
        
        this.validators = {
            email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            phone: /^(\+234|0)[789][01]\d{8}$/,
            required: /\S+/,
            minLength: (value, min) => value.length >= min,
            maxLength: (value, max) => value.length <= max
        };
        
        this.errorHandler = new ModernErrorHandler();
        this.analytics = window.Analytics || null;
        
        this.init();
    }

    /**
     * Initialize the contact manager
     */
    init() {
        this.cacheElements();
        this.bindEvents();
        this.initializeFormValidation();
        this.initializeAnimations();
        this.setupAccessibility();
        this.updateFormProgress();
    }

    /**
     * Cache DOM elements
     */
    cacheElements() {
        this.elements = {
            form: document.getElementById('contact-form'),
            submitBtn: document.getElementById('submit-form'),
            resetBtn: document.getElementById('reset-form'),
            formStatus: document.getElementById('form-status'),
            progressBar: document.querySelector('.progress-bar'),
            toastContainer: this.createToastContainer(),
            
            // Form fields
            customerName: document.getElementById('customer-name'),
            customerEmail: document.getElementById('customer-email'),
            customerPhone: document.getElementById('customer-phone'),
            inquiryType: document.getElementById('inquiry-type'),
            customerMessage: document.getElementById('customer-message'),
            
            // All form inputs
            formInputs: document.querySelectorAll('.form-input'),
            requiredInputs: document.querySelectorAll('.form-input[required]'),
            
            // Character count
            messageCount: document.getElementById('customer-message-count')
        };
    }

    /**
     * Create toast container for notifications
     */
    createToastContainer() {
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container';
            container.setAttribute('aria-live', 'polite');
            container.setAttribute('aria-atomic', 'true');
            document.body.appendChild(container);
        }
        return container;
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Form submission
        if (this.elements.form) {
            this.elements.form.addEventListener('submit', this.handleFormSubmit.bind(this));
        }

        // Form reset
        if (this.elements.resetBtn) {
            this.elements.resetBtn.addEventListener('click', this.handleFormReset.bind(this));
        }

        // Real-time validation and progress
        this.elements.formInputs.forEach(input => {
            input.addEventListener('input', this.handleInputChange.bind(this));
            input.addEventListener('blur', this.handleInputBlur.bind(this));
            input.addEventListener('focus', this.handleInputFocus.bind(this));
        });

        // Character count for message field
        if (this.elements.customerMessage) {
            this.elements.customerMessage.addEventListener('input', this.updateCharacterCount.bind(this));
        }

        // Keyboard navigation
        document.addEventListener('keydown', this.handleKeyboardNavigation.bind(this));

        // Window events
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
        window.addEventListener('online', () => this.showToast('Connection restored', 'success'));
        window.addEventListener('offline', () => this.showToast('You are offline', 'warning'));

        // Form dirty state tracking
        this.elements.formInputs.forEach(input => {
            input.addEventListener('input', () => {
                this.state.isDirty = true;
            });
        });
    }

    /**
     * Initialize form validation
     */
    initializeFormValidation() {
        // Set up ARIA relationships
        this.elements.formInputs.forEach(input => {
            const errorId = input.getAttribute('aria-describedby')?.split(' ').find(id => id.includes('error'));
            if (errorId) {
                const errorElement = document.getElementById(errorId);
                if (errorElement) {
                    errorElement.setAttribute('role', 'alert');
                    errorElement.setAttribute('aria-live', 'polite');
                }
            }
        });

        // Initial validation state
        this.updateSubmitButton();
        this.updateFormProgress();
    }

    /**
     * Initialize animations
     */
    initializeAnimations() {
        // Initialize AOS if available
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 800,
                once: true,
                offset: 100,
                easing: 'ease-out-cubic'
            });
        }

        // Stagger animation for contact cards
        const contactCards = document.querySelectorAll('.contact-card');
        contactCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in');
        });
    }

    /**
     * Setup accessibility features
     */
    setupAccessibility() {
        // Add form description
        if (this.elements.form) {
            this.elements.form.setAttribute('aria-describedby', 'form-description');
            
            const description = document.createElement('div');
            description.id = 'form-description';
            description.className = 'sr-only';
            description.textContent = 'Contact form with real-time validation. Required fields are marked with an asterisk.';
            this.elements.form.insertBefore(description, this.elements.form.firstChild);
        }

        // Enhance submit button accessibility
        if (this.elements.submitBtn) {
            this.elements.submitBtn.setAttribute('aria-describedby', 'form-status');
        }

        // Add live region for announcements
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only';
        liveRegion.id = 'live-region';
        document.body.appendChild(liveRegion);
    }

    /**
     * Handle input changes for real-time validation
     */
    handleInputChange(e) {
        const field = e.target;
        const fieldName = field.name;

        // Debounce validation for better performance
        if (this.validationTimeout) {
            clearTimeout(this.validationTimeout);
        }

        // Update form data
        this.state.formData[fieldName] = field.value;

        // Clear previous error state
        this.clearFieldError(field);

        // Debounced validation
        this.validationTimeout = setTimeout(() => {
            // Validate if field has content or was previously validated
            if (field.value.trim() || this.state.validationErrors[fieldName]) {
                this.validateField(field);
            }

            // Update form state
            this.updateFormProgress();
            this.updateSubmitButton();
        }, 300);

        // Track analytics (throttled)
        this.throttledTrackFieldInteraction(fieldName, 'input');
    }

    /**
     * Handle input blur for validation
     */
    handleInputBlur(e) {
        const field = e.target;
        this.validateField(field);
        this.updateFormProgress();
        this.updateSubmitButton();
        
        this.trackFieldInteraction(field.name, 'blur');
    }

    /**
     * Handle input focus
     */
    handleInputFocus(e) {
        const field = e.target;
        this.clearFieldError(field);
        
        this.trackFieldInteraction(field.name, 'focus');
    }

    /**
     * Validate individual field
     */
    validateField(field) {
        const fieldName = field.name;
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        // Required field validation
        if (field.hasAttribute('required') && !this.validators.required.test(value)) {
            isValid = false;
            errorMessage = `${this.getFieldLabel(field)} is required`;
        }

        // Specific field validations
        if (value && isValid) {
            switch (fieldName) {
                case 'customerEmail':
                    if (!this.validators.email.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid email address';
                    } else if (value.length > 254) {
                        isValid = false;
                        errorMessage = 'Email address is too long';
                    }
                    break;

                case 'customerName':
                    // Check for valid name characters
                    const namePattern = /^[a-zA-Z\s\-'\.]+$/;
                    if (!namePattern.test(value)) {
                        isValid = false;
                        errorMessage = 'Name can only contain letters, spaces, hyphens, and apostrophes';
                    } else if (!this.validators.minLength(value, 2)) {
                        isValid = false;
                        errorMessage = 'Name must be at least 2 characters long';
                    } else if (!this.validators.maxLength(value, 100)) {
                        isValid = false;
                        errorMessage = 'Name must be less than 100 characters';
                    }
                    break;

                case 'customerPhone':
                    if (value && !this.validators.phone.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid Nigerian phone number (e.g., +234 ************ or 08012345678)';
                    }
                    break;

                case 'customerMessage':
                    // Check for suspicious content
                    const suspiciousPatterns = [
                        /https?:\/\/[^\s]+/gi, // URLs
                        /\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}\b/gi, // Email addresses
                        /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g // Credit card patterns
                    ];

                    const hasSuspiciousContent = suspiciousPatterns.some(pattern => pattern.test(value));

                    if (hasSuspiciousContent) {
                        isValid = false;
                        errorMessage = 'Please avoid including URLs, email addresses, or sensitive information in your message';
                    } else if (!this.validators.minLength(value, 10)) {
                        isValid = false;
                        errorMessage = 'Message must be at least 10 characters long';
                    } else if (!this.validators.maxLength(value, 1000)) {
                        isValid = false;
                        errorMessage = 'Message must be less than 1000 characters';
                    }
                    break;
            }
        }

        // Update field state
        this.updateFieldState(field, isValid, errorMessage);

        // Update validation state
        this.state.validationErrors[fieldName] = isValid ? null : errorMessage;

        return isValid;
    }

    /**
     * Get field label text
     */
    getFieldLabel(field) {
        const label = document.querySelector(`label[for="${field.id}"] .label-text`);
        return label ? label.textContent.trim() : field.name;
    }

    /**
     * Update field visual state
     */
    updateFieldState(field, isValid, errorMessage) {
        const formGroup = field.closest('.form-group');
        const errorElement = formGroup?.querySelector('.error-message');

        if (!formGroup) return;

        // Remove previous states
        formGroup.classList.remove('error', 'success');

        if (!isValid && errorMessage) {
            // Show error state
            formGroup.classList.add('error');
            if (errorElement) {
                errorElement.textContent = errorMessage;
                errorElement.classList.add('show');
            }
        } else if (field.value.trim() && isValid) {
            // Show success state
            formGroup.classList.add('success');
            if (errorElement) {
                errorElement.classList.remove('show');
            }
        }
    }

    /**
     * Clear field error state
     */
    clearFieldError(field) {
        const formGroup = field.closest('.form-group');
        const errorElement = formGroup?.querySelector('.error-message');

        if (formGroup) {
            formGroup.classList.remove('error');
            if (errorElement) {
                errorElement.classList.remove('show');
            }
        }
    }

    /**
     * Update character count for message field
     */
    updateCharacterCount() {
        if (!this.elements.customerMessage || !this.elements.messageCount) return;

        const current = this.elements.customerMessage.value.length;
        const max = 1000;
        const currentSpan = this.elements.messageCount.querySelector('.count-current');
        
        if (currentSpan) {
            currentSpan.textContent = current;
        }

        // Update styling based on character count
        this.elements.messageCount.classList.remove('warning', 'error');
        
        if (current > max * 0.9) {
            this.elements.messageCount.classList.add('warning');
        }
        
        if (current > max) {
            this.elements.messageCount.classList.add('error');
        }
    }

    /**
     * Update form progress
     */
    updateFormProgress() {
        if (!this.elements.progressBar) return;

        const totalFields = this.elements.requiredInputs.length;
        let completedFields = 0;

        this.elements.requiredInputs.forEach(input => {
            if (input.value.trim() && !this.state.validationErrors[input.name]) {
                completedFields++;
            }
        });

        const progress = totalFields > 0 ? (completedFields / totalFields) * 100 : 0;
        this.state.formProgress = progress;
        
        this.elements.progressBar.style.width = `${progress}%`;
        this.elements.progressBar.parentElement.setAttribute('aria-valuenow', progress);
    }

    /**
     * Update submit button state
     */
    updateSubmitButton() {
        if (!this.elements.submitBtn) return;

        let allValid = true;

        this.elements.requiredInputs.forEach(field => {
            const fieldName = field.name;
            const hasError = this.state.validationErrors[fieldName];
            const hasValue = field.value.trim();

            if (!hasValue || hasError) {
                allValid = false;
            }
        });

        this.state.isFormValid = allValid;
        this.elements.submitBtn.disabled = !allValid || this.state.isLoading;

        // Update button appearance
        if (allValid && !this.state.isLoading) {
            this.elements.submitBtn.classList.add('ready');
        } else {
            this.elements.submitBtn.classList.remove('ready');
        }
    }

    /**
     * Handle form submission
     */
    async handleFormSubmit(e) {
        e.preventDefault();

        if (this.state.isLoading || !this.state.isFormValid) return;

        // Final validation
        let isFormValid = true;
        this.elements.requiredInputs.forEach(input => {
            const isFieldValid = this.validateField(input);
            if (!isFieldValid) isFormValid = false;
        });

        if (!isFormValid) {
            this.showToast('Please fix the errors in the form', 'error');
            this.focusFirstError();
            this.announceToScreenReader('Form has errors. Please review and correct them.');
            return;
        }

        // Track form submission attempt
        this.trackEvent('contact_form_submit_attempt', {
            form_type: 'contact',
            inquiry_type: this.state.formData.inquiryType || 'general',
            form_progress: this.state.formProgress
        });

        try {
            await this.submitForm();
        } catch (error) {
            this.errorHandler.handleError(error);
            this.showToast('Failed to send message. Please try again.', 'error');
            this.announceToScreenReader('Failed to send message. Please try again.');
        }
    }

    /**
     * Submit form data
     */
    async submitForm() {
        this.setLoadingState(true);

        try {
            // Prepare form data
            const formData = new FormData(this.elements.form);
            const data = {
                customerName: formData.get('customerName'),
                customerEmail: formData.get('customerEmail'),
                customerPhone: formData.get('customerPhone') || null,
                inquiryType: formData.get('inquiryType') || 'general',
                customerMessage: formData.get('customerMessage'),
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                referrer: document.referrer,
                formProgress: this.state.formProgress
            };

            // Validate data one more time
            if (!data.customerName || !data.customerEmail || !data.customerMessage) {
                throw new Error('Missing required fields');
            }

            // Simulate API call (replace with actual endpoint)
            const response = await this.mockApiCall(data);

            if (response.success) {
                this.handleSubmissionSuccess(response);
                this.trackEvent('contact_form_submit_success', {
                    form_type: 'contact',
                    inquiry_type: data.inquiryType,
                    response_time: response.responseTime,
                    submission_id: response.id
                });
            } else {
                throw new Error(response.message || 'Submission failed');
            }

        } catch (error) {
            this.trackEvent('contact_form_submit_error', {
                form_type: 'contact',
                error_message: error.message,
                form_progress: this.state.formProgress
            });
            throw error;
        } finally {
            this.setLoadingState(false);
        }
    }

    /**
     * Mock API call for demonstration
     * Replace with actual API endpoint
     */
    async mockApiCall(data) {
        const startTime = Date.now();

        // Simulate network delay
        await ModernUtils.delay(1500);

        // Simulate occasional failures for testing
        if (Math.random() < 0.03) {
            throw new Error('Network error occurred. Please check your connection and try again.');
        }

        // Simulate server validation
        if (!data.customerEmail.includes('@')) {
            throw new Error('Invalid email address format');
        }

        // Simulate spam detection
        if (data.customerMessage.toLowerCase().includes('spam')) {
            throw new Error('Message flagged by spam filter. Please revise your message.');
        }

        return {
            success: true,
            message: 'Message sent successfully',
            responseTime: Date.now() - startTime,
            id: ModernUtils.generateId(),
            estimatedResponse: '2 hours',
            ticketNumber: `MM-${Date.now().toString().slice(-6)}`
        };
    }

    /**
     * Handle successful form submission
     */
    handleSubmissionSuccess(response) {
        // Show success message with ticket number
        const successMessage = response.ticketNumber
            ? `Message sent successfully! Your ticket number is ${response.ticketNumber}.`
            : 'Message sent successfully!';

        const detailMessage = `Thank you for contacting Magic Menu. We'll get back to you within ${response.estimatedResponse || '2 hours'}.`;

        this.showToast(
            successMessage,
            'success',
            detailMessage
        );

        // Update form status with more details
        this.updateFormStatus('success',
            `✅ Your message has been sent successfully! ${response.ticketNumber ? `Ticket: ${response.ticketNumber}` : ''}`
        );

        // Reset form
        this.resetForm();

        // Announce success to screen readers
        this.announceToScreenReader(`Message sent successfully. ${response.ticketNumber ? `Your ticket number is ${response.ticketNumber}.` : ''} Thank you for contacting us.`);

        // Focus on first field for better UX
        if (this.elements.customerName) {
            setTimeout(() => {
                this.elements.customerName.focus();
            }, 1000);
        }

        // Mark form as clean
        this.state.isDirty = false;
    }

    /**
     * Handle form reset
     */
    handleFormReset(e) {
        e.preventDefault();

        if (this.state.isDirty) {
            const confirmed = confirm('Are you sure you want to reset the form? All entered data will be lost.');
            if (!confirmed) return;
        }

        this.resetForm();
        this.trackEvent('contact_form_reset', {
            form_type: 'contact',
            form_progress: this.state.formProgress
        });
    }

    /**
     * Reset form to initial state
     */
    resetForm() {
        if (this.elements.form) {
            this.elements.form.reset();
            this.state.formData = {};
            this.state.validationErrors = {};
            this.state.isDirty = false;

            // Clear all field states
            this.elements.formInputs.forEach(input => {
                this.clearFieldError(input);
                const formGroup = input.closest('.form-group');
                if (formGroup) {
                    formGroup.classList.remove('success', 'error');
                }
            });

            // Reset character count
            this.updateCharacterCount();

            // Update form state
            this.updateFormProgress();
            this.updateSubmitButton();

            // Clear form status
            this.clearFormStatus();
        }
    }

    /**
     * Set loading state
     */
    setLoadingState(isLoading) {
        this.state.isLoading = isLoading;

        if (!this.elements.submitBtn) return;

        if (isLoading) {
            this.elements.submitBtn.disabled = true;
            this.elements.submitBtn.classList.add('loading');

            const btnText = this.elements.submitBtn.querySelector('.btn-text');
            if (btnText) {
                btnText.textContent = 'Sending...';
            }
        } else {
            this.elements.submitBtn.disabled = !this.state.isFormValid;
            this.elements.submitBtn.classList.remove('loading');

            const btnText = this.elements.submitBtn.querySelector('.btn-text');
            if (btnText) {
                btnText.textContent = 'Send Message';
            }
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info', title = null) {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        toast.innerHTML = `
            <i class="toast-icon ${iconMap[type]}" aria-hidden="true"></i>
            <div class="toast-content">
                ${title ? `<div class="toast-title">${ModernUtils.escapeHtml(title)}</div>` : ''}
                <p class="toast-message">${ModernUtils.escapeHtml(message)}</p>
            </div>
            <button class="toast-close" aria-label="Close notification">
                <i class="fas fa-times" aria-hidden="true"></i>
            </button>
        `;

        // Add close functionality
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => this.removeToast(toast));

        // Add to container
        this.elements.toastContainer.appendChild(toast);

        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto remove after 5 seconds
        setTimeout(() => this.removeToast(toast), 5000);

        return toast;
    }

    /**
     * Remove toast notification
     */
    removeToast(toast) {
        if (!toast || !toast.parentNode) return;

        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    /**
     * Update form status
     */
    updateFormStatus(type, message) {
        if (!this.elements.formStatus) return;

        this.elements.formStatus.className = `form-status ${type} show`;
        this.elements.formStatus.textContent = message;
        this.elements.formStatus.setAttribute('role', 'alert');
    }

    /**
     * Clear form status
     */
    clearFormStatus() {
        if (!this.elements.formStatus) return;

        this.elements.formStatus.classList.remove('show', 'success', 'error');
        this.elements.formStatus.textContent = '';
        this.elements.formStatus.removeAttribute('role');
    }

    /**
     * Focus first error field
     */
    focusFirstError() {
        const errorField = this.elements.form?.querySelector('.form-group.error .form-input');
        if (errorField) {
            errorField.focus();
            errorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    /**
     * Announce message to screen readers
     */
    announceToScreenReader(message) {
        const liveRegion = document.getElementById('live-region');
        if (liveRegion) {
            liveRegion.textContent = message;
            setTimeout(() => {
                liveRegion.textContent = '';
            }, 1000);
        }
    }

    /**
     * Handle keyboard navigation
     */
    handleKeyboardNavigation(e) {
        // Escape key closes toasts
        if (e.key === 'Escape') {
            const toasts = this.elements.toastContainer.querySelectorAll('.toast');
            toasts.forEach(toast => this.removeToast(toast));
        }

        // Ctrl+Enter submits form
        if (e.ctrlKey && e.key === 'Enter' && this.state.isFormValid) {
            e.preventDefault();
            this.elements.submitBtn?.click();
        }

        // Ctrl+R resets form (with confirmation)
        if (e.ctrlKey && e.key === 'r' && this.state.isDirty) {
            e.preventDefault();
            this.elements.resetBtn?.click();
        }
    }

    /**
     * Handle before unload (warn about unsaved changes)
     */
    handleBeforeUnload(e) {
        if (this.state.isDirty && !this.state.isLoading) {
            e.preventDefault();
            e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
            return e.returnValue;
        }
    }

    /**
     * Track field interactions for analytics (throttled)
     */
    throttledTrackFieldInteraction(fieldName, action) {
        const key = `${fieldName}_${action}`;
        const now = Date.now();

        if (!this.trackingThrottle) {
            this.trackingThrottle = {};
        }

        // Throttle to once per second per field/action combination
        if (!this.trackingThrottle[key] || now - this.trackingThrottle[key] > 1000) {
            this.trackingThrottle[key] = now;
            this.trackFieldInteraction(fieldName, action);
        }
    }

    /**
     * Track field interactions for analytics
     */
    trackFieldInteraction(fieldName, action) {
        this.trackEvent('contact_form_field_interaction', {
            field_name: fieldName,
            action: action,
            form_progress: this.state.formProgress,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * Track analytics events
     */
    trackEvent(eventName, properties = {}) {
        try {
            // Google Analytics 4
            if (typeof gtag !== 'undefined') {
                gtag('event', eventName, properties);
            }

            // Custom analytics
            if (this.analytics && this.analytics.consent) {
                this.analytics.track(eventName, {
                    ...properties,
                    page_url: window.location.href,
                    user_agent: navigator.userAgent
                });
            }
        } catch (error) {
            console.warn('Analytics tracking failed:', error);
        }
    }

    /**
     * Get current form data
     */
    getFormData() {
        return { ...this.state.formData };
    }

    /**
     * Check if form is valid
     */
    isFormValid() {
        return this.state.isFormValid;
    }

    /**
     * Get form progress percentage
     */
    getFormProgress() {
        return this.state.formProgress;
    }

    /**
     * Check if form has unsaved changes
     */
    isDirty() {
        return this.state.isDirty;
    }

    /**
     * Manually validate all fields
     */
    validateAllFields() {
        let isValid = true;

        this.elements.formInputs.forEach(input => {
            const fieldValid = this.validateField(input);
            if (!fieldValid) isValid = false;
        });

        this.updateFormProgress();
        this.updateSubmitButton();

        return isValid;
    }

    /**
     * Destroy the contact manager
     */
    destroy() {
        // Clear timeouts
        if (this.validationTimeout) {
            clearTimeout(this.validationTimeout);
        }

        // Clear throttling cache
        this.trackingThrottle = {};

        // Remove event listeners
        if (this.elements.form) {
            this.elements.form.removeEventListener('submit', this.handleFormSubmit);
        }

        if (this.elements.resetBtn) {
            this.elements.resetBtn.removeEventListener('click', this.handleFormReset);
        }

        this.elements.formInputs.forEach(input => {
            input.removeEventListener('input', this.handleInputChange);
            input.removeEventListener('blur', this.handleInputBlur);
            input.removeEventListener('focus', this.handleInputFocus);
        });

        if (this.elements.customerMessage) {
            this.elements.customerMessage.removeEventListener('input', this.updateCharacterCount);
        }

        document.removeEventListener('keydown', this.handleKeyboardNavigation);
        window.removeEventListener('beforeunload', this.handleBeforeUnload);

        // Clear toasts
        const toasts = this.elements.toastContainer.querySelectorAll('.toast');
        toasts.forEach(toast => this.removeToast(toast));

        // Remove live region
        const liveRegion = document.getElementById('live-region');
        if (liveRegion) {
            liveRegion.remove();
        }

        // Clear state
        this.state = {};
        this.elements = {};
    }
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.modernContactManager = new ModernContactManager();
});

// Export for manual initialization
export default ModernContactManager;
