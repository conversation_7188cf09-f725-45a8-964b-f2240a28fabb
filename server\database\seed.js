const db = require('../utils/DatabaseSecurity');
const bcrypt = require('bcrypt');
require('dotenv').config();

class DatabaseSeeder {
    constructor() {
        this.saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
    }

    async seedUsers() {
        try {
            console.log('🌱 Seeding users...');
            
            // Create admin user
            const adminPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD || 'SecureAdminPassword123!', this.saltRounds);
            
            const adminUser = {
                email: process.env.ADMIN_EMAIL || '<EMAIL>',
                password_hash: adminPassword,
                first_name: 'Admin',
                last_name: 'User',
                phone: '+234-************',
                role: 'admin',
                is_active: true,
                email_verified: true
            };

            await db.safeInsert('users', adminUser);
            console.log('✓ Admin user created');

            // Create sample customer
            const customerPassword = await bcrypt.hash('CustomerPassword123!', this.saltRounds);
            
            const customerUser = {
                email: '<EMAIL>',
                password_hash: customerPassword,
                first_name: 'John',
                last_name: 'Doe',
                phone: '+234-************',
                role: 'customer',
                is_active: true,
                email_verified: true
            };

            await db.safeInsert('users', customerUser);
            console.log('✓ Sample customer created');

        } catch (error) {
            console.error('✗ Error seeding users:', error.message);
            throw error;
        }
    }

    async seedMenuCategories() {
        try {
            console.log('🌱 Seeding menu categories...');
            
            const categories = [
                {
                    name: 'Local Delights',
                    description: 'Traditional Nigerian dishes prepared with authentic flavors',
                    display_order: 1
                },
                {
                    name: 'International Flavors',
                    description: 'Popular international dishes with a local twist',
                    display_order: 2
                },
                {
                    name: 'Beverages',
                    description: 'Refreshing drinks and traditional beverages',
                    display_order: 3
                },
                {
                    name: 'Desserts',
                    description: 'Sweet treats and traditional desserts',
                    display_order: 4
                }
            ];

            for (const category of categories) {
                await db.safeInsert('menu_categories', category);
            }
            
            console.log('✓ Menu categories created');
        } catch (error) {
            console.error('✗ Error seeding menu categories:', error.message);
            throw error;
        }
    }

    async seedMenuItems() {
        try {
            console.log('🌱 Seeding menu items...');
            
            // Get category IDs
            const categories = await db.query('SELECT id, name FROM menu_categories ORDER BY display_order');
            const categoryMap = {};
            categories.forEach(cat => {
                categoryMap[cat.name] = cat.id;
            });

            const menuItems = [
                // Local Delights
                {
                    category_id: categoryMap['Local Delights'],
                    name: 'Jollof Rice with Chicken',
                    description: 'Our signature jollof rice served with tender grilled chicken, plantain, and coleslaw',
                    price: 2500.00,
                    is_available: true,
                    is_featured: true,
                    preparation_time: 25,
                    calories: 650,
                    allergens: ['gluten'],
                    ingredients: ['rice', 'chicken', 'tomatoes', 'onions', 'peppers', 'spices'],
                    display_order: 1
                },
                {
                    category_id: categoryMap['Local Delights'],
                    name: 'Pounded Yam with Egusi Soup',
                    description: 'Fresh pounded yam served with rich egusi soup, assorted meat, and fish',
                    price: 3000.00,
                    is_available: true,
                    is_featured: true,
                    preparation_time: 30,
                    calories: 750,
                    allergens: [],
                    ingredients: ['yam', 'egusi', 'beef', 'fish', 'vegetables', 'palm oil'],
                    display_order: 2
                },
                {
                    category_id: categoryMap['Local Delights'],
                    name: 'Pepper Soup',
                    description: 'Spicy Nigerian pepper soup with assorted meat or fish',
                    price: 2000.00,
                    is_available: true,
                    preparation_time: 20,
                    calories: 400,
                    allergens: [],
                    ingredients: ['meat', 'pepper soup spices', 'vegetables'],
                    display_order: 3
                },
                {
                    category_id: categoryMap['Local Delights'],
                    name: 'Fried Rice',
                    description: 'Colorful fried rice with mixed vegetables, chicken, and prawns',
                    price: 2800.00,
                    is_available: true,
                    preparation_time: 20,
                    calories: 580,
                    allergens: ['shellfish'],
                    ingredients: ['rice', 'chicken', 'prawns', 'mixed vegetables', 'soy sauce'],
                    display_order: 4
                },

                // International Flavors
                {
                    category_id: categoryMap['International Flavors'],
                    name: 'Margherita Pizza',
                    description: 'Classic pizza with fresh tomatoes, mozzarella, and basil',
                    price: 3500.00,
                    is_available: true,
                    is_featured: true,
                    preparation_time: 15,
                    calories: 520,
                    allergens: ['gluten', 'dairy'],
                    ingredients: ['pizza dough', 'tomato sauce', 'mozzarella', 'basil'],
                    display_order: 1
                },
                {
                    category_id: categoryMap['International Flavors'],
                    name: 'Chicken Burger',
                    description: 'Grilled chicken breast with lettuce, tomato, and special sauce',
                    price: 2200.00,
                    is_available: true,
                    preparation_time: 12,
                    calories: 480,
                    allergens: ['gluten', 'eggs'],
                    ingredients: ['chicken breast', 'burger bun', 'lettuce', 'tomato', 'sauce'],
                    display_order: 2
                },
                {
                    category_id: categoryMap['International Flavors'],
                    name: 'Spaghetti Bolognese',
                    description: 'Classic Italian pasta with rich meat sauce and parmesan',
                    price: 2600.00,
                    is_available: true,
                    preparation_time: 18,
                    calories: 620,
                    allergens: ['gluten', 'dairy'],
                    ingredients: ['spaghetti', 'ground beef', 'tomato sauce', 'parmesan', 'herbs'],
                    display_order: 3
                },

                // Beverages
                {
                    category_id: categoryMap['Beverages'],
                    name: 'Fresh Orange Juice',
                    description: 'Freshly squeezed orange juice',
                    price: 800.00,
                    is_available: true,
                    preparation_time: 5,
                    calories: 120,
                    allergens: [],
                    ingredients: ['fresh oranges'],
                    display_order: 1
                },
                {
                    category_id: categoryMap['Beverages'],
                    name: 'Chapman',
                    description: 'Nigerian cocktail with mixed fruits and sparkling water',
                    price: 1200.00,
                    is_available: true,
                    preparation_time: 8,
                    calories: 150,
                    allergens: [],
                    ingredients: ['mixed fruits', 'sparkling water', 'grenadine', 'cucumber'],
                    display_order: 2
                },
                {
                    category_id: categoryMap['Beverages'],
                    name: 'Zobo Drink',
                    description: 'Traditional hibiscus drink with natural flavors',
                    price: 600.00,
                    is_available: true,
                    preparation_time: 5,
                    calories: 80,
                    allergens: [],
                    ingredients: ['hibiscus leaves', 'ginger', 'cucumber', 'watermelon'],
                    display_order: 3
                },

                // Desserts
                {
                    category_id: categoryMap['Desserts'],
                    name: 'Chocolate Cake',
                    description: 'Rich chocolate cake with chocolate frosting',
                    price: 1500.00,
                    is_available: true,
                    preparation_time: 5,
                    calories: 380,
                    allergens: ['gluten', 'dairy', 'eggs'],
                    ingredients: ['flour', 'chocolate', 'eggs', 'butter', 'sugar'],
                    display_order: 1
                },
                {
                    category_id: categoryMap['Desserts'],
                    name: 'Fruit Salad',
                    description: 'Fresh mixed fruits with honey dressing',
                    price: 1000.00,
                    is_available: true,
                    preparation_time: 5,
                    calories: 150,
                    allergens: [],
                    ingredients: ['mixed seasonal fruits', 'honey'],
                    display_order: 2
                }
            ];

            for (const item of menuItems) {
                await db.safeInsert('menu_items', item);
            }
            
            console.log('✓ Menu items created');
        } catch (error) {
            console.error('✗ Error seeding menu items:', error.message);
            throw error;
        }
    }

    async seedAll() {
        try {
            console.log('🚀 Starting database seeding...');
            
            // Test database connection
            const isConnected = await db.testConnection();
            if (!isConnected) {
                throw new Error('Database connection failed');
            }

            await this.seedUsers();
            await this.seedMenuCategories();
            await this.seedMenuItems();
            
            console.log('✅ Database seeding completed successfully!');
            console.log('\n📋 Seeded data summary:');
            console.log('- Admin user: <EMAIL>');
            console.log('- Sample customer: <EMAIL>');
            console.log('- 4 menu categories');
            console.log('- 12 menu items');
            
        } catch (error) {
            console.error('❌ Database seeding failed:', error.message);
            throw error;
        }
    }

    async clearAll() {
        try {
            console.log('🧹 Clearing existing seed data...');
            
            // Clear in reverse order of dependencies
            await db.query('DELETE FROM cart_items');
            await db.query('DELETE FROM shopping_carts');
            await db.query('DELETE FROM order_items');
            await db.query('DELETE FROM orders');
            await db.query('DELETE FROM menu_items');
            await db.query('DELETE FROM menu_categories');
            await db.query('DELETE FROM user_addresses');
            await db.query('DELETE FROM user_sessions');
            await db.query('DELETE FROM users');
            await db.query('DELETE FROM contact_submissions');
            
            console.log('✓ Existing data cleared');
        } catch (error) {
            console.error('✗ Error clearing data:', error.message);
            throw error;
        }
    }
}

// CLI interface
async function main() {
    const seeder = new DatabaseSeeder();
    const command = process.argv[2];

    try {
        switch (command) {
            case 'seed':
            case undefined:
                await seeder.seedAll();
                break;
            case 'clear':
                await seeder.clearAll();
                break;
            case 'reset':
                await seeder.clearAll();
                await seeder.seedAll();
                break;
            default:
                console.log('Usage: node seed.js [seed|clear|reset]');
                console.log('  seed (default): Add seed data to database');
                console.log('  clear: Remove all seed data');
                console.log('  reset: Clear and re-seed data');
        }
    } catch (error) {
        console.error('Seeding command failed:', error.message);
        process.exit(1);
    } finally {
        await db.close();
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = DatabaseSeeder;
