const BaseModel = require('./BaseModel');
const { CURRENCY_CONFIG } = require('../../assets/scripts/config/currency.js');

class Cart extends BaseModel {
    constructor() {
        super('shopping_carts');
    }

    // Get or create cart for user
    async getOrCreateUserCart(userId) {
        try {
            let cart = await this.findOne({ user_id: userId });
            
            if (!cart) {
                cart = await this.create({ user_id: userId });
            }
            
            return cart;
        } catch (error) {
            console.error('Error getting or creating user cart:', error);
            throw error;
        }
    }

    // Get or create cart for session (guest users)
    async getOrCreateSessionCart(sessionId) {
        try {
            let cart = await this.findOne({ session_id: sessionId });
            
            if (!cart) {
                cart = await this.create({ session_id: sessionId });
            }
            
            return cart;
        } catch (error) {
            console.error('Error getting or creating session cart:', error);
            throw error;
        }
    }

    // Get cart with items
    async getCartWithItems(cartId) {
        try {
            const query = `
                SELECT 
                    sc.*,
                    json_agg(
                        json_build_object(
                            'id', ci.id,
                            'menu_item_id', ci.menu_item_id,
                            'quantity', ci.quantity,
                            'unit_price', ci.unit_price,
                            'special_instructions', ci.special_instructions,
                            'menu_item', json_build_object(
                                'id', mi.id,
                                'name', mi.name,
                                'description', mi.description,
                                'price', mi.price,
                                'image_url', mi.image_url,
                                'is_available', mi.is_available,
                                'preparation_time', mi.preparation_time,
                                'category_name', mc.name
                            )
                        ) ORDER BY ci.created_at
                    ) FILTER (WHERE ci.id IS NOT NULL) as items
                FROM shopping_carts sc
                LEFT JOIN cart_items ci ON sc.id = ci.cart_id
                LEFT JOIN menu_items mi ON ci.menu_item_id = mi.id
                LEFT JOIN menu_categories mc ON mi.category_id = mc.id
                WHERE sc.id = $1
                GROUP BY sc.id
            `;
            const result = await this.db.query(query, [cartId]);
            return result[0] || null;
        } catch (error) {
            console.error('Error getting cart with items:', error);
            throw error;
        }
    }

    // Add item to cart
    async addItem(cartId, menuItemId, quantity, specialInstructions = null) {
        try {
            const client = await this.db.pool.connect();
            
            try {
                await client.query('BEGIN');
                
                // Get menu item details
                const menuItemResult = await client.query(
                    'SELECT id, price, is_available FROM menu_items WHERE id = $1',
                    [menuItemId]
                );
                
                if (menuItemResult.rows.length === 0) {
                    throw new Error('Menu item not found');
                }
                
                const menuItem = menuItemResult.rows[0];
                if (!menuItem.is_available) {
                    throw new Error('Menu item is not available');
                }
                
                // Check if item already exists in cart
                const existingItemResult = await client.query(
                    'SELECT id, quantity FROM cart_items WHERE cart_id = $1 AND menu_item_id = $2',
                    [cartId, menuItemId]
                );
                
                if (existingItemResult.rows.length > 0) {
                    // Update existing item
                    const existingItem = existingItemResult.rows[0];
                    const newQuantity = existingItem.quantity + quantity;
                    
                    await client.query(
                        'UPDATE cart_items SET quantity = $1, special_instructions = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3',
                        [newQuantity, specialInstructions, existingItem.id]
                    );
                } else {
                    // Add new item
                    await client.query(
                        'INSERT INTO cart_items (cart_id, menu_item_id, quantity, unit_price, special_instructions) VALUES ($1, $2, $3, $4, $5)',
                        [cartId, menuItemId, quantity, menuItem.price, specialInstructions]
                    );
                }
                
                // Update cart timestamp
                await client.query(
                    'UPDATE shopping_carts SET updated_at = CURRENT_TIMESTAMP WHERE id = $1',
                    [cartId]
                );
                
                await client.query('COMMIT');
                
                // Return updated cart
                return await this.getCartWithItems(cartId);
                
            } catch (error) {
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            console.error('Error adding item to cart:', error);
            throw error;
        }
    }

    // Update item quantity
    async updateItemQuantity(cartId, menuItemId, quantity) {
        try {
            if (quantity <= 0) {
                return await this.removeItem(cartId, menuItemId);
            }
            
            const result = await this.db.query(
                'UPDATE cart_items SET quantity = $1, updated_at = CURRENT_TIMESTAMP WHERE cart_id = $2 AND menu_item_id = $3 RETURNING *',
                [quantity, cartId, menuItemId]
            );
            
            if (result.length === 0) {
                throw new Error('Cart item not found');
            }
            
            // Update cart timestamp
            await this.db.query(
                'UPDATE shopping_carts SET updated_at = CURRENT_TIMESTAMP WHERE id = $1',
                [cartId]
            );
            
            return await this.getCartWithItems(cartId);
        } catch (error) {
            console.error('Error updating item quantity:', error);
            throw error;
        }
    }

    // Remove item from cart
    async removeItem(cartId, menuItemId) {
        try {
            const result = await this.db.query(
                'DELETE FROM cart_items WHERE cart_id = $1 AND menu_item_id = $2 RETURNING *',
                [cartId, menuItemId]
            );
            
            if (result.length === 0) {
                throw new Error('Cart item not found');
            }
            
            // Update cart timestamp
            await this.db.query(
                'UPDATE shopping_carts SET updated_at = CURRENT_TIMESTAMP WHERE id = $1',
                [cartId]
            );
            
            return await this.getCartWithItems(cartId);
        } catch (error) {
            console.error('Error removing item from cart:', error);
            throw error;
        }
    }

    // Clear cart
    async clearCart(cartId) {
        try {
            await this.db.query('DELETE FROM cart_items WHERE cart_id = $1', [cartId]);
            
            // Update cart timestamp
            await this.db.query(
                'UPDATE shopping_carts SET updated_at = CURRENT_TIMESTAMP WHERE id = $1',
                [cartId]
            );
            
            return await this.getCartWithItems(cartId);
        } catch (error) {
            console.error('Error clearing cart:', error);
            throw error;
        }
    }

    // Calculate cart totals
    calculateTotals(cartItems) {
        if (!cartItems || cartItems.length === 0) {
            return {
                subtotal: 0,
                tax: 0,
                delivery_fee: CURRENCY_CONFIG.deliveryFee,
                total: CURRENCY_CONFIG.deliveryFee
            };
        }
        
        const subtotal = cartItems.reduce((sum, item) => {
            return sum + (parseFloat(item.unit_price) * item.quantity);
        }, 0);
        
        const tax = subtotal * CURRENCY_CONFIG.vatRate;
        const delivery_fee = CURRENCY_CONFIG.deliveryFee;
        const total = subtotal + tax + delivery_fee;
        
        return {
            subtotal: parseFloat(subtotal.toFixed(2)),
            tax: parseFloat(tax.toFixed(2)),
            delivery_fee: parseFloat(delivery_fee.toFixed(2)),
            total: parseFloat(total.toFixed(2))
        };
    }

    // Get cart summary with totals
    async getCartSummary(cartId) {
        try {
            const cart = await this.getCartWithItems(cartId);
            if (!cart) {
                return null;
            }
            
            const totals = this.calculateTotals(cart.items);
            
            return {
                ...cart,
                totals,
                item_count: cart.items ? cart.items.length : 0,
                total_quantity: cart.items ? cart.items.reduce((sum, item) => sum + item.quantity, 0) : 0
            };
        } catch (error) {
            console.error('Error getting cart summary:', error);
            throw error;
        }
    }

    // Merge guest cart with user cart (when user logs in)
    async mergeGuestCartWithUserCart(sessionId, userId) {
        try {
            const client = await this.db.pool.connect();
            
            try {
                await client.query('BEGIN');
                
                // Get guest cart
                const guestCartResult = await client.query(
                    'SELECT id FROM shopping_carts WHERE session_id = $1',
                    [sessionId]
                );
                
                if (guestCartResult.rows.length === 0) {
                    await client.query('COMMIT');
                    return await this.getOrCreateUserCart(userId);
                }
                
                const guestCartId = guestCartResult.rows[0].id;
                
                // Get or create user cart
                let userCartResult = await client.query(
                    'SELECT id FROM shopping_carts WHERE user_id = $1',
                    [userId]
                );
                
                let userCartId;
                if (userCartResult.rows.length === 0) {
                    const newCartResult = await client.query(
                        'INSERT INTO shopping_carts (user_id) VALUES ($1) RETURNING id',
                        [userId]
                    );
                    userCartId = newCartResult.rows[0].id;
                } else {
                    userCartId = userCartResult.rows[0].id;
                }
                
                // Move items from guest cart to user cart
                await client.query(
                    'UPDATE cart_items SET cart_id = $1 WHERE cart_id = $2',
                    [userCartId, guestCartId]
                );
                
                // Delete guest cart
                await client.query('DELETE FROM shopping_carts WHERE id = $1', [guestCartId]);
                
                await client.query('COMMIT');
                
                return await this.getCartWithItems(userCartId);
                
            } catch (error) {
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            console.error('Error merging guest cart with user cart:', error);
            throw error;
        }
    }
}

module.exports = new Cart();
