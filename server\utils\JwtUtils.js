const jwt = require('jsonwebtoken');
const crypto = require('crypto');
require('dotenv').config();

class JwtUtils {
    constructor() {
        this.jwtSecret = process.env.JWT_SECRET;
        this.jwtRefreshSecret = process.env.JWT_REFRESH_SECRET;
        this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '24h';
        this.jwtRefreshExpiresIn = process.env.JWT_REFRESH_EXPIRES_IN || '7d';
        
        if (!this.jwtSecret || !this.jwtRefreshSecret) {
            throw new Error('JWT secrets not configured in environment variables');
        }
    }

    // Generate access token
    generateAccessToken(payload) {
        try {
            return jwt.sign(payload, this.jwtSecret, {
                expiresIn: this.jwtExpiresIn,
                issuer: 'magic-menu-api',
                audience: 'magic-menu-client'
            });
        } catch (error) {
            console.error('Error generating access token:', error);
            throw new Error('Token generation failed');
        }
    }

    // Generate refresh token
    generateRefreshToken(payload) {
        try {
            return jwt.sign(payload, this.jwtRefreshSecret, {
                expiresIn: this.jwtRefreshExpiresIn,
                issuer: 'magic-menu-api',
                audience: 'magic-menu-client'
            });
        } catch (error) {
            console.error('Error generating refresh token:', error);
            throw new Error('Refresh token generation failed');
        }
    }

    // Generate token pair
    generateTokenPair(user) {
        const payload = {
            userId: user.id,
            email: user.email,
            role: user.role,
            tokenId: crypto.randomUUID()
        };

        return {
            accessToken: this.generateAccessToken(payload),
            refreshToken: this.generateRefreshToken(payload),
            expiresIn: this.jwtExpiresIn,
            tokenType: 'Bearer'
        };
    }

    // Verify access token
    verifyAccessToken(token) {
        try {
            return jwt.verify(token, this.jwtSecret, {
                issuer: 'magic-menu-api',
                audience: 'magic-menu-client'
            });
        } catch (error) {
            if (error.name === 'TokenExpiredError') {
                throw new Error('Access token expired');
            } else if (error.name === 'JsonWebTokenError') {
                throw new Error('Invalid access token');
            } else {
                throw new Error('Token verification failed');
            }
        }
    }

    // Verify refresh token
    verifyRefreshToken(token) {
        try {
            return jwt.verify(token, this.jwtRefreshSecret, {
                issuer: 'magic-menu-api',
                audience: 'magic-menu-client'
            });
        } catch (error) {
            if (error.name === 'TokenExpiredError') {
                throw new Error('Refresh token expired');
            } else if (error.name === 'JsonWebTokenError') {
                throw new Error('Invalid refresh token');
            } else {
                throw new Error('Refresh token verification failed');
            }
        }
    }

    // Extract token from Authorization header
    extractTokenFromHeader(authHeader) {
        if (!authHeader) {
            return null;
        }

        const parts = authHeader.split(' ');
        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            return null;
        }

        return parts[1];
    }

    // Decode token without verification (for debugging)
    decodeToken(token) {
        try {
            return jwt.decode(token, { complete: true });
        } catch (error) {
            console.error('Error decoding token:', error);
            return null;
        }
    }

    // Check if token is expired
    isTokenExpired(token) {
        try {
            const decoded = jwt.decode(token);
            if (!decoded || !decoded.exp) {
                return true;
            }
            
            const currentTime = Math.floor(Date.now() / 1000);
            return decoded.exp < currentTime;
        } catch (error) {
            return true;
        }
    }

    // Get token expiration time
    getTokenExpiration(token) {
        try {
            const decoded = jwt.decode(token);
            if (!decoded || !decoded.exp) {
                return null;
            }
            
            return new Date(decoded.exp * 1000);
        } catch (error) {
            return null;
        }
    }

    // Refresh access token using refresh token
    async refreshAccessToken(refreshToken, userModel) {
        try {
            // Verify refresh token
            const decoded = this.verifyRefreshToken(refreshToken);
            
            // Get fresh user data
            const user = await userModel.findById(decoded.userId);
            if (!user || !user.is_active) {
                throw new Error('User not found or inactive');
            }

            // Generate new access token
            const payload = {
                userId: user.id,
                email: user.email,
                role: user.role,
                tokenId: crypto.randomUUID()
            };

            return {
                accessToken: this.generateAccessToken(payload),
                expiresIn: this.jwtExpiresIn,
                tokenType: 'Bearer'
            };
        } catch (error) {
            console.error('Error refreshing access token:', error);
            throw error;
        }
    }

    // Generate secure random token (for password reset, email verification, etc.)
    generateSecureToken(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }

    // Hash token for storage (one-way)
    hashToken(token) {
        return crypto.createHash('sha256').update(token).digest('hex');
    }

    // Verify hashed token
    verifyHashedToken(token, hashedToken) {
        const tokenHash = this.hashToken(token);
        return crypto.timingSafeEqual(
            Buffer.from(tokenHash, 'hex'),
            Buffer.from(hashedToken, 'hex')
        );
    }
}

module.exports = new JwtUtils();
