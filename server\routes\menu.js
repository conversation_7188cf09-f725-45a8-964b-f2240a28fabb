const express = require('express');
const Joi = require('joi');
const MenuItem = require('../models/MenuItem');
const MenuCategory = require('../models/MenuCategory');
const AuthMiddleware = require('../middleware/auth');

const router = express.Router();

// Validation schemas
const categorySchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    displayOrder: Joi.number().integer().min(0).optional()
});

const menuItemSchema = Joi.object({
    categoryId: Joi.string().uuid().required(),
    name: Joi.string().min(2).max(255).required(),
    description: Joi.string().max(1000).optional(),
    price: Joi.number().positive().precision(2).required(),
    imageUrl: Joi.string().uri().optional(),
    isAvailable: Joi.boolean().optional(),
    isFeatured: Joi.boolean().optional(),
    preparationTime: Joi.number().integer().min(1).max(120).optional(),
    calories: Joi.number().integer().min(0).optional(),
    allergens: Joi.array().items(Joi.string()).optional(),
    ingredients: Joi.array().items(Joi.string()).optional(),
    nutritionalInfo: Joi.object().optional(),
    displayOrder: Joi.number().integer().min(0).optional(),
    tags: Joi.array().items(Joi.string()).optional()
});

// PUBLIC ROUTES

// Get all menu categories (public)
router.get('/categories', async (req, res) => {
    try {
        const categories = await MenuCategory.getPublicCategories();
        
        res.json({
            categories,
            count: categories.length
        });
    } catch (error) {
        console.error('Get categories error:', error);
        res.status(500).json({
            error: 'Failed to fetch categories',
            message: 'Internal server error'
        });
    }
});

// Get all menu items with categories (public)
router.get('/items', async (req, res) => {
    try {
        const { 
            category, 
            featured, 
            search, 
            minPrice, 
            maxPrice, 
            allergens,
            page = 1, 
            limit = 50 
        } = req.query;

        let items;

        // Handle different query types
        if (search) {
            items = await MenuItem.search(search);
        } else if (category) {
            items = await MenuItem.getByCategory(category);
        } else if (featured === 'true') {
            items = await MenuItem.getFeatured();
        } else if (minPrice || maxPrice) {
            const min = parseFloat(minPrice) || 0;
            const max = parseFloat(maxPrice) || Number.MAX_VALUE;
            items = await MenuItem.getByPriceRange(min, max);
        } else if (allergens) {
            const excludeAllergens = Array.isArray(allergens) ? allergens : [allergens];
            items = await MenuItem.getWithAllergenFilter(excludeAllergens);
        } else {
            items = await MenuItem.getAllWithCategories();
        }

        // Apply pagination
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const startIndex = (pageNum - 1) * limitNum;
        const endIndex = startIndex + limitNum;
        
        const paginatedItems = items.slice(startIndex, endIndex);

        res.json({
            items: paginatedItems,
            pagination: {
                page: pageNum,
                limit: limitNum,
                total: items.length,
                pages: Math.ceil(items.length / limitNum),
                hasNext: endIndex < items.length,
                hasPrev: pageNum > 1
            }
        });
    } catch (error) {
        console.error('Get menu items error:', error);
        res.status(500).json({
            error: 'Failed to fetch menu items',
            message: 'Internal server error'
        });
    }
});

// Get single menu item (public)
router.get('/items/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
            return res.status(400).json({
                error: 'Invalid ID format',
                message: 'Menu item ID must be a valid UUID'
            });
        }

        const item = await MenuItem.findById(id);
        if (!item) {
            return res.status(404).json({
                error: 'Menu item not found',
                message: 'The requested menu item does not exist'
            });
        }

        res.json({ item });
    } catch (error) {
        console.error('Get menu item error:', error);
        res.status(500).json({
            error: 'Failed to fetch menu item',
            message: 'Internal server error'
        });
    }
});

// Get category with items (public)
router.get('/categories/:id/items', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
            return res.status(400).json({
                error: 'Invalid ID format',
                message: 'Category ID must be a valid UUID'
            });
        }

        const category = await MenuCategory.getCategoryWithItems(id);
        if (!category) {
            return res.status(404).json({
                error: 'Category not found',
                message: 'The requested category does not exist'
            });
        }

        res.json({ category });
    } catch (error) {
        console.error('Get category with items error:', error);
        res.status(500).json({
            error: 'Failed to fetch category',
            message: 'Internal server error'
        });
    }
});

// Get popular menu items (public)
router.get('/popular', async (req, res) => {
    try {
        const { limit = 10 } = req.query;
        const items = await MenuItem.getPopularItems(parseInt(limit));
        
        res.json({
            items,
            count: items.length
        });
    } catch (error) {
        console.error('Get popular items error:', error);
        res.status(500).json({
            error: 'Failed to fetch popular items',
            message: 'Internal server error'
        });
    }
});

// ADMIN ROUTES (require authentication and admin role)

// Get all categories with item counts (admin)
router.get('/admin/categories', 
    AuthMiddleware.authenticate, 
    AuthMiddleware.requireAdmin, 
    async (req, res) => {
        try {
            const categories = await MenuCategory.getAllWithItemCounts();
            
            res.json({
                categories,
                count: categories.length
            });
        } catch (error) {
            console.error('Get admin categories error:', error);
            res.status(500).json({
                error: 'Failed to fetch categories',
                message: 'Internal server error'
            });
        }
    }
);

// Create new category (admin)
router.post('/admin/categories', 
    AuthMiddleware.authenticate, 
    AuthMiddleware.requireAdmin, 
    async (req, res) => {
        try {
            const { error, value } = categorySchema.validate(req.body);
            if (error) {
                return res.status(400).json({
                    error: 'Validation failed',
                    message: error.details[0].message
                });
            }

            const categoryData = {
                name: value.name,
                description: value.description,
                display_order: value.displayOrder
            };

            const category = await MenuCategory.createCategory(categoryData);
            
            res.status(201).json({
                message: 'Category created successfully',
                category
            });
        } catch (error) {
            console.error('Create category error:', error);
            
            if (error.message.includes('already exists')) {
                return res.status(409).json({
                    error: 'Category exists',
                    message: error.message
                });
            }
            
            res.status(500).json({
                error: 'Failed to create category',
                message: 'Internal server error'
            });
        }
    }
);

// Update category (admin)
router.put('/admin/categories/:id', 
    AuthMiddleware.authenticate, 
    AuthMiddleware.requireAdmin, 
    async (req, res) => {
        try {
            const { id } = req.params;
            
            if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
                return res.status(400).json({
                    error: 'Invalid ID format',
                    message: 'Category ID must be a valid UUID'
                });
            }

            const { error, value } = categorySchema.validate(req.body);
            if (error) {
                return res.status(400).json({
                    error: 'Validation failed',
                    message: error.details[0].message
                });
            }

            const updateData = {
                name: value.name,
                description: value.description,
                display_order: value.displayOrder
            };

            const category = await MenuCategory.updateCategory(id, updateData);
            if (!category) {
                return res.status(404).json({
                    error: 'Category not found',
                    message: 'The requested category does not exist'
                });
            }
            
            res.json({
                message: 'Category updated successfully',
                category
            });
        } catch (error) {
            console.error('Update category error:', error);
            
            if (error.message.includes('already exists')) {
                return res.status(409).json({
                    error: 'Category exists',
                    message: error.message
                });
            }
            
            res.status(500).json({
                error: 'Failed to update category',
                message: 'Internal server error'
            });
        }
    }
);

// Delete category (admin)
router.delete('/admin/categories/:id', 
    AuthMiddleware.authenticate, 
    AuthMiddleware.requireAdmin, 
    async (req, res) => {
        try {
            const { id } = req.params;
            
            if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
                return res.status(400).json({
                    error: 'Invalid ID format',
                    message: 'Category ID must be a valid UUID'
                });
            }

            const result = await MenuCategory.deleteCategory(id);
            if (!result) {
                return res.status(404).json({
                    error: 'Category not found',
                    message: 'The requested category does not exist'
                });
            }
            
            res.json({
                message: 'Category deleted successfully'
            });
        } catch (error) {
            console.error('Delete category error:', error);
            res.status(500).json({
                error: 'Failed to delete category',
                message: 'Internal server error'
            });
        }
    }
);

// Create new menu item (admin)
router.post('/admin/items',
    AuthMiddleware.authenticate,
    AuthMiddleware.requireAdmin,
    async (req, res) => {
        try {
            const { error, value } = menuItemSchema.validate(req.body);
            if (error) {
                return res.status(400).json({
                    error: 'Validation failed',
                    message: error.details[0].message
                });
            }

            const itemData = {
                category_id: value.categoryId,
                name: value.name,
                description: value.description,
                price: value.price,
                image_url: value.imageUrl,
                is_available: value.isAvailable !== undefined ? value.isAvailable : true,
                is_featured: value.isFeatured || false,
                preparation_time: value.preparationTime || 15,
                calories: value.calories,
                allergens: value.allergens || [],
                ingredients: value.ingredients || [],
                nutritional_info: value.nutritionalInfo,
                display_order: value.displayOrder || 0,
                tags: value.tags || []
            };

            const item = await MenuItem.createMenuItem(itemData);

            res.status(201).json({
                message: 'Menu item created successfully',
                item
            });
        } catch (error) {
            console.error('Create menu item error:', error);
            res.status(500).json({
                error: 'Failed to create menu item',
                message: 'Internal server error'
            });
        }
    }
);

// Update menu item (admin)
router.put('/admin/items/:id',
    AuthMiddleware.authenticate,
    AuthMiddleware.requireAdmin,
    async (req, res) => {
        try {
            const { id } = req.params;

            if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
                return res.status(400).json({
                    error: 'Invalid ID format',
                    message: 'Menu item ID must be a valid UUID'
                });
            }

            const updateSchema = menuItemSchema.fork(['categoryId', 'name', 'price'], (schema) => schema.optional());
            const { error, value } = updateSchema.validate(req.body);
            if (error) {
                return res.status(400).json({
                    error: 'Validation failed',
                    message: error.details[0].message
                });
            }

            const updateData = {};
            if (value.categoryId) updateData.category_id = value.categoryId;
            if (value.name) updateData.name = value.name;
            if (value.description !== undefined) updateData.description = value.description;
            if (value.price) updateData.price = value.price;
            if (value.imageUrl !== undefined) updateData.image_url = value.imageUrl;
            if (value.isAvailable !== undefined) updateData.is_available = value.isAvailable;
            if (value.isFeatured !== undefined) updateData.is_featured = value.isFeatured;
            if (value.preparationTime) updateData.preparation_time = value.preparationTime;
            if (value.calories !== undefined) updateData.calories = value.calories;
            if (value.allergens) updateData.allergens = value.allergens;
            if (value.ingredients) updateData.ingredients = value.ingredients;
            if (value.nutritionalInfo) updateData.nutritional_info = value.nutritionalInfo;
            if (value.displayOrder !== undefined) updateData.display_order = value.displayOrder;
            if (value.tags) updateData.tags = value.tags;

            const item = await MenuItem.update(id, updateData);
            if (!item) {
                return res.status(404).json({
                    error: 'Menu item not found',
                    message: 'The requested menu item does not exist'
                });
            }

            res.json({
                message: 'Menu item updated successfully',
                item
            });
        } catch (error) {
            console.error('Update menu item error:', error);
            res.status(500).json({
                error: 'Failed to update menu item',
                message: 'Internal server error'
            });
        }
    }
);

// Delete menu item (admin)
router.delete('/admin/items/:id',
    AuthMiddleware.authenticate,
    AuthMiddleware.requireAdmin,
    async (req, res) => {
        try {
            const { id } = req.params;

            if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
                return res.status(400).json({
                    error: 'Invalid ID format',
                    message: 'Menu item ID must be a valid UUID'
                });
            }

            const result = await MenuItem.delete(id);
            if (!result) {
                return res.status(404).json({
                    error: 'Menu item not found',
                    message: 'The requested menu item does not exist'
                });
            }

            res.json({
                message: 'Menu item deleted successfully'
            });
        } catch (error) {
            console.error('Delete menu item error:', error);
            res.status(500).json({
                error: 'Failed to delete menu item',
                message: 'Internal server error'
            });
        }
    }
);

// Update menu item availability (admin)
router.patch('/admin/items/:id/availability',
    AuthMiddleware.authenticate,
    AuthMiddleware.requireAdmin,
    async (req, res) => {
        try {
            const { id } = req.params;
            const { isAvailable } = req.body;

            if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
                return res.status(400).json({
                    error: 'Invalid ID format',
                    message: 'Menu item ID must be a valid UUID'
                });
            }

            if (typeof isAvailable !== 'boolean') {
                return res.status(400).json({
                    error: 'Validation failed',
                    message: 'isAvailable must be a boolean value'
                });
            }

            const item = await MenuItem.updateAvailability(id, isAvailable);
            if (!item) {
                return res.status(404).json({
                    error: 'Menu item not found',
                    message: 'The requested menu item does not exist'
                });
            }

            res.json({
                message: `Menu item ${isAvailable ? 'enabled' : 'disabled'} successfully`,
                item
            });
        } catch (error) {
            console.error('Update availability error:', error);
            res.status(500).json({
                error: 'Failed to update availability',
                message: 'Internal server error'
            });
        }
    }
);

// Get menu statistics (admin)
router.get('/admin/stats',
    AuthMiddleware.authenticate,
    AuthMiddleware.requireAdmin,
    async (req, res) => {
        try {
            const [menuStats, categoryStats] = await Promise.all([
                MenuItem.getMenuStats(),
                MenuCategory.getCategoryStats()
            ]);

            res.json({
                menu: menuStats,
                categories: categoryStats
            });
        } catch (error) {
            console.error('Get menu stats error:', error);
            res.status(500).json({
                error: 'Failed to fetch menu statistics',
                message: 'Internal server error'
            });
        }
    }
);

// Bulk update item availability (admin)
router.patch('/admin/items/bulk/availability',
    AuthMiddleware.authenticate,
    AuthMiddleware.requireAdmin,
    async (req, res) => {
        try {
            const { itemIds, isAvailable } = req.body;

            if (!Array.isArray(itemIds) || itemIds.length === 0) {
                return res.status(400).json({
                    error: 'Validation failed',
                    message: 'itemIds must be a non-empty array'
                });
            }

            if (typeof isAvailable !== 'boolean') {
                return res.status(400).json({
                    error: 'Validation failed',
                    message: 'isAvailable must be a boolean value'
                });
            }

            // Validate all IDs are UUIDs
            for (const id of itemIds) {
                if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
                    return res.status(400).json({
                        error: 'Invalid ID format',
                        message: 'All item IDs must be valid UUIDs'
                    });
                }
            }

            await MenuItem.bulkUpdateAvailability(itemIds, isAvailable);

            res.json({
                message: `${itemIds.length} items updated`,
                processedCount: itemIds.length,
                isAvailable
            });

        } catch (error) {
            console.error('Bulk availability update error:', error);
            res.status(500).json({
                error: 'Failed to bulk update items',
                message: 'Internal server error'
            });
        }
    }
);

// Bulk delete items (admin)
router.delete('/admin/items/bulk',
    AuthMiddleware.authenticate,
    AuthMiddleware.requireAdmin,
    async (req, res) => {
        try {
            const { itemIds } = req.body;

            if (!Array.isArray(itemIds) || itemIds.length === 0) {
                return res.status(400).json({
                    error: 'Validation failed',
                    message: 'itemIds must be a non-empty array'
                });
            }

            // Validate all IDs are UUIDs
            for (const id of itemIds) {
                if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
                    return res.status(400).json({
                        error: 'Invalid ID format',
                        message: 'All item IDs must be valid UUIDs'
                    });
                }
            }

            await MenuItem.bulkDelete(itemIds);

            res.json({
                message: `${itemIds.length} items deleted`,
                deletedCount: itemIds.length
            });

        } catch (error) {
            console.error('Bulk delete error:', error);
            res.status(500).json({
                error: 'Failed to bulk delete items',
                message: 'Internal server error'
            });
        }
    }
);

module.exports = router;
