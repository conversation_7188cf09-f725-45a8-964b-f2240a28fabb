const fs = require('fs').promises;
const path = require('path');
const db = require('../utils/DatabaseSecurity');
require('dotenv').config();

class DatabaseMigrator {
    constructor() {
        this.migrationsPath = path.join(__dirname, 'migrations');
        this.schemaPath = path.join(__dirname, 'schema.sql');
    }

    async createMigrationsTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS migrations (
                id SERIAL PRIMARY KEY,
                filename VARCHAR(255) NOT NULL UNIQUE,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `;
        
        try {
            await db.query(query);
            console.log('✓ Migrations table created/verified');
        } catch (error) {
            console.error('✗ Failed to create migrations table:', error.message);
            throw error;
        }
    }

    async getExecutedMigrations() {
        try {
            const result = await db.query('SELECT filename FROM migrations ORDER BY id');
            return result.map(row => row.filename);
        } catch (error) {
            console.error('✗ Failed to get executed migrations:', error.message);
            return [];
        }
    }

    async executeMigration(filename, sql) {
        const client = await db.pool.connect();
        
        try {
            await client.query('BEGIN');
            
            // Execute the migration SQL
            await client.query(sql);
            
            // Record the migration as executed
            await client.query(
                'INSERT INTO migrations (filename) VALUES ($1)',
                [filename]
            );
            
            await client.query('COMMIT');
            console.log(`✓ Executed migration: ${filename}`);
            
        } catch (error) {
            await client.query('ROLLBACK');
            console.error(`✗ Failed to execute migration ${filename}:`, error.message);
            throw error;
        } finally {
            client.release();
        }
    }

    async runInitialSchema() {
        try {
            console.log('🚀 Running initial schema setup...');
            
            const schemaSQL = await fs.readFile(this.schemaPath, 'utf8');
            await this.executeMigration('001_initial_schema.sql', schemaSQL);
            
            console.log('✅ Initial schema setup completed');
        } catch (error) {
            console.error('❌ Initial schema setup failed:', error.message);
            throw error;
        }
    }

    async runMigrations() {
        try {
            console.log('🔄 Starting database migrations...');
            
            // Test database connection
            const isConnected = await db.testConnection();
            if (!isConnected) {
                throw new Error('Database connection failed');
            }

            // Create migrations table
            await this.createMigrationsTable();

            // Get executed migrations
            const executedMigrations = await this.getExecutedMigrations();
            console.log(`📋 Found ${executedMigrations.length} executed migrations`);

            // Check if initial schema needs to be run
            if (!executedMigrations.includes('001_initial_schema.sql')) {
                await this.runInitialSchema();
            }

            // Check for additional migration files
            try {
                const migrationFiles = await fs.readdir(this.migrationsPath);
                const pendingMigrations = migrationFiles
                    .filter(file => file.endsWith('.sql'))
                    .filter(file => !executedMigrations.includes(file))
                    .sort();

                if (pendingMigrations.length === 0) {
                    console.log('✅ No pending migrations');
                    return;
                }

                console.log(`📝 Found ${pendingMigrations.length} pending migrations`);

                for (const filename of pendingMigrations) {
                    const filePath = path.join(this.migrationsPath, filename);
                    const sql = await fs.readFile(filePath, 'utf8');
                    await this.executeMigration(filename, sql);
                }

                console.log('✅ All migrations completed successfully');

            } catch (error) {
                if (error.code === 'ENOENT') {
                    console.log('📁 No additional migrations directory found');
                } else {
                    throw error;
                }
            }

        } catch (error) {
            console.error('❌ Migration failed:', error.message);
            process.exit(1);
        }
    }

    async rollback(steps = 1) {
        try {
            console.log(`🔄 Rolling back ${steps} migration(s)...`);
            
            const executedMigrations = await db.query(
                'SELECT filename FROM migrations ORDER BY id DESC LIMIT $1',
                [steps]
            );

            for (const migration of executedMigrations) {
                // Remove from migrations table
                await db.query(
                    'DELETE FROM migrations WHERE filename = $1',
                    [migration.filename]
                );
                console.log(`✓ Rolled back: ${migration.filename}`);
            }

            console.log('✅ Rollback completed');
        } catch (error) {
            console.error('❌ Rollback failed:', error.message);
            throw error;
        }
    }

    async reset() {
        try {
            console.log('🔄 Resetting database...');
            
            // Drop all tables (be careful!)
            const dropTablesQuery = `
                DROP SCHEMA public CASCADE;
                CREATE SCHEMA public;
                GRANT ALL ON SCHEMA public TO ${process.env.DB_USER};
                GRANT ALL ON SCHEMA public TO public;
            `;
            
            await db.query(dropTablesQuery);
            console.log('✓ Database reset completed');
            
            // Run migrations again
            await this.runMigrations();
            
        } catch (error) {
            console.error('❌ Database reset failed:', error.message);
            throw error;
        }
    }
}

// CLI interface
async function main() {
    const migrator = new DatabaseMigrator();
    const command = process.argv[2];

    try {
        switch (command) {
            case 'up':
            case undefined:
                await migrator.runMigrations();
                break;
            case 'rollback':
                const steps = parseInt(process.argv[3]) || 1;
                await migrator.rollback(steps);
                break;
            case 'reset':
                await migrator.reset();
                break;
            default:
                console.log('Usage: node migrate.js [up|rollback|reset]');
                console.log('  up (default): Run pending migrations');
                console.log('  rollback [n]: Rollback n migrations (default: 1)');
                console.log('  reset: Drop all tables and re-run migrations');
        }
    } catch (error) {
        console.error('Migration command failed:', error.message);
        process.exit(1);
    } finally {
        await db.close();
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = DatabaseMigrator;
