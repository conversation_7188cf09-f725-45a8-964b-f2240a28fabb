{"name": "magic-menu", "version": "1.0.0", "description": "A modern restaurant website with online ordering capabilities", "main": "server/index.js", "scripts": {"start": "node server/index.js", "dev": "nodemon server/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "db:migrate": "node server/database/migrate.js", "db:seed": "node server/database/seed.js", "db:reset": "node server/database/reset.js"}, "dependencies": {"express": "^4.18.2", "pg": "^8.11.3", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "redis": "^4.6.8", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "express-slow-down": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "winston": "^3.10.0", "winston-elasticsearch": "^0.17.4", "sql-template-strings": "^2.2.2", "joi": "^17.9.2", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "cypress": "^12.17.4"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["restaurant", "food", "ordering", "nodejs", "express", "postgresql"], "author": "Magic Menu Team", "license": "MIT"}