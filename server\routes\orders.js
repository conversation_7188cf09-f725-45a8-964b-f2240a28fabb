const express = require('express');
const Joi = require('joi');
const Order = require('../models/Order');
const Cart = require('../models/Cart');
const AuthMiddleware = require('../middleware/auth');

const router = express.Router();

// Validation schemas
const createOrderSchema = Joi.object({
    customerInfo: Joi.object({
        name: Joi.string().min(2).max(100).required(),
        email: Joi.string().email().required(),
        phone: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).required(),
        address: Joi.object({
            street: Joi.string().required(),
            city: Joi.string().required(),
            state: Joi.string().required(),
            postalCode: Joi.string().optional(),
            country: Joi.string().default('Nigeria')
        }).required(),
        specialInstructions: Joi.string().max(500).optional().allow('')
    }).required(),
    paymentInfo: Joi.object({
        method: Joi.string().valid('cash', 'card', 'transfer', 'wallet').required(),
        reference: Joi.string().optional()
    }).optional()
});

const updateStatusSchema = Joi.object({
    status: Joi.string().valid('pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled').required()
});

// Helper function to get cart for order creation
async function getCartForOrder(req) {
    if (req.user) {
        const cart = await Cart.getOrCreateUserCart(req.user.id);
        return await Cart.getCartWithItems(cart.id);
    } else {
        if (!req.session.guestId) {
            throw new Error('No cart found');
        }
        const cart = await Cart.getOrCreateSessionCart(req.session.guestId);
        return await Cart.getCartWithItems(cart.id);
    }
}

// Create new order
router.post('/', AuthMiddleware.optionalAuth, AuthMiddleware.getSessionUser, async (req, res) => {
    try {
        const { error, value } = createOrderSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                error: 'Validation failed',
                message: error.details[0].message
            });
        }

        const { customerInfo, paymentInfo } = value;

        // Get cart data
        const cartData = await getCartForOrder(req);
        
        if (!cartData || !cartData.items || cartData.items.length === 0) {
            return res.status(400).json({
                error: 'Empty cart',
                message: 'Cannot create order with empty cart'
            });
        }

        // Validate all items are still available
        for (const item of cartData.items) {
            if (!item.menu_item.is_available) {
                return res.status(400).json({
                    error: 'Item unavailable',
                    message: `${item.menu_item.name} is no longer available`
                });
            }
        }

        // Add user ID to customer info if authenticated
        const orderCustomerInfo = {
            ...customerInfo,
            userId: req.user ? req.user.id : null
        };

        // Create order
        const order = await Order.createOrderFromCart(cartData, orderCustomerInfo, paymentInfo);

        // Clear cart after successful order creation
        await Cart.clearCart(cartData.id);

        // Clear guest session if applicable
        if (req.session.guestId) {
            delete req.session.guestId;
        }

        res.status(201).json({
            message: 'Order created successfully',
            order: {
                id: order.id,
                orderNumber: order.order_number,
                status: order.status,
                paymentStatus: order.payment_status,
                totalAmount: order.total_amount,
                estimatedDeliveryTime: order.estimated_delivery_time,
                items: order.items,
                createdAt: order.created_at
            }
        });

    } catch (error) {
        console.error('Create order error:', error);
        res.status(500).json({
            error: 'Failed to create order',
            message: 'Internal server error'
        });
    }
});

// Get order by ID
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
            return res.status(400).json({
                error: 'Invalid ID format',
                message: 'Order ID must be a valid UUID'
            });
        }

        const order = await Order.getOrderWithItems(id);
        if (!order) {
            return res.status(404).json({
                error: 'Order not found',
                message: 'The requested order does not exist'
            });
        }

        // Check if user can access this order
        if (req.user && order.user_id && order.user_id !== req.user.id && req.user.role !== 'admin') {
            return res.status(403).json({
                error: 'Access denied',
                message: 'You can only view your own orders'
            });
        }

        res.json({
            order: {
                id: order.id,
                orderNumber: order.order_number,
                status: order.status,
                paymentStatus: order.payment_status,
                customerName: order.customer_name,
                customerEmail: order.customer_email,
                customerPhone: order.customer_phone,
                deliveryAddress: order.delivery_address,
                subtotal: order.subtotal,
                taxAmount: order.tax_amount,
                deliveryFee: order.delivery_fee,
                totalAmount: order.total_amount,
                specialInstructions: order.special_instructions,
                estimatedDeliveryTime: order.estimated_delivery_time,
                deliveredAt: order.delivered_at,
                items: order.items,
                createdAt: order.created_at,
                updatedAt: order.updated_at
            }
        });

    } catch (error) {
        console.error('Get order error:', error);
        res.status(500).json({
            error: 'Failed to fetch order',
            message: 'Internal server error'
        });
    }
});

// Get order by order number (public endpoint for order tracking)
router.get('/track/:orderNumber', async (req, res) => {
    try {
        const { orderNumber } = req.params;
        
        const order = await Order.findOne({ order_number: orderNumber });
        if (!order) {
            return res.status(404).json({
                error: 'Order not found',
                message: 'No order found with this order number'
            });
        }

        const orderWithItems = await Order.getOrderWithItems(order.id);

        res.json({
            order: {
                orderNumber: orderWithItems.order_number,
                status: orderWithItems.status,
                estimatedDeliveryTime: orderWithItems.estimated_delivery_time,
                deliveredAt: orderWithItems.delivered_at,
                totalAmount: orderWithItems.total_amount,
                items: orderWithItems.items.map(item => ({
                    name: item.menu_item_name,
                    quantity: item.quantity,
                    totalPrice: item.total_price
                })),
                createdAt: orderWithItems.created_at
            }
        });

    } catch (error) {
        console.error('Track order error:', error);
        res.status(500).json({
            error: 'Failed to track order',
            message: 'Internal server error'
        });
    }
});

// Get user's order history
router.get('/user/history', AuthMiddleware.authenticate, async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        
        const result = await Order.getOrdersByUser(
            req.user.id, 
            parseInt(page), 
            parseInt(limit)
        );

        const formattedOrders = result.orders.map(order => ({
            id: order.id,
            orderNumber: order.order_number,
            status: order.status,
            paymentStatus: order.payment_status,
            totalAmount: order.total_amount,
            itemCount: order.items ? order.items.length : 0,
            estimatedDeliveryTime: order.estimated_delivery_time,
            deliveredAt: order.delivered_at,
            createdAt: order.created_at
        }));

        res.json({
            orders: formattedOrders,
            pagination: result.pagination
        });

    } catch (error) {
        console.error('Get order history error:', error);
        res.status(500).json({
            error: 'Failed to fetch order history',
            message: 'Internal server error'
        });
    }
});

// ADMIN ROUTES

// Get all orders (admin)
router.get('/admin/all', 
    AuthMiddleware.authenticate, 
    AuthMiddleware.requireAdmin, 
    async (req, res) => {
        try {
            const { 
                page = 1, 
                limit = 20, 
                status, 
                search,
                startDate,
                endDate 
            } = req.query;

            let result;
            
            if (search) {
                result = await Order.searchOrders(search, status, parseInt(page), parseInt(limit));
            } else {
                const conditions = {};
                if (status) conditions.status = status;
                
                result = await Order.paginate(
                    parseInt(page), 
                    parseInt(limit), 
                    conditions, 
                    'created_at DESC'
                );
                
                // Get items for each order
                for (let order of result.data) {
                    const orderWithItems = await Order.getOrderWithItems(order.id);
                    order.items = orderWithItems.items;
                }
            }

            res.json({
                orders: result.orders || result.data,
                pagination: result.pagination
            });

        } catch (error) {
            console.error('Get all orders error:', error);
            res.status(500).json({
                error: 'Failed to fetch orders',
                message: 'Internal server error'
            });
        }
    }
);

// Update order status (admin)
router.patch('/:id/status', 
    AuthMiddleware.authenticate, 
    AuthMiddleware.requireAdmin, 
    async (req, res) => {
        try {
            const { id } = req.params;
            
            if (!id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
                return res.status(400).json({
                    error: 'Invalid ID format',
                    message: 'Order ID must be a valid UUID'
                });
            }

            const { error, value } = updateStatusSchema.validate(req.body);
            if (error) {
                return res.status(400).json({
                    error: 'Validation failed',
                    message: error.details[0].message
                });
            }

            const { status } = value;
            
            const updatedOrder = await Order.updateOrderStatus(id, status, req.user.id);
            if (!updatedOrder) {
                return res.status(404).json({
                    error: 'Order not found',
                    message: 'The requested order does not exist'
                });
            }

            res.json({
                message: `Order status updated to ${status}`,
                order: {
                    id: updatedOrder.id,
                    orderNumber: updatedOrder.order_number,
                    status: updatedOrder.status,
                    updatedAt: updatedOrder.updated_at
                }
            });

        } catch (error) {
            console.error('Update order status error:', error);
            res.status(500).json({
                error: 'Failed to update order status',
                message: 'Internal server error'
            });
        }
    }
);

// Get order statistics (admin)
router.get('/admin/stats', 
    AuthMiddleware.authenticate, 
    AuthMiddleware.requireAdmin, 
    async (req, res) => {
        try {
            const { startDate, endDate } = req.query;
            
            const stats = await Order.getOrderStats(startDate, endDate);
            
            res.json({
                stats: {
                    totalOrders: parseInt(stats.total_orders),
                    pendingOrders: parseInt(stats.pending_orders),
                    confirmedOrders: parseInt(stats.confirmed_orders),
                    preparingOrders: parseInt(stats.preparing_orders),
                    readyOrders: parseInt(stats.ready_orders),
                    deliveredOrders: parseInt(stats.delivered_orders),
                    cancelledOrders: parseInt(stats.cancelled_orders),
                    paidOrders: parseInt(stats.paid_orders),
                    totalRevenue: parseFloat(stats.total_revenue || 0),
                    averageOrderValue: parseFloat(stats.average_order_value || 0),
                    ordersLast30Days: parseInt(stats.orders_last_30_days)
                }
            });

        } catch (error) {
            console.error('Get order stats error:', error);
            res.status(500).json({
                error: 'Failed to fetch order statistics',
                message: 'Internal server error'
            });
        }
    }
);

module.exports = router;
