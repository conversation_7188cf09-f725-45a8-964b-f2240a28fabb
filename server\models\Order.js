const BaseModel = require('./BaseModel');
const { CURRENCY_CONFIG } = require('../../assets/scripts/config/currency.js');

class Order extends BaseModel {
    constructor() {
        super('orders');
    }

    // Generate unique order number
    generateOrderNumber() {
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `MM${timestamp.slice(-6)}${random}`;
    }

    // Create order from cart
    async createOrderFromCart(cartData, customerInfo, paymentInfo = {}) {
        const client = await this.db.pool.connect();
        
        try {
            await client.query('BEGIN');
            
            // Calculate totals
            const subtotal = cartData.items.reduce((sum, item) => {
                return sum + (parseFloat(item.unit_price) * item.quantity);
            }, 0);
            
            const taxAmount = subtotal * CURRENCY_CONFIG.vatRate;
            const deliveryFee = CURRENCY_CONFIG.deliveryFee;
            const totalAmount = subtotal + taxAmount + deliveryFee;
            
            // Create order
            const orderData = {
                order_number: this.generateOrderNumber(),
                user_id: customerInfo.userId || null,
                customer_name: customerInfo.name,
                customer_email: customerInfo.email,
                customer_phone: customerInfo.phone,
                delivery_address: customerInfo.address,
                subtotal: parseFloat(subtotal.toFixed(2)),
                tax_amount: parseFloat(taxAmount.toFixed(2)),
                delivery_fee: parseFloat(deliveryFee.toFixed(2)),
                total_amount: parseFloat(totalAmount.toFixed(2)),
                status: 'pending',
                payment_status: 'pending',
                payment_method: paymentInfo.method || null,
                payment_reference: paymentInfo.reference || null,
                special_instructions: customerInfo.specialInstructions || null,
                estimated_delivery_time: this.calculateEstimatedDeliveryTime(cartData.items)
            };
            
            const orderResult = await client.query(
                `INSERT INTO orders (
                    order_number, user_id, customer_name, customer_email, customer_phone,
                    delivery_address, subtotal, tax_amount, delivery_fee, total_amount,
                    status, payment_status, payment_method, payment_reference,
                    special_instructions, estimated_delivery_time
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                RETURNING *`,
                [
                    orderData.order_number, orderData.user_id, orderData.customer_name,
                    orderData.customer_email, orderData.customer_phone, orderData.delivery_address,
                    orderData.subtotal, orderData.tax_amount, orderData.delivery_fee,
                    orderData.total_amount, orderData.status, orderData.payment_status,
                    orderData.payment_method, orderData.payment_reference,
                    orderData.special_instructions, orderData.estimated_delivery_time
                ]
            );
            
            const order = orderResult.rows[0];
            
            // Create order items
            for (const cartItem of cartData.items) {
                const totalPrice = parseFloat(cartItem.unit_price) * cartItem.quantity;
                
                await client.query(
                    `INSERT INTO order_items (
                        order_id, menu_item_id, menu_item_name, quantity,
                        unit_price, total_price, special_instructions
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
                    [
                        order.id,
                        cartItem.menu_item_id,
                        cartItem.menu_item.name,
                        cartItem.quantity,
                        cartItem.unit_price,
                        totalPrice,
                        cartItem.special_instructions
                    ]
                );
            }
            
            await client.query('COMMIT');
            
            // Return order with items
            return await this.getOrderWithItems(order.id);
            
        } catch (error) {
            await client.query('ROLLBACK');
            console.error('Error creating order:', error);
            throw error;
        } finally {
            client.release();
        }
    }

    // Calculate estimated delivery time
    calculateEstimatedDeliveryTime(items) {
        const maxPrepTime = Math.max(...items.map(item => item.menu_item.preparation_time || 15));
        const deliveryTime = 30; // 30 minutes delivery time
        const totalTime = maxPrepTime + deliveryTime;
        
        const estimatedTime = new Date();
        estimatedTime.setMinutes(estimatedTime.getMinutes() + totalTime);
        
        return estimatedTime;
    }

    // Get order with items
    async getOrderWithItems(orderId) {
        try {
            const query = `
                SELECT 
                    o.*,
                    json_agg(
                        json_build_object(
                            'id', oi.id,
                            'menu_item_id', oi.menu_item_id,
                            'menu_item_name', oi.menu_item_name,
                            'quantity', oi.quantity,
                            'unit_price', oi.unit_price,
                            'total_price', oi.total_price,
                            'special_instructions', oi.special_instructions
                        ) ORDER BY oi.created_at
                    ) as items
                FROM orders o
                LEFT JOIN order_items oi ON o.id = oi.order_id
                WHERE o.id = $1
                GROUP BY o.id
            `;
            
            const result = await this.db.query(query, [orderId]);
            return result[0] || null;
        } catch (error) {
            console.error('Error getting order with items:', error);
            throw error;
        }
    }

    // Get orders by user
    async getOrdersByUser(userId, page = 1, limit = 10) {
        try {
            const offset = (page - 1) * limit;
            
            const query = `
                SELECT 
                    o.*,
                    json_agg(
                        json_build_object(
                            'id', oi.id,
                            'menu_item_id', oi.menu_item_id,
                            'menu_item_name', oi.menu_item_name,
                            'quantity', oi.quantity,
                            'unit_price', oi.unit_price,
                            'total_price', oi.total_price,
                            'special_instructions', oi.special_instructions
                        ) ORDER BY oi.created_at
                    ) as items
                FROM orders o
                LEFT JOIN order_items oi ON o.id = oi.order_id
                WHERE o.user_id = $1
                GROUP BY o.id
                ORDER BY o.created_at DESC
                LIMIT $2 OFFSET $3
            `;
            
            const [orders, totalCount] = await Promise.all([
                this.db.query(query, [userId, limit, offset]),
                this.count({ user_id: userId })
            ]);
            
            return {
                orders,
                pagination: {
                    page,
                    limit,
                    total: totalCount,
                    pages: Math.ceil(totalCount / limit),
                    hasNext: page < Math.ceil(totalCount / limit),
                    hasPrev: page > 1
                }
            };
        } catch (error) {
            console.error('Error getting orders by user:', error);
            throw error;
        }
    }

    // Update order status
    async updateOrderStatus(orderId, status, adminId = null) {
        try {
            const validStatuses = ['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled'];
            if (!validStatuses.includes(status)) {
                throw new Error('Invalid order status');
            }

            const updateData = { status };
            
            if (status === 'delivered') {
                updateData.delivered_at = new Date();
            }

            const updatedOrder = await this.update(orderId, updateData);
            
            // Log status change (in a real app, you'd have an order_status_history table)
            console.log(`Order ${orderId} status changed to ${status} by ${adminId || 'system'}`);
            
            return updatedOrder;
        } catch (error) {
            console.error('Error updating order status:', error);
            throw error;
        }
    }

    // Update payment status
    async updatePaymentStatus(orderId, paymentStatus, paymentReference = null) {
        try {
            const validStatuses = ['pending', 'completed', 'failed', 'refunded'];
            if (!validStatuses.includes(paymentStatus)) {
                throw new Error('Invalid payment status');
            }

            const updateData = { payment_status: paymentStatus };
            if (paymentReference) {
                updateData.payment_reference = paymentReference;
            }

            return await this.update(orderId, updateData);
        } catch (error) {
            console.error('Error updating payment status:', error);
            throw error;
        }
    }

    // Get order statistics
    async getOrderStats(startDate = null, endDate = null) {
        try {
            let dateFilter = '';
            const params = [];
            
            if (startDate && endDate) {
                dateFilter = 'WHERE created_at BETWEEN $1 AND $2';
                params.push(startDate, endDate);
            }
            
            const query = `
                SELECT 
                    COUNT(*) as total_orders,
                    COUNT(*) FILTER (WHERE status = 'pending') as pending_orders,
                    COUNT(*) FILTER (WHERE status = 'confirmed') as confirmed_orders,
                    COUNT(*) FILTER (WHERE status = 'preparing') as preparing_orders,
                    COUNT(*) FILTER (WHERE status = 'ready') as ready_orders,
                    COUNT(*) FILTER (WHERE status = 'delivered') as delivered_orders,
                    COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_orders,
                    COUNT(*) FILTER (WHERE payment_status = 'completed') as paid_orders,
                    SUM(total_amount) FILTER (WHERE payment_status = 'completed') as total_revenue,
                    AVG(total_amount) FILTER (WHERE payment_status = 'completed') as average_order_value,
                    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as orders_last_30_days
                FROM orders
                ${dateFilter}
            `;
            
            const result = await this.db.query(query, params);
            return result[0];
        } catch (error) {
            console.error('Error getting order stats:', error);
            throw error;
        }
    }

    // Search orders (admin function)
    async searchOrders(searchTerm, status = null, page = 1, limit = 20) {
        try {
            const offset = (page - 1) * limit;
            let query = `
                SELECT 
                    o.*,
                    json_agg(
                        json_build_object(
                            'id', oi.id,
                            'menu_item_name', oi.menu_item_name,
                            'quantity', oi.quantity,
                            'total_price', oi.total_price
                        ) ORDER BY oi.created_at
                    ) as items
                FROM orders o
                LEFT JOIN order_items oi ON o.id = oi.order_id
                WHERE (
                    o.order_number ILIKE $1 OR
                    o.customer_name ILIKE $1 OR
                    o.customer_email ILIKE $1 OR
                    o.customer_phone ILIKE $1
                )
            `;
            
            const params = [`%${searchTerm}%`];
            
            if (status) {
                query += ` AND o.status = $${params.length + 1}`;
                params.push(status);
            }
            
            query += ` GROUP BY o.id ORDER BY o.created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
            params.push(limit, offset);
            
            const orders = await this.db.query(query, params);
            
            return {
                orders,
                pagination: {
                    page,
                    limit,
                    total: orders.length // This is approximate - in production you'd do a separate count query
                }
            };
        } catch (error) {
            console.error('Error searching orders:', error);
            throw error;
        }
    }
}

module.exports = new Order();
