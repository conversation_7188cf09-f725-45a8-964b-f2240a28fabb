const axios = require('axios');
require('dotenv').config();

class APITester {
    constructor() {
        this.baseURL = process.env.API_BASE_URL || 'http://localhost:3000/api';
        this.tokens = {};
        this.testData = {};
        this.results = {
            passed: 0,
            failed: 0,
            errors: []
        };
    }

    async runTest(testName, testFunction) {
        try {
            console.log(`🧪 Testing: ${testName}`);
            await testFunction();
            console.log(`✅ ${testName} - PASSED`);
            this.results.passed++;
        } catch (error) {
            console.error(`❌ ${testName} - FAILED: ${error.message}`);
            this.results.failed++;
            this.results.errors.push({ test: testName, error: error.message });
        }
    }

    async request(method, endpoint, data = null, headers = {}) {
        try {
            const config = {
                method,
                url: `${this.baseURL}${endpoint}`,
                headers: {
                    'Content-Type': 'application/json',
                    ...headers
                }
            };

            if (data) {
                config.data = data;
            }

            const response = await axios(config);
            return response.data;
        } catch (error) {
            if (error.response) {
                throw new Error(`${error.response.status}: ${error.response.data.message || error.response.data.error}`);
            }
            throw error;
        }
    }

    async testHealthCheck() {
        const response = await axios.get('http://localhost:3000/health');
        if (response.data.status !== 'ok') {
            throw new Error('Health check failed');
        }
    }

    async testUserRegistration() {
        const userData = {
            email: '<EMAIL>',
            password: 'TestPassword123!',
            firstName: 'Test',
            lastName: 'User',
            phone: '+234-************'
        };

        const response = await this.request('POST', '/auth/register', userData);
        
        if (!response.user || !response.tokens) {
            throw new Error('Registration response missing required fields');
        }

        this.tokens.customer = response.tokens.accessToken;
        this.testData.customerId = response.user.id;
    }

    async testUserLogin() {
        const loginData = {
            email: '<EMAIL>',
            password: process.env.ADMIN_PASSWORD || 'SecureAdminPassword123!'
        };

        const response = await this.request('POST', '/auth/login', loginData);
        
        if (!response.user || !response.tokens) {
            throw new Error('Login response missing required fields');
        }

        if (response.user.role !== 'admin') {
            throw new Error('Admin login failed - wrong role');
        }

        this.tokens.admin = response.tokens.accessToken;
    }

    async testGetProfile() {
        const headers = { Authorization: `Bearer ${this.tokens.customer}` };
        const response = await this.request('GET', '/auth/profile', null, headers);
        
        if (!response.user || response.user.email !== '<EMAIL>') {
            throw new Error('Profile data incorrect');
        }
    }

    async testGetMenuCategories() {
        const response = await this.request('GET', '/menu/categories');
        
        if (!response.categories || !Array.isArray(response.categories)) {
            throw new Error('Categories response invalid');
        }

        if (response.categories.length === 0) {
            throw new Error('No categories found');
        }

        this.testData.categoryId = response.categories[0].id;
    }

    async testGetMenuItems() {
        const response = await this.request('GET', '/menu/items');
        
        if (!response.items || !Array.isArray(response.items)) {
            throw new Error('Menu items response invalid');
        }

        if (response.items.length === 0) {
            throw new Error('No menu items found');
        }

        this.testData.menuItemId = response.items[0].id;
    }

    async testSearchMenuItems() {
        const response = await this.request('GET', '/menu/items?search=jollof');
        
        if (!response.items || !Array.isArray(response.items)) {
            throw new Error('Search response invalid');
        }
    }

    async testAddToCart() {
        const cartData = {
            menuItemId: this.testData.menuItemId,
            quantity: 2,
            specialInstructions: 'Test instructions'
        };

        const headers = { Authorization: `Bearer ${this.tokens.customer}` };
        const response = await this.request('POST', '/cart/add', cartData, headers);
        
        if (!response.cart || !response.cart.items) {
            throw new Error('Add to cart response invalid');
        }

        if (response.cart.items.length === 0) {
            throw new Error('Item not added to cart');
        }
    }

    async testGetCart() {
        const headers = { Authorization: `Bearer ${this.tokens.customer}` };
        const response = await this.request('GET', '/cart', null, headers);
        
        if (!response.cart) {
            throw new Error('Cart response invalid');
        }

        if (!response.cart.totals || typeof response.cart.totals.total !== 'number') {
            throw new Error('Cart totals invalid');
        }
    }

    async testUpdateCartItem() {
        const updateData = { quantity: 3 };
        const headers = { Authorization: `Bearer ${this.tokens.customer}` };
        
        const response = await this.request(
            'PUT', 
            `/cart/items/${this.testData.menuItemId}`, 
            updateData, 
            headers
        );
        
        if (!response.cart) {
            throw new Error('Update cart response invalid');
        }
    }

    async testCreateOrder() {
        const orderData = {
            customerInfo: {
                name: 'Test User',
                email: '<EMAIL>',
                phone: '+234-************',
                address: {
                    street: '123 Test Street',
                    city: 'Lagos',
                    state: 'Lagos',
                    postalCode: '100001',
                    country: 'Nigeria'
                },
                specialInstructions: 'Test order'
            },
            paymentInfo: {
                method: 'cash'
            }
        };

        const headers = { Authorization: `Bearer ${this.tokens.customer}` };
        const response = await this.request('POST', '/orders', orderData, headers);
        
        if (!response.order || !response.order.orderNumber) {
            throw new Error('Order creation response invalid');
        }

        this.testData.orderId = response.order.id;
        this.testData.orderNumber = response.order.orderNumber;
    }

    async testGetOrder() {
        const headers = { Authorization: `Bearer ${this.tokens.customer}` };
        const response = await this.request('GET', `/orders/${this.testData.orderId}`, null, headers);
        
        if (!response.order || response.order.id !== this.testData.orderId) {
            throw new Error('Get order response invalid');
        }
    }

    async testTrackOrder() {
        const response = await this.request('GET', `/orders/track/${this.testData.orderNumber}`);
        
        if (!response.order || response.order.orderNumber !== this.testData.orderNumber) {
            throw new Error('Track order response invalid');
        }
    }

    async testAdminDashboard() {
        const headers = { Authorization: `Bearer ${this.tokens.admin}` };
        const response = await this.request('GET', '/admin/dashboard', null, headers);
        
        if (!response.dashboard || !response.dashboard.users || !response.dashboard.orders) {
            throw new Error('Admin dashboard response invalid');
        }
    }

    async testAdminGetUsers() {
        const headers = { Authorization: `Bearer ${this.tokens.admin}` };
        const response = await this.request('GET', '/admin/users', null, headers);
        
        if (!response.users || !Array.isArray(response.users)) {
            throw new Error('Admin get users response invalid');
        }
    }

    async testAdminUpdateOrderStatus() {
        const updateData = { status: 'confirmed' };
        const headers = { Authorization: `Bearer ${this.tokens.admin}` };
        
        const response = await this.request(
            'PATCH', 
            `/orders/${this.testData.orderId}/status`, 
            updateData, 
            headers
        );
        
        if (!response.order || response.order.status !== 'confirmed') {
            throw new Error('Admin update order status failed');
        }
    }

    async testContactSubmission() {
        const contactData = {
            name: 'Test Contact',
            email: '<EMAIL>',
            phone: '+234-************',
            subject: 'Test Inquiry',
            message: 'This is a test contact form submission.',
            inquiryType: 'general'
        };

        const response = await this.request('POST', '/contact/submit', contactData);
        
        if (!response.submissionId) {
            throw new Error('Contact submission response invalid');
        }

        this.testData.contactId = response.submissionId;
    }

    async testAdminGetContactSubmissions() {
        const headers = { Authorization: `Bearer ${this.tokens.admin}` };
        const response = await this.request('GET', '/contact/admin/submissions', null, headers);
        
        if (!response.submissions || !Array.isArray(response.submissions)) {
            throw new Error('Admin get contact submissions response invalid');
        }
    }

    async runAllTests() {
        console.log('🚀 Starting comprehensive API tests...\n');

        // Health and basic tests
        await this.runTest('Health Check', () => this.testHealthCheck());
        
        // Authentication tests
        await this.runTest('User Registration', () => this.testUserRegistration());
        await this.runTest('Admin Login', () => this.testUserLogin());
        await this.runTest('Get User Profile', () => this.testGetProfile());
        
        // Menu tests
        await this.runTest('Get Menu Categories', () => this.testGetMenuCategories());
        await this.runTest('Get Menu Items', () => this.testGetMenuItems());
        await this.runTest('Search Menu Items', () => this.testSearchMenuItems());
        
        // Cart tests
        await this.runTest('Add Item to Cart', () => this.testAddToCart());
        await this.runTest('Get Cart', () => this.testGetCart());
        await this.runTest('Update Cart Item', () => this.testUpdateCartItem());
        
        // Order tests
        await this.runTest('Create Order', () => this.testCreateOrder());
        await this.runTest('Get Order', () => this.testGetOrder());
        await this.runTest('Track Order', () => this.testTrackOrder());
        
        // Admin tests
        await this.runTest('Admin Dashboard', () => this.testAdminDashboard());
        await this.runTest('Admin Get Users', () => this.testAdminGetUsers());
        await this.runTest('Admin Update Order Status', () => this.testAdminUpdateOrderStatus());
        
        // Contact tests
        await this.runTest('Contact Form Submission', () => this.testContactSubmission());
        await this.runTest('Admin Get Contact Submissions', () => this.testAdminGetContactSubmissions());

        console.log('\n📊 Test Results Summary:');
        console.log(`✅ Passed: ${this.results.passed}`);
        console.log(`❌ Failed: ${this.results.failed}`);
        console.log(`📈 Success Rate: ${((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)}%`);

        if (this.results.failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.results.errors.forEach(error => {
                console.log(`  - ${error.test}: ${error.error}`);
            });
        } else {
            console.log('\n🎉 All API tests passed successfully!');
        }

        return this.results.failed === 0;
    }
}

// CLI interface
async function main() {
    const tester = new APITester();
    const testSuite = process.argv[2];

    try {
        if (testSuite) {
            console.log(`Running ${testSuite} test suite...`);
            // Run specific test suite (implementation would go here)
        } else {
            await tester.runAllTests();
        }
    } catch (error) {
        console.error('API testing failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = APITester;
