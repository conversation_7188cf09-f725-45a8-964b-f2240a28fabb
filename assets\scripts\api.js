import { ApiClient } from './utils/ApiClient.js';
import { ApiRetry } from './utils/ApiRetry.js';
import { ApiErrorHandler } from './utils/ApiErrorHandler.js';

class Api {
    constructor() {
        this.client = new ApiClient();
    }

    // Authentication endpoints
    async login(credentials) {
        try {
            const response = await this.client.post('/api/auth/login', credentials);
            if (response.tokens) {
                this.client.setAuthToken(response.tokens.accessToken);
                this.client.setRefreshToken(response.tokens.refreshToken);
            }
            return response;
        } catch (error) {
            throw this.handleError(error, 'Login failed');
        }
    }

    async register(userData) {
        try {
            const response = await this.client.post('/api/auth/register', userData);
            if (response.tokens) {
                this.client.setAuthToken(response.tokens.accessToken);
                this.client.setRefreshToken(response.tokens.refreshToken);
            }
            return response;
        } catch (error) {
            throw this.handleError(error, 'Registration failed');
        }
    }

    async logout() {
        try {
            await this.client.post('/api/auth/logout');
            this.client.clearTokens();
            return { success: true };
        } catch (error) {
            // Clear tokens even if logout fails
            this.client.clearTokens();
            throw this.handleError(error, 'Logout failed');
        }
    }

    async getProfile() {
        try {
            return await this.client.get('/api/auth/profile');
        } catch (error) {
            throw this.handleError(error, 'Failed to get profile');
        }
    }

    async updateProfile(profileData) {
        try {
            return await this.client.put('/api/auth/profile', profileData);
        } catch (error) {
            throw this.handleError(error, 'Failed to update profile');
        }
    }

    // Menu endpoints
    async getMenuCategories() {
        try {
            return await ApiRetry.retry(() => this.client.get('/api/menu/categories'));
        } catch (error) {
            throw this.handleError(error, 'Failed to get menu categories');
        }
    }

    async getMenuItems(params = {}) {
        try {
            const queryString = new URLSearchParams(params).toString();
            const endpoint = queryString ? `/api/menu/items?${queryString}` : '/api/menu/items';
            return await ApiRetry.retry(() => this.client.get(endpoint));
        } catch (error) {
            throw this.handleError(error, 'Failed to get menu items');
        }
    }

    async getMenuItem(id) {
        try {
            return await ApiRetry.retry(() => this.client.get(`/api/menu/items/${id}`));
        } catch (error) {
            throw this.handleError(error, 'Failed to get menu item');
        }
    }

    async searchMenuItems(query) {
        try {
            return await this.getMenuItems({ search: query });
        } catch (error) {
            throw this.handleError(error, 'Failed to search menu items');
        }
    }

    async getPopularItems(limit = 10) {
        try {
            return await this.client.get(`/api/menu/popular?limit=${limit}`);
        } catch (error) {
            throw this.handleError(error, 'Failed to get popular items');
        }
    }

    // Cart endpoints
    async getCart() {
        try {
            return await this.client.get('/api/cart');
        } catch (error) {
            throw this.handleError(error, 'Failed to get cart');
        }
    }

    async addToCart(menuItemId, quantity = 1, specialInstructions = '') {
        try {
            return await this.client.post('/api/cart/add', {
                menuItemId,
                quantity,
                specialInstructions
            });
        } catch (error) {
            throw this.handleError(error, 'Failed to add item to cart');
        }
    }

    async updateCartItem(menuItemId, quantity) {
        try {
            return await this.client.put(`/api/cart/items/${menuItemId}`, { quantity });
        } catch (error) {
            throw this.handleError(error, 'Failed to update cart item');
        }
    }

    async removeFromCart(menuItemId) {
        try {
            return await this.client.delete(`/api/cart/items/${menuItemId}`);
        } catch (error) {
            throw this.handleError(error, 'Failed to remove item from cart');
        }
    }

    async clearCart() {
        try {
            return await this.client.delete('/api/cart');
        } catch (error) {
            throw this.handleError(error, 'Failed to clear cart');
        }
    }

    async getCartCount() {
        try {
            return await this.client.get('/api/cart/count');
        } catch (error) {
            throw this.handleError(error, 'Failed to get cart count');
        }
    }

    async validateCart() {
        try {
            return await this.client.post('/api/cart/validate');
        } catch (error) {
            throw this.handleError(error, 'Failed to validate cart');
        }
    }

    // Order endpoints
    async createOrder(orderData) {
        try {
            return await this.client.post('/api/orders', orderData);
        } catch (error) {
            throw this.handleError(error, 'Failed to create order');
        }
    }

    async getOrder(orderId) {
        try {
            return await this.client.get(`/api/orders/${orderId}`);
        } catch (error) {
            throw this.handleError(error, 'Failed to get order');
        }
    }

    async trackOrder(orderNumber) {
        try {
            return await this.client.get(`/api/orders/track/${orderNumber}`);
        } catch (error) {
            throw this.handleError(error, 'Failed to track order');
        }
    }

    async getOrderHistory(page = 1, limit = 10) {
        try {
            return await this.client.get(`/api/orders/user/history?page=${page}&limit=${limit}`);
        } catch (error) {
            throw this.handleError(error, 'Failed to get order history');
        }
    }

    // Contact endpoints
    async submitContactForm(contactData) {
        try {
            return await this.client.post('/api/contact/submit', contactData);
        } catch (error) {
            throw this.handleError(error, 'Failed to submit contact form');
        }
    }

    async getContactStats() {
        try {
            return await this.client.get('/api/contact/stats');
        } catch (error) {
            throw this.handleError(error, 'Failed to get contact stats');
        }
    }

    // Admin endpoints
    async getAdminDashboard() {
        try {
            return await this.client.get('/api/admin/dashboard');
        } catch (error) {
            throw this.handleError(error, 'Failed to get admin dashboard');
        }
    }

    async getAdminUsers(params = {}) {
        try {
            const queryString = new URLSearchParams(params).toString();
            const endpoint = queryString ? `/api/admin/users?${queryString}` : '/api/admin/users';
            return await this.client.get(endpoint);
        } catch (error) {
            throw this.handleError(error, 'Failed to get users');
        }
    }

    async updateUserStatus(userId, isActive) {
        try {
            return await this.client.patch(`/api/admin/users/${userId}/status`, { isActive });
        } catch (error) {
            throw this.handleError(error, 'Failed to update user status');
        }
    }

    async getAdminOrders(params = {}) {
        try {
            const queryString = new URLSearchParams(params).toString();
            const endpoint = queryString ? `/api/orders/admin/all?${queryString}` : '/api/orders/admin/all';
            return await this.client.get(endpoint);
        } catch (error) {
            throw this.handleError(error, 'Failed to get orders');
        }
    }

    async updateOrderStatus(orderId, status) {
        try {
            return await this.client.patch(`/api/orders/${orderId}/status`, { status });
        } catch (error) {
            throw this.handleError(error, 'Failed to update order status');
        }
    }

    async getSalesAnalytics(params = {}) {
        try {
            const queryString = new URLSearchParams(params).toString();
            const endpoint = queryString ? `/api/admin/analytics/sales?${queryString}` : '/api/admin/analytics/sales';
            return await this.client.get(endpoint);
        } catch (error) {
            throw this.handleError(error, 'Failed to get sales analytics');
        }
    }

    // Admin menu management endpoints
    async createMenuItem(itemData) {
        try {
            return await this.client.post('/api/menu/admin/items', itemData);
        } catch (error) {
            throw this.handleError(error, 'Failed to create menu item');
        }
    }

    async updateMenuItem(itemId, itemData) {
        try {
            return await this.client.put(`/api/menu/admin/items/${itemId}`, itemData);
        } catch (error) {
            throw this.handleError(error, 'Failed to update menu item');
        }
    }

    async deleteMenuItem(itemId) {
        try {
            return await this.client.delete(`/api/menu/admin/items/${itemId}`);
        } catch (error) {
            throw this.handleError(error, 'Failed to delete menu item');
        }
    }

    async bulkUpdateItemAvailability(itemIds, isAvailable) {
        try {
            return await this.client.patch('/api/menu/admin/items/bulk/availability', {
                itemIds,
                isAvailable
            });
        } catch (error) {
            throw this.handleError(error, 'Failed to bulk update availability');
        }
    }

    async bulkUpdateItemFeatured(itemIds, isFeatured) {
        try {
            return await this.client.patch('/api/menu/admin/items/bulk/featured', {
                itemIds,
                isFeatured
            });
        } catch (error) {
            throw this.handleError(error, 'Failed to bulk update featured status');
        }
    }

    async bulkDeleteItems(itemIds) {
        try {
            return await this.client.delete('/api/menu/admin/items/bulk', {
                data: { itemIds }
            });
        } catch (error) {
            throw this.handleError(error, 'Failed to bulk delete items');
        }
    }

    async createMenuCategory(categoryData) {
        try {
            return await this.client.post('/api/menu/admin/categories', categoryData);
        } catch (error) {
            throw this.handleError(error, 'Failed to create category');
        }
    }

    async updateMenuCategory(categoryId, categoryData) {
        try {
            return await this.client.put(`/api/menu/admin/categories/${categoryId}`, categoryData);
        } catch (error) {
            throw this.handleError(error, 'Failed to update category');
        }
    }

    async deleteMenuCategory(categoryId) {
        try {
            return await this.client.delete(`/api/menu/admin/categories/${categoryId}`);
        } catch (error) {
            throw this.handleError(error, 'Failed to delete category');
        }
    }

    // Utility methods
    isAuthenticated() {
        return this.client.isAuthenticated();
    }

    async refreshTokenIfNeeded() {
        return await this.client.refreshTokenIfNeeded();
    }

    // Error handling wrapper
    handleError(error, context = '') {
        const message = ApiErrorHandler.handleError(error, context);
        return new Error(message);
    }

    async request(operation, context = '') {
        try {
            // Try to refresh token if needed
            await this.refreshTokenIfNeeded();
            return await operation();
        } catch (error) {
            throw this.handleError(error, context);
        }
    }
}

// Export both the default instance and named export
const api = new Api();
export { api };
export default api;
