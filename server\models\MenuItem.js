const BaseModel = require('./BaseModel');

class MenuItem extends BaseModel {
    constructor() {
        super('menu_items');
    }

    // Get all menu items with categories
    async getAllWithCategories() {
        try {
            const query = `
                SELECT 
                    mi.*,
                    mc.name as category_name,
                    mc.description as category_description
                FROM menu_items mi
                JOIN menu_categories mc ON mi.category_id = mc.id
                WHERE mi.is_available = true AND mc.is_active = true
                ORDER BY mc.display_order, mi.display_order, mi.name
            `;
            return await this.db.query(query);
        } catch (error) {
            console.error('Error getting menu items with categories:', error);
            throw error;
        }
    }

    // Get menu items by category
    async getByCategory(categoryId) {
        try {
            const query = `
                SELECT mi.*
                FROM menu_items mi
                WHERE mi.category_id = $1 AND mi.is_available = true
                ORDER BY mi.display_order, mi.name
            `;
            return await this.db.query(query, [categoryId]);
        } catch (error) {
            console.error('Error getting menu items by category:', error);
            throw error;
        }
    }

    // Get featured menu items
    async getFeatured() {
        try {
            const query = `
                SELECT 
                    mi.*,
                    mc.name as category_name
                FROM menu_items mi
                JOIN menu_categories mc ON mi.category_id = mc.id
                WHERE mi.is_featured = true AND mi.is_available = true AND mc.is_active = true
                ORDER BY mi.display_order, mi.name
            `;
            return await this.db.query(query);
        } catch (error) {
            console.error('Error getting featured menu items:', error);
            throw error;
        }
    }

    // Search menu items
    async search(searchTerm) {
        try {
            const query = `
                SELECT 
                    mi.*,
                    mc.name as category_name,
                    ts_rank(
                        to_tsvector('english', mi.name || ' ' || mi.description),
                        plainto_tsquery('english', $1)
                    ) as rank
                FROM menu_items mi
                JOIN menu_categories mc ON mi.category_id = mc.id
                WHERE 
                    mi.is_available = true 
                    AND mc.is_active = true
                    AND (
                        mi.name ILIKE $2 OR 
                        mi.description ILIKE $2 OR
                        $3 = ANY(mi.ingredients) OR
                        to_tsvector('english', mi.name || ' ' || mi.description) @@ plainto_tsquery('english', $1)
                    )
                ORDER BY rank DESC, mi.name
            `;
            const searchPattern = `%${searchTerm}%`;
            return await this.db.query(query, [searchTerm, searchPattern, searchTerm]);
        } catch (error) {
            console.error('Error searching menu items:', error);
            throw error;
        }
    }

    // Get menu items with allergen filtering
    async getWithAllergenFilter(excludeAllergens = []) {
        try {
            let query = `
                SELECT 
                    mi.*,
                    mc.name as category_name
                FROM menu_items mi
                JOIN menu_categories mc ON mi.category_id = mc.id
                WHERE mi.is_available = true AND mc.is_active = true
            `;
            
            if (excludeAllergens.length > 0) {
                query += ` AND NOT (mi.allergens && $1)`;
            }
            
            query += ` ORDER BY mc.display_order, mi.display_order, mi.name`;
            
            const params = excludeAllergens.length > 0 ? [excludeAllergens] : [];
            return await this.db.query(query, params);
        } catch (error) {
            console.error('Error getting menu items with allergen filter:', error);
            throw error;
        }
    }

    // Get menu items by price range
    async getByPriceRange(minPrice, maxPrice) {
        try {
            const query = `
                SELECT 
                    mi.*,
                    mc.name as category_name
                FROM menu_items mi
                JOIN menu_categories mc ON mi.category_id = mc.id
                WHERE 
                    mi.is_available = true 
                    AND mc.is_active = true
                    AND mi.price BETWEEN $1 AND $2
                ORDER BY mi.price, mi.name
            `;
            return await this.db.query(query, [minPrice, maxPrice]);
        } catch (error) {
            console.error('Error getting menu items by price range:', error);
            throw error;
        }
    }

    // Create menu item with validation
    async createMenuItem(itemData) {
        try {
            // Validate required fields
            const requiredFields = ['category_id', 'name', 'price'];
            for (const field of requiredFields) {
                if (!itemData[field]) {
                    throw new Error(`Missing required field: ${field}`);
                }
            }

            // Validate price
            if (itemData.price <= 0) {
                throw new Error('Price must be greater than 0');
            }

            return await this.create(itemData);
        } catch (error) {
            console.error('Error creating menu item:', error);
            throw error;
        }
    }

    // Update menu item availability
    async updateAvailability(itemId, isAvailable) {
        try {
            return await this.update(itemId, { is_available: isAvailable });
        } catch (error) {
            console.error('Error updating menu item availability:', error);
            throw error;
        }
    }

    // Get menu statistics (admin function)
    async getMenuStats() {
        try {
            const query = `
                SELECT 
                    COUNT(*) as total_items,
                    COUNT(*) FILTER (WHERE is_available = true) as available_items,
                    COUNT(*) FILTER (WHERE is_featured = true) as featured_items,
                    AVG(price) as average_price,
                    MIN(price) as min_price,
                    MAX(price) as max_price,
                    COUNT(DISTINCT category_id) as total_categories
                FROM menu_items
            `;
            const result = await this.db.query(query);
            return result[0];
        } catch (error) {
            console.error('Error getting menu stats:', error);
            throw error;
        }
    }

    // Get popular items (based on order frequency)
    async getPopularItems(limit = 10) {
        try {
            const query = `
                SELECT 
                    mi.*,
                    mc.name as category_name,
                    COUNT(oi.id) as order_count,
                    SUM(oi.quantity) as total_quantity
                FROM menu_items mi
                JOIN menu_categories mc ON mi.category_id = mc.id
                LEFT JOIN order_items oi ON mi.id = oi.menu_item_id
                WHERE mi.is_available = true AND mc.is_active = true
                GROUP BY mi.id, mc.name
                ORDER BY order_count DESC, total_quantity DESC
                LIMIT $1
            `;
            return await this.db.query(query, [limit]);
        } catch (error) {
            console.error('Error getting popular items:', error);
            throw error;
        }
    }

    // Bulk update display order
    async updateDisplayOrder(items) {
        try {
            const client = await this.db.pool.connect();
            
            try {
                await client.query('BEGIN');
                
                for (const item of items) {
                    await client.query(
                        'UPDATE menu_items SET display_order = $1 WHERE id = $2',
                        [item.display_order, item.id]
                    );
                }
                
                await client.query('COMMIT');
                return true;
            } catch (error) {
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            console.error('Error updating display order:', error);
            throw error;
        }
    }

    // Bulk update availability
    async bulkUpdateAvailability(itemIds, isAvailable) {
        try {
            const query = `
                UPDATE menu_items
                SET is_available = $1, updated_at = CURRENT_TIMESTAMP
                WHERE id = ANY($2)
                RETURNING id, name, is_available
            `;

            const result = await this.db.query(query, [isAvailable, itemIds]);
            return result;
        } catch (error) {
            console.error('Bulk update availability error:', error);
            throw error;
        }
    }

    // Bulk delete items (soft delete)
    async bulkDelete(itemIds) {
        try {
            const query = `
                UPDATE menu_items
                SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                WHERE id = ANY($1) AND deleted_at IS NULL
                RETURNING id, name
            `;

            const result = await this.db.query(query, [itemIds]);
            return result;
        } catch (error) {
            console.error('Bulk delete error:', error);
            throw error;
        }
    }

    // Bulk update category
    async bulkUpdateCategory(itemIds, categoryId) {
        try {
            const query = `
                UPDATE menu_items
                SET category_id = $1, updated_at = CURRENT_TIMESTAMP
                WHERE id = ANY($2)
                RETURNING id, name, category_id
            `;

            const result = await this.db.query(query, [categoryId, itemIds]);
            return result;
        } catch (error) {
            console.error('Bulk update category error:', error);
            throw error;
        }
    }

    // Bulk update featured status
    async bulkUpdateFeatured(itemIds, isFeatured) {
        try {
            const query = `
                UPDATE menu_items
                SET is_featured = $1, updated_at = CURRENT_TIMESTAMP
                WHERE id = ANY($2)
                RETURNING id, name, is_featured
            `;

            const result = await this.db.query(query, [isFeatured, itemIds]);
            return result;
        } catch (error) {
            console.error('Bulk update featured error:', error);
            throw error;
        }
    }
}

module.exports = new MenuItem();
