-- Add tags functionality to menu items
-- This is an example of how future migrations would work

-- Add tags column to menu_items table
ALTER TABLE menu_items 
ADD COLUMN tags TEXT[] DEFAULT '{}';

-- Create index for tags
CREATE INDEX idx_menu_items_tags ON menu_items USING GIN(tags);

-- Add some common tags for existing items (this would be handled by seed data normally)
-- This is just an example of how data migrations could work
