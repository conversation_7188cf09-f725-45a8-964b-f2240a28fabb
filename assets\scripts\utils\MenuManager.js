import { api } from '../api.js';
import { Alert } from './Alert.js';
import { formatPrice } from '../config/currency.js';

export class MenuManager {
    constructor() {
        this.categories = [];
        this.menuItems = [];
        this.currentFilter = {};
        this.isLoading = false;
        
        // DOM elements
        this.categoryNav = null;
        this.menuContainer = null;
        this.searchInput = null;
        this.loadingIndicator = null;
        
        this.init();
    }

    async init() {
        this.setupDOMElements();
        this.setupEventListeners();
        await this.loadMenuData();
    }

    setupDOMElements() {
        this.categoryNav = document.querySelector('.category-nav');
        this.menuContainer = document.querySelector('.menu-container') || document.querySelector('.menu-sections');
        this.searchInput = document.querySelector('#menuSearch');
        this.loadingIndicator = document.querySelector('.loading-indicator');
        
        // Create loading indicator if it doesn't exist
        if (!this.loadingIndicator && this.menuContainer) {
            this.loadingIndicator = document.createElement('div');
            this.loadingIndicator.className = 'loading-indicator';
            this.loadingIndicator.innerHTML = `
                <div class="loading-spinner"></div>
                <p>Loading menu...</p>
            `;
            this.menuContainer.parentNode.insertBefore(this.loadingIndicator, this.menuContainer);
        }
    }

    setupEventListeners() {
        // Search functionality
        if (this.searchInput) {
            let searchTimeout;
            this.searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.searchMenu(e.target.value);
                }, 300);
            });
        }

        // Category filter clicks
        if (this.categoryNav) {
            this.categoryNav.addEventListener('click', (e) => {
                const categoryLink = e.target.closest('.category-link');
                if (categoryLink) {
                    e.preventDefault();
                    const categoryId = categoryLink.dataset.categoryId;
                    this.filterByCategory(categoryId);
                }
            });
        }

        // Filter buttons (if they exist)
        document.addEventListener('click', (e) => {
            if (e.target.matches('.filter-btn')) {
                e.preventDefault();
                const filterType = e.target.dataset.filter;
                const filterValue = e.target.dataset.value;
                this.applyFilter(filterType, filterValue);
            }
        });
    }

    async loadMenuData() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading(true);

        try {
            // Load categories and items in parallel
            const [categoriesResponse, itemsResponse] = await Promise.all([
                api.getMenuCategories(),
                api.getMenuItems()
            ]);

            this.categories = categoriesResponse.categories || [];
            this.menuItems = itemsResponse.items || [];

            // Render the menu
            this.renderCategories();
            this.renderMenuItems();

        } catch (error) {
            console.error('Failed to load menu data:', error);
            Alert.show('Failed to load menu. Please refresh the page.', 'error');
            this.showError('Failed to load menu data');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    renderCategories() {
        if (!this.categoryNav || this.categories.length === 0) return;

        // Clear existing categories (except "All" if it exists)
        const existingLinks = this.categoryNav.querySelectorAll('.category-link:not([data-category-id="all"])');
        existingLinks.forEach(link => link.parentElement.remove());

        // Add categories
        this.categories.forEach(category => {
            const categoryItem = document.createElement('li');
            categoryItem.className = 'category-item';
            
            categoryItem.innerHTML = `
                <a href="#${category.name.toLowerCase().replace(/\s+/g, '-')}" 
                   class="category-link" 
                   data-category-id="${category.id}">
                    ${category.name}
                </a>
            `;
            
            this.categoryNav.appendChild(categoryItem);
        });
    }

    renderMenuItems(items = null) {
        if (!this.menuContainer) return;

        const itemsToRender = items || this.menuItems;
        
        // Group items by category
        const itemsByCategory = this.groupItemsByCategory(itemsToRender);
        
        // Clear existing content
        this.menuContainer.innerHTML = '';

        // Render each category section
        Object.entries(itemsByCategory).forEach(([categoryName, categoryItems]) => {
            const categorySection = this.createCategorySection(categoryName, categoryItems);
            this.menuContainer.appendChild(categorySection);
        });

        // Initialize any additional functionality (like AOS animations)
        if (window.AOS) {
            window.AOS.refresh();
        }
    }

    groupItemsByCategory(items) {
        const grouped = {};
        
        items.forEach(item => {
            const categoryName = item.category_name || 'Other';
            if (!grouped[categoryName]) {
                grouped[categoryName] = [];
            }
            grouped[categoryName].push(item);
        });

        return grouped;
    }

    createCategorySection(categoryName, items) {
        const section = document.createElement('section');
        section.className = 'menu-category';
        section.id = categoryName.toLowerCase().replace(/\s+/g, '-');
        section.setAttribute('data-aos', 'fade-up');

        const categoryHeading = document.createElement('h3');
        categoryHeading.className = 'category-heading';
        categoryHeading.textContent = categoryName;

        const menuGrid = document.createElement('div');
        menuGrid.className = 'menu-grid';

        items.forEach(item => {
            const itemCard = this.createMenuItemCard(item);
            menuGrid.appendChild(itemCard);
        });

        section.appendChild(categoryHeading);
        section.appendChild(menuGrid);

        return section;
    }

    createMenuItemCard(item) {
        const card = document.createElement('div');
        card.className = 'menu-item-card';
        card.setAttribute('data-aos', 'fade-up');
        card.setAttribute('data-id', item.id);
        card.setAttribute('data-title', item.name);
        card.setAttribute('data-description', item.description || '');
        card.setAttribute('data-price', formatPrice(item.price));
        card.setAttribute('data-calories', item.calories || 'N/A');
        card.setAttribute('data-protein', item.nutritional_info?.protein || 'N/A');
        card.setAttribute('data-carbs', item.nutritional_info?.carbs || 'N/A');
        card.setAttribute('data-ingredients', (item.ingredients || []).join(', '));

        const imageUrl = item.image_url || 'assets/images/placeholder-food.jpg';
        const isAvailable = item.is_available;

        card.innerHTML = `
            <div class="card-image-wrapper">
                <img src="${imageUrl}" alt="${item.name}" loading="lazy">
                <div class="card-overlay">
                    <button class="quick-view-btn">Quick View</button>
                </div>
                ${!isAvailable ? '<div class="unavailable-overlay">Currently Unavailable</div>' : ''}
            </div>
            <div class="card-content">
                <h4 class="card-title">${item.name}</h4>
                <p class="card-description">${item.description || ''}</p>
                <div class="card-footer">
                    <span class="card-price">${formatPrice(item.price)}</span>
                    <button class="btn btn-primary add-to-cart-btn" 
                            ${!isAvailable ? 'disabled' : ''} 
                            data-item-id="${item.id}">
                        <i class="fas fa-cart-plus"></i>
                        ${isAvailable ? 'Add to Cart' : 'Unavailable'}
                    </button>
                </div>
                ${item.is_featured ? '<div class="featured-badge">Featured</div>' : ''}
            </div>
        `;

        return card;
    }

    async searchMenu(query) {
        if (!query.trim()) {
            this.renderMenuItems();
            return;
        }

        this.showLoading(true);

        try {
            const response = await api.searchMenuItems(query);
            this.renderMenuItems(response.items || []);
            
            // Update URL without page reload
            const url = new URL(window.location);
            url.searchParams.set('search', query);
            window.history.replaceState({}, '', url);
            
        } catch (error) {
            console.error('Search failed:', error);
            Alert.show('Search failed. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async filterByCategory(categoryId) {
        if (categoryId === 'all' || !categoryId) {
            this.renderMenuItems();
            this.updateActiveCategory('all');
            return;
        }

        this.showLoading(true);

        try {
            const response = await api.getMenuItems({ category: categoryId });
            this.renderMenuItems(response.items || []);
            this.updateActiveCategory(categoryId);
            
        } catch (error) {
            console.error('Filter failed:', error);
            Alert.show('Failed to filter menu. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async applyFilter(filterType, filterValue) {
        this.showLoading(true);

        try {
            const params = { [filterType]: filterValue };
            const response = await api.getMenuItems(params);
            this.renderMenuItems(response.items || []);
            
        } catch (error) {
            console.error('Filter failed:', error);
            Alert.show('Failed to apply filter. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    updateActiveCategory(categoryId) {
        if (!this.categoryNav) return;

        // Remove active class from all links
        this.categoryNav.querySelectorAll('.category-link').forEach(link => {
            link.classList.remove('active');
        });

        // Add active class to selected category
        const activeLink = this.categoryNav.querySelector(`[data-category-id="${categoryId}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    showLoading(show) {
        if (this.loadingIndicator) {
            this.loadingIndicator.style.display = show ? 'block' : 'none';
        }
        
        if (this.menuContainer) {
            this.menuContainer.style.opacity = show ? '0.5' : '1';
        }
    }

    showError(message) {
        if (this.menuContainer) {
            this.menuContainer.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Oops! Something went wrong</h3>
                    <p>${message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        Try Again
                    </button>
                </div>
            `;
        }
    }

    // Public methods for external use
    async refreshMenu() {
        await this.loadMenuData();
    }

    getMenuItems() {
        return this.menuItems;
    }

    getCategories() {
        return this.categories;
    }
}

// Create global menu manager instance
export const menuManager = new MenuManager();
