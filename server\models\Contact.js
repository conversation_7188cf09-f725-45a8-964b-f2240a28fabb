const BaseModel = require('./BaseModel');

class Contact extends BaseModel {
    constructor() {
        super('contact_submissions');
    }

    // Create new contact submission
    async createSubmission(submissionData) {
        try {
            // Validate required fields
            const requiredFields = ['name', 'email', 'subject', 'message'];
            for (const field of requiredFields) {
                if (!submissionData[field]) {
                    throw new Error(`Missing required field: ${field}`);
                }
            }

            // Sanitize and prepare data
            const contactData = {
                name: submissionData.name.trim(),
                email: submissionData.email.toLowerCase().trim(),
                phone: submissionData.phone ? submissionData.phone.trim() : null,
                subject: submissionData.subject.trim(),
                message: submissionData.message.trim(),
                inquiry_type: submissionData.inquiryType || 'general',
                is_read: false
            };

            return await this.create(contactData);
        } catch (error) {
            console.error('Error creating contact submission:', error);
            throw error;
        }
    }

    // Get all submissions with pagination and filtering
    async getSubmissions(page = 1, limit = 20, filters = {}) {
        try {
            const conditions = {};
            
            // Apply filters
            if (filters.isRead !== undefined) {
                conditions.is_read = filters.isRead;
            }
            
            if (filters.inquiryType) {
                conditions.inquiry_type = filters.inquiryType;
            }
            
            if (filters.startDate && filters.endDate) {
                // This would need custom query implementation
                return await this.getSubmissionsByDateRange(
                    filters.startDate, 
                    filters.endDate, 
                    page, 
                    limit, 
                    conditions
                );
            }

            return await this.paginate(page, limit, conditions, 'created_at DESC');
        } catch (error) {
            console.error('Error getting submissions:', error);
            throw error;
        }
    }

    // Get submissions by date range
    async getSubmissionsByDateRange(startDate, endDate, page = 1, limit = 20, additionalConditions = {}) {
        try {
            const offset = (page - 1) * limit;
            
            let query = `
                SELECT * FROM contact_submissions 
                WHERE created_at BETWEEN $1 AND $2
            `;
            const params = [startDate, endDate];
            
            // Add additional conditions
            let paramIndex = 3;
            for (const [key, value] of Object.entries(additionalConditions)) {
                query += ` AND ${key} = $${paramIndex}`;
                params.push(value);
                paramIndex++;
            }
            
            query += ` ORDER BY created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
            params.push(limit, offset);
            
            // Get total count
            let countQuery = `
                SELECT COUNT(*) as count FROM contact_submissions 
                WHERE created_at BETWEEN $1 AND $2
            `;
            const countParams = [startDate, endDate];
            
            let countParamIndex = 3;
            for (const [key, value] of Object.entries(additionalConditions)) {
                countQuery += ` AND ${key} = $${countParamIndex}`;
                countParams.push(value);
                countParamIndex++;
            }
            
            const [data, countResult] = await Promise.all([
                this.db.query(query, params),
                this.db.query(countQuery, countParams)
            ]);
            
            const totalCount = parseInt(countResult[0].count);
            
            return {
                data,
                pagination: {
                    page,
                    limit,
                    total: totalCount,
                    pages: Math.ceil(totalCount / limit),
                    hasNext: page < Math.ceil(totalCount / limit),
                    hasPrev: page > 1
                }
            };
        } catch (error) {
            console.error('Error getting submissions by date range:', error);
            throw error;
        }
    }

    // Search submissions
    async searchSubmissions(searchTerm, page = 1, limit = 20) {
        try {
            const offset = (page - 1) * limit;
            
            const query = `
                SELECT * FROM contact_submissions 
                WHERE 
                    name ILIKE $1 OR 
                    email ILIKE $1 OR 
                    subject ILIKE $1 OR 
                    message ILIKE $1
                ORDER BY created_at DESC
                LIMIT $2 OFFSET $3
            `;
            
            const searchPattern = `%${searchTerm}%`;
            const data = await this.db.query(query, [searchPattern, limit, offset]);
            
            // Get total count for search
            const countQuery = `
                SELECT COUNT(*) as count FROM contact_submissions 
                WHERE 
                    name ILIKE $1 OR 
                    email ILIKE $1 OR 
                    subject ILIKE $1 OR 
                    message ILIKE $1
            `;
            
            const countResult = await this.db.query(countQuery, [searchPattern]);
            const totalCount = parseInt(countResult[0].count);
            
            return {
                data,
                pagination: {
                    page,
                    limit,
                    total: totalCount,
                    pages: Math.ceil(totalCount / limit),
                    hasNext: page < Math.ceil(totalCount / limit),
                    hasPrev: page > 1
                }
            };
        } catch (error) {
            console.error('Error searching submissions:', error);
            throw error;
        }
    }

    // Mark submission as read/unread
    async markAsRead(submissionId, isRead = true) {
        try {
            const updatedSubmission = await this.update(submissionId, { is_read: isRead });
            if (!updatedSubmission) {
                throw new Error('Submission not found');
            }
            return updatedSubmission;
        } catch (error) {
            console.error('Error marking submission as read:', error);
            throw error;
        }
    }

    // Add admin notes to submission
    async addAdminNotes(submissionId, notes, adminId) {
        try {
            const updateData = {
                admin_notes: notes,
                is_read: true // Mark as read when admin adds notes
            };
            
            const updatedSubmission = await this.update(submissionId, updateData);
            if (!updatedSubmission) {
                throw new Error('Submission not found');
            }
            
            // Log admin action
            console.log(`Admin ${adminId} added notes to contact submission ${submissionId}`);
            
            return updatedSubmission;
        } catch (error) {
            console.error('Error adding admin notes:', error);
            throw error;
        }
    }

    // Get submission statistics
    async getSubmissionStats(startDate = null, endDate = null) {
        try {
            let dateFilter = '';
            const params = [];
            
            if (startDate && endDate) {
                dateFilter = 'WHERE created_at BETWEEN $1 AND $2';
                params.push(startDate, endDate);
            }
            
            const query = `
                SELECT 
                    COUNT(*) as total_submissions,
                    COUNT(*) FILTER (WHERE is_read = true) as read_submissions,
                    COUNT(*) FILTER (WHERE is_read = false) as unread_submissions,
                    COUNT(*) FILTER (WHERE inquiry_type = 'general') as general_inquiries,
                    COUNT(*) FILTER (WHERE inquiry_type = 'complaint') as complaints,
                    COUNT(*) FILTER (WHERE inquiry_type = 'suggestion') as suggestions,
                    COUNT(*) FILTER (WHERE inquiry_type = 'support') as support_requests,
                    COUNT(*) FILTER (WHERE admin_notes IS NOT NULL) as submissions_with_notes,
                    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '7 days') as submissions_last_7_days,
                    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as submissions_last_30_days
                FROM contact_submissions
                ${dateFilter}
            `;
            
            const result = await this.db.query(query, params);
            return result[0];
        } catch (error) {
            console.error('Error getting submission stats:', error);
            throw error;
        }
    }

    // Get recent submissions (for dashboard)
    async getRecentSubmissions(limit = 5) {
        try {
            const query = `
                SELECT 
                    id, name, email, subject, inquiry_type, 
                    is_read, created_at
                FROM contact_submissions 
                ORDER BY created_at DESC 
                LIMIT $1
            `;
            
            return await this.db.query(query, [limit]);
        } catch (error) {
            console.error('Error getting recent submissions:', error);
            throw error;
        }
    }

    // Get submissions by inquiry type
    async getSubmissionsByType(inquiryType, page = 1, limit = 20) {
        try {
            return await this.paginate(
                page, 
                limit, 
                { inquiry_type: inquiryType }, 
                'created_at DESC'
            );
        } catch (error) {
            console.error('Error getting submissions by type:', error);
            throw error;
        }
    }

    // Bulk mark as read
    async bulkMarkAsRead(submissionIds) {
        try {
            const client = await this.db.pool.connect();
            
            try {
                await client.query('BEGIN');
                
                for (const id of submissionIds) {
                    await client.query(
                        'UPDATE contact_submissions SET is_read = true, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
                        [id]
                    );
                }
                
                await client.query('COMMIT');
                return true;
            } catch (error) {
                await client.query('ROLLBACK');
                throw error;
            } finally {
                client.release();
            }
        } catch (error) {
            console.error('Error bulk marking as read:', error);
            throw error;
        }
    }

    // Delete old submissions (cleanup function)
    async deleteOldSubmissions(daysOld = 365) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysOld);
            
            const query = `
                DELETE FROM contact_submissions 
                WHERE created_at < $1 
                RETURNING id
            `;
            
            const deletedSubmissions = await this.db.query(query, [cutoffDate]);
            
            console.log(`Deleted ${deletedSubmissions.length} old contact submissions`);
            return deletedSubmissions.length;
        } catch (error) {
            console.error('Error deleting old submissions:', error);
            throw error;
        }
    }
}

module.exports = new Contact();
