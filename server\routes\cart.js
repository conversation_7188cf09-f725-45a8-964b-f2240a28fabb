const express = require('express');
const Joi = require('joi');
const Cart = require('../models/Cart');
const MenuItem = require('../models/MenuItem');
const AuthMiddleware = require('../middleware/auth');

const router = express.Router();

// Validation schemas
const addItemSchema = Joi.object({
    menuItemId: Joi.string().uuid().required(),
    quantity: Joi.number().integer().min(1).max(99).required(),
    specialInstructions: Joi.string().max(500).optional().allow('')
});

const updateQuantitySchema = Joi.object({
    quantity: Joi.number().integer().min(0).max(99).required()
});

// Helper function to get cart ID
async function getCartId(req) {
    if (req.user) {
        // Authenticated user
        const cart = await Cart.getOrCreateUserCart(req.user.id);
        return cart.id;
    } else {
        // Guest user
        if (!req.session.guestId) {
            req.session.guestId = require('crypto').randomUUID();
        }
        const cart = await Cart.getOrCreateSessionCart(req.session.guestId);
        return cart.id;
    }
}

// Get current cart
router.get('/', AuthMiddleware.optionalAuth, AuthMiddleware.getSessionUser, async (req, res) => {
    try {
        const cartId = await getCartId(req);
        const cartSummary = await Cart.getCartSummary(cartId);
        
        if (!cartSummary) {
            return res.json({
                cart: {
                    id: cartId,
                    items: [],
                    totals: {
                        subtotal: 0,
                        tax: 0,
                        delivery_fee: 500,
                        total: 500
                    },
                    item_count: 0,
                    total_quantity: 0
                }
            });
        }

        res.json({
            cart: cartSummary
        });
    } catch (error) {
        console.error('Get cart error:', error);
        res.status(500).json({
            error: 'Failed to fetch cart',
            message: 'Internal server error'
        });
    }
});

// Add item to cart
router.post('/add', AuthMiddleware.optionalAuth, AuthMiddleware.getSessionUser, async (req, res) => {
    try {
        const { error, value } = addItemSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                error: 'Validation failed',
                message: error.details[0].message
            });
        }

        const { menuItemId, quantity, specialInstructions } = value;

        // Verify menu item exists and is available
        const menuItem = await MenuItem.findById(menuItemId);
        if (!menuItem) {
            return res.status(404).json({
                error: 'Menu item not found',
                message: 'The requested menu item does not exist'
            });
        }

        if (!menuItem.is_available) {
            return res.status(400).json({
                error: 'Item unavailable',
                message: 'This menu item is currently unavailable'
            });
        }

        const cartId = await getCartId(req);
        const updatedCart = await Cart.addItem(cartId, menuItemId, quantity, specialInstructions);
        const cartSummary = await Cart.getCartSummary(updatedCart.id);

        res.json({
            message: 'Item added to cart successfully',
            cart: cartSummary
        });
    } catch (error) {
        console.error('Add to cart error:', error);
        res.status(500).json({
            error: 'Failed to add item to cart',
            message: 'Internal server error'
        });
    }
});

// Update item quantity in cart
router.put('/items/:menuItemId', AuthMiddleware.optionalAuth, AuthMiddleware.getSessionUser, async (req, res) => {
    try {
        const { menuItemId } = req.params;
        
        if (!menuItemId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
            return res.status(400).json({
                error: 'Invalid ID format',
                message: 'Menu item ID must be a valid UUID'
            });
        }

        const { error, value } = updateQuantitySchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                error: 'Validation failed',
                message: error.details[0].message
            });
        }

        const { quantity } = value;
        const cartId = await getCartId(req);

        let updatedCart;
        if (quantity === 0) {
            updatedCart = await Cart.removeItem(cartId, menuItemId);
        } else {
            updatedCart = await Cart.updateItemQuantity(cartId, menuItemId, quantity);
        }

        const cartSummary = await Cart.getCartSummary(updatedCart.id);

        res.json({
            message: quantity === 0 ? 'Item removed from cart' : 'Item quantity updated',
            cart: cartSummary
        });
    } catch (error) {
        console.error('Update cart item error:', error);
        
        if (error.message.includes('not found')) {
            return res.status(404).json({
                error: 'Cart item not found',
                message: 'The item is not in your cart'
            });
        }
        
        res.status(500).json({
            error: 'Failed to update cart item',
            message: 'Internal server error'
        });
    }
});

// Remove item from cart
router.delete('/items/:menuItemId', AuthMiddleware.optionalAuth, AuthMiddleware.getSessionUser, async (req, res) => {
    try {
        const { menuItemId } = req.params;
        
        if (!menuItemId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)) {
            return res.status(400).json({
                error: 'Invalid ID format',
                message: 'Menu item ID must be a valid UUID'
            });
        }

        const cartId = await getCartId(req);
        const updatedCart = await Cart.removeItem(cartId, menuItemId);
        const cartSummary = await Cart.getCartSummary(updatedCart.id);

        res.json({
            message: 'Item removed from cart successfully',
            cart: cartSummary
        });
    } catch (error) {
        console.error('Remove from cart error:', error);
        
        if (error.message.includes('not found')) {
            return res.status(404).json({
                error: 'Cart item not found',
                message: 'The item is not in your cart'
            });
        }
        
        res.status(500).json({
            error: 'Failed to remove item from cart',
            message: 'Internal server error'
        });
    }
});

// Clear entire cart
router.delete('/', AuthMiddleware.optionalAuth, AuthMiddleware.getSessionUser, async (req, res) => {
    try {
        const cartId = await getCartId(req);
        const clearedCart = await Cart.clearCart(cartId);
        const cartSummary = await Cart.getCartSummary(clearedCart.id);

        res.json({
            message: 'Cart cleared successfully',
            cart: cartSummary
        });
    } catch (error) {
        console.error('Clear cart error:', error);
        res.status(500).json({
            error: 'Failed to clear cart',
            message: 'Internal server error'
        });
    }
});

// Get cart item count (lightweight endpoint for header display)
router.get('/count', AuthMiddleware.optionalAuth, AuthMiddleware.getSessionUser, async (req, res) => {
    try {
        const cartId = await getCartId(req);
        const cartSummary = await Cart.getCartSummary(cartId);
        
        res.json({
            count: cartSummary ? cartSummary.total_quantity : 0
        });
    } catch (error) {
        console.error('Get cart count error:', error);
        res.status(500).json({
            error: 'Failed to fetch cart count',
            message: 'Internal server error'
        });
    }
});

// Merge guest cart with user cart (called automatically on login)
router.post('/merge', AuthMiddleware.authenticate, async (req, res) => {
    try {
        const { guestSessionId } = req.body;
        
        if (!guestSessionId) {
            return res.status(400).json({
                error: 'Validation failed',
                message: 'Guest session ID is required'
            });
        }

        const mergedCart = await Cart.mergeGuestCartWithUserCart(guestSessionId, req.user.id);
        const cartSummary = await Cart.getCartSummary(mergedCart.id);

        res.json({
            message: 'Cart merged successfully',
            cart: cartSummary
        });
    } catch (error) {
        console.error('Merge cart error:', error);
        res.status(500).json({
            error: 'Failed to merge cart',
            message: 'Internal server error'
        });
    }
});

// Validate cart before checkout
router.post('/validate', AuthMiddleware.optionalAuth, AuthMiddleware.getSessionUser, async (req, res) => {
    try {
        const cartId = await getCartId(req);
        const cartSummary = await Cart.getCartSummary(cartId);
        
        if (!cartSummary || !cartSummary.items || cartSummary.items.length === 0) {
            return res.status(400).json({
                error: 'Empty cart',
                message: 'Your cart is empty'
            });
        }

        // Check if all items are still available
        const unavailableItems = [];
        for (const item of cartSummary.items) {
            const menuItem = await MenuItem.findById(item.menu_item_id);
            if (!menuItem || !menuItem.is_available) {
                unavailableItems.push({
                    id: item.menu_item_id,
                    name: item.menu_item.name
                });
            }
        }

        if (unavailableItems.length > 0) {
            return res.status(400).json({
                error: 'Items unavailable',
                message: 'Some items in your cart are no longer available',
                unavailableItems
            });
        }

        res.json({
            message: 'Cart is valid',
            cart: cartSummary,
            isValid: true
        });
    } catch (error) {
        console.error('Validate cart error:', error);
        res.status(500).json({
            error: 'Failed to validate cart',
            message: 'Internal server error'
        });
    }
});

module.exports = router;
