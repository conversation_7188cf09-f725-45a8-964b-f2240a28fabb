import { authManager } from './AuthManager.js';
import { cartManager } from './CartManager.js';
import { menuManager } from './MenuManager.js';
import { orderManager } from './OrderManager.js';
import { adminManager } from './AdminManager.js';
import { contactManager } from './ContactManager.js';
import { Alert } from './Alert.js';

export class StateManager {
    constructor() {
        this.state = {
            user: null,
            cart: null,
            menu: {
                categories: [],
                items: [],
                isLoaded: false
            },
            orders: [],
            admin: {
                dashboard: null,
                users: [],
                orders: [],
                analytics: null
            },
            ui: {
                isLoading: false,
                currentPage: '',
                notifications: []
            }
        };
        
        this.subscribers = [];
        this.initialized = false;
        
        this.init();
    }

    async init() {
        if (this.initialized) return;
        
        try {
            // Initialize core managers
            await this.initializeManagers();
            
            // Setup cross-manager communication
            this.setupManagerCommunication();
            
            // Setup page-specific initialization
            this.setupPageInitialization();
            
            // Setup global error handling
            this.setupGlobalErrorHandling();
            
            this.initialized = true;
            this.notifySubscribers('initialized', this.state);
            
        } catch (error) {
            console.error('State manager initialization failed:', error);
            Alert.show('Application initialization failed. Please refresh the page.', 'error');
        }
    }

    async initializeManagers() {
        // Initialize authentication first
        const isAuthenticated = await authManager.checkAuthStatus();
        this.updateState('user', authManager.getCurrentUser());
        
        // Initialize cart (works for both authenticated and guest users)
        await cartManager.init();
        this.updateState('cart', cartManager.getCart());
        
        // Initialize menu if on menu page
        if (this.isMenuPage()) {
            await menuManager.init();
            this.updateState('menu', {
                categories: menuManager.getCategories(),
                items: menuManager.getMenuItems(),
                isLoaded: true
            });
        }
        
        // Initialize admin manager if user is admin
        if (isAuthenticated && authManager.isAdmin() && this.isAdminPage()) {
            await adminManager.init();
        }
    }

    setupManagerCommunication() {
        // Authentication state changes
        authManager.onAuthChange((event, user) => {
            this.updateState('user', user);
            
            switch (event) {
                case 'login':
                    this.handleUserLogin(user);
                    break;
                case 'logout':
                    this.handleUserLogout();
                    break;
                case 'profileUpdate':
                    this.handleProfileUpdate(user);
                    break;
            }
        });

        // Cart state changes
        cartManager.onCartChange((event, data, cart) => {
            this.updateState('cart', cart);
            
            switch (event) {
                case 'itemAdded':
                    this.handleCartItemAdded(data);
                    break;
                case 'itemRemoved':
                    this.handleCartItemRemoved(data);
                    break;
                case 'cartCleared':
                    this.handleCartCleared();
                    break;
            }
        });

        // Order state changes
        orderManager.onOrderChange((event, order) => {
            switch (event) {
                case 'orderCreated':
                    this.handleOrderCreated(order);
                    break;
            }
        });

        // Contact form submissions
        contactManager.onContactChange((event, data) => {
            switch (event) {
                case 'contactSubmitted':
                    this.handleContactSubmitted(data);
                    break;
            }
        });
    }

    setupPageInitialization() {
        // Detect current page
        const path = window.location.pathname;
        let currentPage = 'home';
        
        if (path.includes('menu')) currentPage = 'menu';
        else if (path.includes('cart')) currentPage = 'cart';
        else if (path.includes('checkout')) currentPage = 'checkout';
        else if (path.includes('account')) currentPage = 'account';
        else if (path.includes('admin')) currentPage = 'admin';
        else if (path.includes('contact')) currentPage = 'contact';
        else if (path.includes('confirmation')) currentPage = 'confirmation';
        else if (path.includes('dashboard')) currentPage = 'dashboard';
        
        this.updateState('ui.currentPage', currentPage);
        
        // Page-specific initialization
        this.initializePage(currentPage);
    }

    async initializePage(page) {
        switch (page) {
            case 'menu':
                await this.initializeMenuPage();
                break;
            case 'cart':
                await this.initializeCartPage();
                break;
            case 'checkout':
                await this.initializeCheckoutPage();
                break;
            case 'admin':
                await this.initializeAdminPage();
                break;
            case 'contact':
                await this.initializeContactPage();
                break;
            case 'confirmation':
                await this.initializeConfirmationPage();
                break;
            case 'dashboard':
                await this.initializeDashboardPage();
                break;
        }
    }

    async initializeMenuPage() {
        // Menu manager is already initialized in initializeManagers
        // Update UI elements
        this.updateMenuUI();
    }

    async initializeCartPage() {
        // Update cart display
        cartManager.updateCartUI();
        
        // Setup checkout button
        const checkoutBtn = document.querySelector('.checkout-btn');
        if (checkoutBtn) {
            checkoutBtn.addEventListener('click', () => {
                if (cartManager.isEmpty()) {
                    Alert.show('Your cart is empty', 'warning');
                    return;
                }
                window.location.href = '/checkout.html';
            });
        }
    }

    async initializeCheckoutPage() {
        // Redirect if cart is empty
        if (cartManager.isEmpty()) {
            Alert.show('Your cart is empty. Redirecting to menu...', 'warning');
            setTimeout(() => {
                window.location.href = '/menu.html';
            }, 2000);
            return;
        }
        
        // Update checkout summary
        this.updateCheckoutSummary();
    }

    async initializeAdminPage() {
        // Check admin access
        if (!authManager.isAdmin()) {
            Alert.show('Access denied. Admin privileges required.', 'error');
            setTimeout(() => {
                window.location.href = '/';
            }, 2000);
            return;
        }
        
        // Admin manager is already initialized
    }

    async initializeContactPage() {
        // Contact manager is already initialized
        // Setup any additional contact page features
    }

    async initializeConfirmationPage() {
        // Order manager handles confirmation page initialization
    }

    async initializeDashboardPage() {
        // Check authentication
        if (!authManager.isAuthenticated()) {
            Alert.show('Please log in to access your dashboard.', 'warning');
            setTimeout(() => {
                window.location.href = '/account.html';
            }, 2000);
            return;
        }
        
        // Load user order history
        try {
            const orderHistory = await orderManager.getOrderHistory();
            this.updateState('orders', orderHistory.orders || []);
            this.updateOrderHistoryUI();
        } catch (error) {
            console.error('Failed to load order history:', error);
        }
    }

    setupGlobalErrorHandling() {
        // Handle global errors
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.addNotification('An unexpected error occurred', 'error');
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.addNotification('An unexpected error occurred', 'error');
        });
    }

    // Event handlers
    async handleUserLogin(user) {
        // Merge guest cart with user cart
        if (!cartManager.isEmpty()) {
            try {
                await cartManager.loadCart(); // This will merge guest cart
            } catch (error) {
                console.error('Failed to merge cart on login:', error);
            }
        }
        
        // Update UI
        this.updateAuthUI();
        
        // Redirect if on account page
        if (this.state.ui.currentPage === 'account') {
            setTimeout(() => {
                if (user.role === 'admin') {
                    window.location.href = '/admin.html';
                } else {
                    window.location.href = '/dashboard.html';
                }
            }, 1000);
        }
    }

    handleUserLogout() {
        // Clear sensitive state
        this.updateState('user', null);
        this.updateState('orders', []);
        this.updateState('admin', {
            dashboard: null,
            users: [],
            orders: [],
            analytics: null
        });
        
        // Update UI
        this.updateAuthUI();
        
        // Redirect if on protected page
        const protectedPages = ['admin', 'dashboard'];
        if (protectedPages.includes(this.state.ui.currentPage)) {
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        }
    }

    handleProfileUpdate(user) {
        this.updateAuthUI();
        this.addNotification('Profile updated successfully', 'success');
    }

    handleCartItemAdded(data) {
        this.addNotification('Item added to cart', 'success');
        this.updateCartUI();
    }

    handleCartItemRemoved(data) {
        this.updateCartUI();
    }

    handleCartCleared() {
        this.addNotification('Cart cleared', 'info');
        this.updateCartUI();
    }

    handleOrderCreated(order) {
        // Clear cart after successful order
        cartManager.clearCart();
        this.addNotification('Order placed successfully!', 'success');
    }

    handleContactSubmitted(data) {
        this.addNotification('Message sent successfully!', 'success');
    }

    // UI update methods
    updateAuthUI() {
        authManager.updateAuthUI();
    }

    updateCartUI() {
        cartManager.updateCartUI();
    }

    updateMenuUI() {
        // Update menu-specific UI elements
        const menuStats = document.querySelector('.menu-stats');
        if (menuStats) {
            const itemCount = this.state.menu.items.length;
            const categoryCount = this.state.menu.categories.length;
            menuStats.innerHTML = `
                <span>${categoryCount} Categories</span>
                <span>${itemCount} Items</span>
            `;
        }
    }

    updateCheckoutSummary() {
        const cart = this.state.cart;
        if (!cart) return;

        // Update order summary
        const summaryElements = document.querySelectorAll('.order-summary');
        summaryElements.forEach(element => {
            element.innerHTML = cartManager.renderCartSummary();
        });
    }

    updateOrderHistoryUI() {
        const historyContainer = document.querySelector('.order-history');
        if (!historyContainer) return;

        const orders = this.state.orders;
        if (orders.length === 0) {
            historyContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-receipt"></i>
                    <h3>No orders yet</h3>
                    <p>Your order history will appear here.</p>
                    <a href="/menu.html" class="btn btn-primary">Start Shopping</a>
                </div>
            `;
            return;
        }

        historyContainer.innerHTML = orders.map(order => `
            <div class="order-card">
                <div class="order-header">
                    <h4>Order #${order.orderNumber}</h4>
                    <span class="order-status status-${order.status}">${order.status}</span>
                </div>
                <div class="order-details">
                    <p>Date: ${new Date(order.createdAt).toLocaleDateString()}</p>
                    <p>Total: ${order.totalAmount}</p>
                    <p>Items: ${order.items?.length || 0}</p>
                </div>
                <div class="order-actions">
                    <a href="/track-order.html?orderNumber=${order.orderNumber}" class="btn btn-sm">Track Order</a>
                </div>
            </div>
        `).join('');
    }

    // State management methods
    updateState(path, value) {
        const keys = path.split('.');
        let current = this.state;
        
        for (let i = 0; i < keys.length - 1; i++) {
            if (!current[keys[i]]) {
                current[keys[i]] = {};
            }
            current = current[keys[i]];
        }
        
        current[keys[keys.length - 1]] = value;
        this.notifySubscribers('stateUpdate', { path, value });
    }

    getState(path = null) {
        if (!path) return this.state;
        
        const keys = path.split('.');
        let current = this.state;
        
        for (const key of keys) {
            if (current[key] === undefined) return undefined;
            current = current[key];
        }
        
        return current;
    }

    // Notification system
    addNotification(message, type = 'info', duration = 5000) {
        const notification = {
            id: Date.now(),
            message,
            type,
            timestamp: new Date()
        };
        
        this.state.ui.notifications.push(notification);
        this.notifySubscribers('notification', notification);
        
        // Auto-remove after duration
        setTimeout(() => {
            this.removeNotification(notification.id);
        }, duration);
    }

    removeNotification(id) {
        const index = this.state.ui.notifications.findIndex(n => n.id === id);
        if (index > -1) {
            this.state.ui.notifications.splice(index, 1);
            this.notifySubscribers('notificationRemoved', id);
        }
    }

    // Subscription management
    subscribe(callback) {
        this.subscribers.push(callback);
        
        // Return unsubscribe function
        return () => {
            const index = this.subscribers.indexOf(callback);
            if (index > -1) {
                this.subscribers.splice(index, 1);
            }
        };
    }

    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data, this.state);
            } catch (error) {
                console.error('Subscriber callback error:', error);
            }
        });
    }

    // Utility methods
    isMenuPage() {
        return window.location.pathname.includes('menu');
    }

    isAdminPage() {
        return window.location.pathname.includes('admin');
    }

    isAuthenticated() {
        return authManager.isAuthenticated();
    }

    isAdmin() {
        return authManager.isAdmin();
    }
}

// Create global state manager instance
export const stateManager = new StateManager();

// Make it available globally for debugging
window.stateManager = stateManager;

// Initialize on DOM content loaded
document.addEventListener('DOMContentLoaded', () => {
    stateManager.init();
});
